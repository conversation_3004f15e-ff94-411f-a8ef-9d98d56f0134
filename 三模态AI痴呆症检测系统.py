"""
🧠 三模态AI痴呆症检测系统
集成 MRI + CT + EEG 三种模态的智能诊断系统
版本: v3.0
作者: AI Assistant
"""

import os
import sys
import threading
import pickle
import json
from datetime import datetime
from tkinter import messagebox, filedialog
import customtkinter as ctk
import numpy as np
from PIL import Image, ImageTk
import tensorflow as tf

# 抑制TensorFlow警告
tf.get_logger().setLevel('ERROR')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

class TripleModalAIDetectionApp:
    """三模态AI痴呆症检测系统主类"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🧠 AI痴呆症识别器 - 三模态联用版 v3.0")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        
        # 设置窗口图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.mri_model = None           # MRI症状分析模型
        self.ct_detection_model = None  # CT图像检测模型
        self.eeg_classifier = None      # EEG分类器
        
        self.current_image_path = None
        self.current_eeg_data = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        
        # 模型路径
        self.mri_model_path = r"D:\模型开发\MRI_class.h5"
        self.ct_detection_path = r"D:\模型开发\picture_class.h5"
        self.eeg_classifier_path = "EEG_complete_classifier.pkl"
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # EEG类别标签
        self.eeg_class_labels = ['健康', '阿尔茨海默病', '额颞叶痴呆']
        
        # 创建界面
        self.create_widgets()
        self.load_models_async()
    
    def create_widgets(self):
        """创建主界面"""
        # 设置主题
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # 主框架
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧠 AI痴呆症智能检测系统",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 5))
        
        # 副标题
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="三模态联用版 - MRI影像 + CT检测 + EEG脑电分析",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 创建主要内容区域
        self.create_main_content()
        
        # 底部状态栏
        self.create_status_bar()
    
    def create_main_content(self):
        """创建主要内容区域"""
        # 内容框架
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=450)
        
        self.create_left_panel()
        self.create_right_panel()
    
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        # 图像显示标题
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 医学影像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 默认显示
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP\n\n三模态系统支持:\n• MRI影像症状分析\n• CT图像检测验证\n• EEG脑电数据分析",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
    
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 模型状态显示
        self.create_model_status_panel()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 结果显示区域
        self.create_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
    
    def create_model_status_panel(self):
        """创建模型状态面板"""
        status_frame = ctk.CTkFrame(self.right_panel)
        status_frame.pack(fill="x", padx=20, pady=10)
        
        # 标题
        status_title = ctk.CTkLabel(
            status_frame,
            text="🔧 三模态系统状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 10))
        
        # MRI模型状态
        self.mri_status = ctk.CTkLabel(
            status_frame,
            text="🔄 MRI症状分析模型加载中...",
            font=ctk.CTkFont(size=12)
        )
        self.mri_status.pack(pady=2)
        
        # CT检测模型状态
        self.ct_status = ctk.CTkLabel(
            status_frame,
            text="🔄 CT图像检测模型加载中...",
            font=ctk.CTkFont(size=12)
        )
        self.ct_status.pack(pady=2)
        
        # EEG模型状态
        self.eeg_status = ctk.CTkLabel(
            status_frame,
            text="🔄 EEG脑电分析模型加载中...",
            font=ctk.CTkFont(size=12)
        )
        self.eeg_status.pack(pady=2)
        
        # 总体状态
        self.overall_status = ctk.CTkLabel(
            status_frame,
            text="⏳ 系统初始化中...",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="orange"
        )
        self.overall_status.pack(pady=(10, 15))
    
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # 选择图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择影像文件",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 选择EEG数据按钮
        self.select_eeg_btn = ctk.CTkButton(
            control_frame,
            text="🧠 选择EEG数据",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_eeg_data,
            fg_color="purple"
        )
        self.select_eeg_btn.pack(fill="x", pady=10)
        
        # 开始三模态分析按钮
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🔍 开始三模态分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.start_triple_analysis,
            state="disabled",
            fg_color="green"
        )
        self.analyze_btn.pack(fill="x", pady=10)
    
    def create_results_panel(self):
        """创建结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 结果标题
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 三模态分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # 结果显示区域
        self.results_text = ctk.CTkTextbox(
            results_frame,
            height=200,
            font=ctk.CTkFont(size=12)
        )
        self.results_text.pack(fill="both", expand=True, padx=10, pady=(0, 15))
        
        # 初始提示
        self.results_text.insert("0.0", 
            "🔬 三模态AI分析系统\n\n"
            "📋 分析流程:\n"
            "1️⃣ MRI影像症状分析\n"
            "2️⃣ CT图像检测验证\n" 
            "3️⃣ EEG脑电信号分析\n"
            "4️⃣ 多模态融合诊断\n\n"
            "请选择影像文件和EEG数据开始分析..."
        )
        self.results_text.configure(state="disabled")
    
    def create_function_buttons(self):
        """创建功能按钮"""
        function_frame = ctk.CTkFrame(self.right_panel)
        function_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # 生成报告按钮
        self.report_btn = ctk.CTkButton(
            function_frame,
            text="📄 生成HTML报告",
            font=ctk.CTkFont(size=12),
            height=35,
            command=self.generate_html_report,
            state="disabled"
        )
        self.report_btn.pack(side="left", fill="x", expand=True, padx=(10, 5), pady=10)
        
        # 清除结果按钮
        self.clear_btn = ctk.CTkButton(
            function_frame,
            text="🗑️ 清除结果",
            font=ctk.CTkFont(size=12),
            height=35,
            command=self.clear_results,
            fg_color="gray"
        )
        self.clear_btn.pack(side="right", fill="x", expand=True, padx=(5, 10), pady=10)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ctk.CTkLabel(
            self.main_frame,
            text="🚀 三模态AI痴呆症检测系统已启动 | 请加载影像和EEG数据",
            font=ctk.CTkFont(size=12),
            anchor="w"
        )
        self.status_bar.pack(fill="x", padx=20, pady=(0, 10))
