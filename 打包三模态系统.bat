@echo off
chcp 65001 >nul
echo 🚀 三模态AI痴呆症识别系统打包工具
echo ================================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，请安装Python 3.8或更高版本
    goto :end
)

REM 检查PyInstaller
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo ❌ PyInstaller安装失败
        goto :end
    )
)

REM 检查必要的依赖
echo 📋 检查必要的依赖...
python -c "import tensorflow, customtkinter, PIL, mne" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装必要的依赖...
    pip install tensorflow customtkinter pillow opencv-python mne
)

REM 检查模型文件
set CT_MODEL=D:\模型开发\ct_class.h5
set CT_VERIFY_MODEL=D:\模型开发\ct_other_model.h5
set EEG_MODEL=models\eeg_model.h5

echo 🔍 检查模型文件...
if not exist "%CT_MODEL%" (
    echo ⚠️ 警告: 找不到CT分类模型: %CT_MODEL%
    echo    请确保模型文件存在或修改build_eeg_integration.py中的路径
)

if not exist "%CT_VERIFY_MODEL%" (
    echo ⚠️ 警告: 找不到CT验证模型: %CT_VERIFY_MODEL%
    echo    请确保模型文件存在或修改build_eeg_integration.py中的路径
)

if not exist "%EEG_MODEL%" (
    echo ⚠️ 警告: 找不到EEG模型: %EEG_MODEL%
    echo    请确保模型文件存在或修改build_eeg_integration.py中的路径
)

REM 运行打包脚本
echo.
echo 🚀 开始打包过程...
python build_eeg_integration.py --eeg_model "%EEG_MODEL%" --ct_model "%CT_MODEL%" --ct_verify_model "%CT_VERIFY_MODEL%" --name "三模态AI痴呆症识别器"

if %errorlevel% neq 0 (
    echo ❌ 打包过程出错
) else (
    echo ✅ 打包过程完成
)

:end
echo.
echo ================================================
pause