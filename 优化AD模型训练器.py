"""
🎯 优化AD模型训练器
专门解决准确率低的问题
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks
from imblearn.over_sampling import SMOTE
import warnings
warnings.filterwarnings('ignore')

class OptimizedADTrainer:
    """优化的AD训练器"""
    
    def __init__(self, data_path="D:/模型开发/AD和老龄化.csv"):
        self.data_path = data_path
        self.df = None
        self.model = None
        self.scaler = StandardScaler()
        
    def load_and_analyze_data(self):
        """加载并深度分析数据"""
        print("📂 加载数据进行深度分析...")
        self.df = pd.read_csv(self.data_path)
        
        print(f"✅ 数据形状: {self.df.shape}")
        
        # 🔍 寻找最有价值的目标变量
        print("\n🎯 分析潜在目标变量:")
        
        # 分析Question字段中的AD相关内容
        ad_questions = self.df[
            self.df['Question'].str.contains(
                'cognitive|memory|confusion|alzheimer|dementia|thinking', 
                case=False, na=False
            )
        ]
        
        if len(ad_questions) > 0:
            print(f"   发现认知相关问题: {len(ad_questions):,} 条")
            print(f"   相关问题:")
            for q in ad_questions['Question'].unique()[:3]:
                print(f"     • {q}")
        
        # 分析Class字段
        print(f"\n📊 Class分布:")
        class_counts = self.df['Class'].value_counts()
        for cls, count in class_counts.head(5).items():
            print(f"   {cls}: {count:,}")
        
        # 分析Data_Value分布
        if 'Data_Value' in self.df.columns:
            data_value_stats = self.df['Data_Value'].describe()
            print(f"\n📈 Data_Value统计:")
            print(f"   有效值: {self.df['Data_Value'].notna().sum():,}")
            print(f"   范围: {data_value_stats['min']:.2f} - {data_value_stats['max']:.2f}")
            print(f"   均值: {data_value_stats['mean']:.2f}")
        
        return self.df
    
    def create_meaningful_target(self):
        """创建有意义的目标变量"""
        print("\n🎯 创建有意义的目标变量...")
        
        # 策略1: 基于认知健康相关问题
        cognitive_keywords = [
            'cognitive', 'memory', 'confusion', 'thinking', 
            'concentration', 'alzheimer', 'dementia'
        ]
        
        cognitive_mask = pd.Series(False, index=self.df.index)
        for keyword in cognitive_keywords:
            cognitive_mask |= self.df['Question'].str.contains(keyword, case=False, na=False)
        
        cognitive_data = self.df[cognitive_mask].copy()
        
        if len(cognitive_data) > 1000:
            print(f"✅ 使用认知健康数据: {len(cognitive_data):,} 条")
            
            # 基于Data_Value创建二分类目标
            cognitive_data = cognitive_data.dropna(subset=['Data_Value'])
            
            if len(cognitive_data) > 500:
                # 使用中位数作为分界点
                median_value = cognitive_data['Data_Value'].median()
                cognitive_data['Target'] = (cognitive_data['Data_Value'] > median_value).astype(int)
                
                print(f"   目标分布: {cognitive_data['Target'].value_counts().to_dict()}")
                return cognitive_data, 'Target'
        
        # 策略2: 基于年龄分层
        age_data = self.df[self.df['StratificationCategory1'] == 'AGE'].copy()
        
        if len(age_data) > 1000:
            print(f"✅ 使用年龄分层数据: {len(age_data):,} 条")
            
            # 创建老年vs非老年分类
            def is_elderly(age_group):
                if pd.isna(age_group):
                    return None
                age_str = str(age_group).lower()
                return 1 if any(x in age_str for x in ['65+', '75+', '85+']) else 0
            
            age_data['Target'] = age_data['Stratification1'].apply(is_elderly)
            age_data = age_data.dropna(subset=['Target'])
            
            if len(age_data) > 500:
                print(f"   目标分布: {age_data['Target'].value_counts().to_dict()}")
                return age_data, 'Target'
        
        # 策略3: 基于主要健康分类
        print(f"✅ 使用健康分类数据")
        
        # 选择最常见的几个类别
        top_classes = self.df['Class'].value_counts().head(3).index.tolist()
        class_data = self.df[self.df['Class'].isin(top_classes)].copy()
        
        # 创建多分类目标
        le = LabelEncoder()
        class_data['Target'] = le.fit_transform(class_data['Class'])
        
        print(f"   使用类别: {top_classes}")
        print(f"   目标分布: {class_data['Target'].value_counts().to_dict()}")
        
        return class_data, 'Target'
    
    def smart_feature_engineering(self, df):
        """智能特征工程"""
        print("\n🔧 智能特征工程...")
        
        features_df = df.copy()
        
        # 1. 核心数值特征
        numeric_features = []
        
        if 'Data_Value' in features_df.columns:
            features_df['Data_Value_filled'] = features_df['Data_Value'].fillna(
                features_df['Data_Value'].median()
            )
            numeric_features.append('Data_Value_filled')
        
        if 'Low_Confidence_Limit' in features_df.columns:
            features_df['Low_Confidence_filled'] = features_df['Low_Confidence_Limit'].fillna(
                features_df['Low_Confidence_Limit'].median()
            )
            numeric_features.append('Low_Confidence_filled')
        
        if 'High_Confidence_Limit' in features_df.columns:
            features_df['High_Confidence_filled'] = features_df['High_Confidence_Limit'].fillna(
                features_df['High_Confidence_Limit'].median()
            )
            numeric_features.append('High_Confidence_filled')
        
        # 2. 重要分类特征编码
        categorical_features = []
        
        # 地理位置
        if 'LocationDesc' in features_df.columns:
            le_loc = LabelEncoder()
            features_df['Location_Code'] = le_loc.fit_transform(features_df['LocationDesc'])
            categorical_features.append('Location_Code')
        
        # 数据源
        if 'Datasource' in features_df.columns:
            le_ds = LabelEncoder()
            features_df['Datasource_Code'] = le_ds.fit_transform(features_df['Datasource'])
            categorical_features.append('Datasource_Code')
        
        # 年份
        if 'YearStart' in features_df.columns:
            features_df['Year_Normalized'] = (features_df['YearStart'] - features_df['YearStart'].min()) / (
                features_df['YearStart'].max() - features_df['YearStart'].min()
            )
            numeric_features.append('Year_Normalized')
        
        # 3. 组合特征
        if len(numeric_features) >= 2:
            features_df['Confidence_Range'] = (
                features_df.get('High_Confidence_filled', 0) - 
                features_df.get('Low_Confidence_filled', 0)
            )
            numeric_features.append('Confidence_Range')
        
        # 选择最终特征
        final_features = numeric_features + categorical_features
        available_features = [f for f in final_features if f in features_df.columns]
        
        print(f"✅ 选择特征: {len(available_features)} 个")
        print(f"   数值特征: {[f for f in available_features if f in numeric_features]}")
        print(f"   分类特征: {[f for f in available_features if f in categorical_features]}")
        
        return features_df, available_features
    
    def build_optimized_model(self, input_dim, n_classes):
        """构建优化的模型"""
        print(f"\n🏗️ 构建优化模型...")
        
        # 简化的网络架构
        model = models.Sequential([
            # 输入层
            layers.Dense(128, activation='relu', input_shape=(input_dim,)),
            layers.BatchNormalization(),
            layers.Dropout(0.2),  # 降低dropout
            
            # 隐藏层1
            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.2),
            
            # 隐藏层2
            layers.Dense(32, activation='relu'),
            layers.Dropout(0.1),
            
            # 输出层
            layers.Dense(n_classes, activation='softmax' if n_classes > 2 else 'sigmoid')
        ])
        
        # 优化编译参数
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy' if n_classes > 2 else 'binary_crossentropy',
            metrics=['accuracy']
        )
        
        print(f"✅ 模型参数: {model.count_params():,}")
        return model
    
    def train_with_class_balance(self, X, y, epochs=100):
        """使用类别平衡训练"""
        print(f"\n🚀 开始平衡训练...")
        
        # 检查类别分布
        unique_classes, class_counts = np.unique(y, return_counts=True)
        print(f"   类别分布: {dict(zip(unique_classes, class_counts))}")
        
        # 计算类别权重
        class_weights = compute_class_weight(
            'balanced', 
            classes=unique_classes, 
            y=y
        )
        class_weight_dict = dict(zip(unique_classes, class_weights))
        print(f"   类别权重: {class_weight_dict}")
        
        # 数据划分
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 数据标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 构建模型
        n_classes = len(unique_classes)
        self.model = self.build_optimized_model(X_train_scaled.shape[1], n_classes)
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                min_delta=0.001
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6
            )
        ]
        
        # 训练模型
        history = self.model.fit(
            X_train_scaled, y_train,
            validation_data=(X_val_scaled, y_val),
            epochs=epochs,
            batch_size=256,  # 增大batch size
            class_weight=class_weight_dict,  # 使用类别权重
            callbacks=callbacks_list,
            verbose=1
        )
        
        # 评估结果
        train_acc = self.model.evaluate(X_train_scaled, y_train, verbose=0)[1]
        val_acc = self.model.evaluate(X_val_scaled, y_val, verbose=0)[1]
        
        print(f"\n📊 训练结果:")
        print(f"   训练准确率: {train_acc:.4f} ({train_acc*100:.2f}%)")
        print(f"   验证准确率: {val_acc:.4f} ({val_acc*100:.2f}%)")
        print(f"   过拟合差距: {train_acc - val_acc:.4f}")
        
        if val_acc > 0.7:
            print("🎉 模型性能良好!")
        elif val_acc > 0.6:
            print("✅ 模型性能可接受")
        else:
            print("⚠️ 模型性能需要进一步优化")
        
        return history
    
    def run_optimized_pipeline(self):
        """运行优化流水线"""
        print("🎯 启动优化AD模型训练流水线")
        print("=" * 50)
        
        # 1. 数据分析
        df = self.load_and_analyze_data()
        
        # 2. 创建有意义的目标
        processed_data, target_col = self.create_meaningful_target()
        
        # 3. 智能特征工程
        featured_data, feature_cols = self.smart_feature_engineering(processed_data)
        
        # 4. 准备训练数据
        X = featured_data[feature_cols].values
        y = featured_data[target_col].values
        
        # 5. 训练优化模型
        history = self.train_with_class_balance(X, y)
        
        # 6. 保存模型
        self.model.save('optimized_AD_model.h5')
        
        print(f"\n🎉 优化训练完成!")
        print(f"📁 模型已保存: optimized_AD_model.h5")
        
        return self.model, history


def main():
    """主函数"""
    trainer = OptimizedADTrainer()
    model, history = trainer.run_optimized_pipeline()
    
    print("\n💡 如果准确率仍然不理想，建议:")
    print("1. 收集更专业的医学数据")
    print("2. 使用更具体的AD诊断标准")
    print("3. 增加领域专家特征工程")
    print("4. 尝试集成学习方法")


if __name__ == "__main__":
    main()
