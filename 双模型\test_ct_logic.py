# -*- coding: utf-8 -*-
"""
测试修正后的CT检测逻辑
"""

import os
import warnings
warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

def test_ct_detection_logic():
    """测试CT检测逻辑"""
    print("🧪 测试修正后的CT检测逻辑")
    print("=" * 40)
    
    try:
        import tensorflow as tf
        from tensorflow.keras.preprocessing import image
        import numpy as np
        
        tf.get_logger().setLevel('ERROR')
        
        # 加载CT检测模型
        ct_model_path = r"D:\模型开发\ct_other_model.h5"
        if not os.path.exists(ct_model_path):
            print(f"❌ 模型文件不存在: {ct_model_path}")
            return
        
        model = tf.keras.models.load_model(ct_model_path)
        print("✅ CT检测模型加载成功")
        
        # 修正后的标签 (类别0=CT图像, 类别1=非CT图像)
        ct_labels = ['CT图像', '非CT图像']
        
        # 模拟不同的预测结果
        test_cases = [
            # [类别0概率, 类别1概率] - 模拟预测结果
            [0.9, 0.1],    # 强烈预测为CT图像
            [0.7, 0.3],    # 较强预测为CT图像
            [0.3, 0.7],    # 较强预测为非CT图像
            [0.1, 0.9],    # 强烈预测为非CT图像
            [0.5, 0.5],    # 不确定
        ]
        
        print(f"\n🔍 测试不同预测结果的逻辑:")
        print("类别0=CT图像, 类别1=非CT图像")
        print("-" * 40)
        
        for i, probs in enumerate(test_cases):
            predictions = np.array([probs])
            predicted_class = np.argmax(predictions, axis=1)[0]
            confidence = np.max(predictions, axis=1)[0]
            
            # 修正后的逻辑
            is_ct = predicted_class == 0  # 类别0表示CT图像
            class_name = ct_labels[predicted_class]
            
            print(f"\n测试 {i+1}:")
            print(f"  预测概率: [CT:{probs[0]:.1f}, 非CT:{probs[1]:.1f}]")
            print(f"  预测类别: {predicted_class}")
            print(f"  类别名称: {class_name}")
            print(f"  是否CT图像: {is_ct}")
            print(f"  置信度: {confidence:.1f}")
            
            if not is_ct:
                print(f"  ⚠️ 会显示警告: 该图像可能不是CT扫描图像")
            else:
                print(f"  ✅ 正常分析: 这是CT图像")
        
        print(f"\n" + "=" * 40)
        print("✅ 逻辑测试完成")
        print("现在CT检测应该基于你的模型正确工作了！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_ct_detection_logic()
