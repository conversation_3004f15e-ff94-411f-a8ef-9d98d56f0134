"""
🧠 AD和老龄化数据深度学习处理器
基于284,142条记录的大规模健康数据进行深度学习建模
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow.keras import layers, models, callbacks
import warnings
warnings.filterwarnings('ignore')

class ADAgingDeepLearner:
    """AD和老龄化深度学习处理器"""
    
    def __init__(self, data_path="D:/模型开发/AD和老龄化.csv"):
        self.data_path = data_path
        self.df = None
        self.processed_data = None
        self.model = None
        self.encoders = {}
        self.scaler = StandardScaler()
        
        print("🧠 AD和老龄化深度学习处理器初始化")
        
    def load_and_explore_data(self):
        """加载并探索数据"""
        print("📂 加载数据...")
        self.df = pd.read_csv(self.data_path)
        
        print(f"✅ 数据加载完成: {self.df.shape}")
        
        # 数据探索
        print("\n🔍 数据探索:")
        print(f"   时间范围: {self.df['YearStart'].min()}-{self.df['YearEnd'].max()}")
        print(f"   地理覆盖: {self.df['LocationDesc'].nunique()} 个地区")
        print(f"   主题类别: {self.df['Class'].nunique()} 个")
        print(f"   具体问题: {self.df['Question'].nunique()} 个")
        
        # 关键字段分析
        print(f"\n📊 关键字段分析:")
        print(f"   Class分布:")
        for cls in self.df['Class'].value_counts().head(5).items():
            print(f"     {cls[0]}: {cls[1]:,} 条")
            
        print(f"\n   Topic分布:")
        for topic in self.df['Topic'].value_counts().head(5).items():
            print(f"     {topic[0]}: {topic[1]:,} 条")
        
        return self.df
    
    def identify_ad_related_data(self):
        """识别AD相关数据"""
        print("\n🎯 识别AD相关数据...")
        
        # AD相关关键词
        ad_keywords = [
            'alzheimer', 'dementia', 'cognitive', 'memory', 'brain',
            'mental', 'confusion', 'thinking', 'concentration'
        ]
        
        # 在Question和Topic中搜索AD相关内容
        ad_mask = pd.Series(False, index=self.df.index)
        
        for keyword in ad_keywords:
            ad_mask |= self.df['Question'].str.contains(keyword, case=False, na=False)
            ad_mask |= self.df['Topic'].str.contains(keyword, case=False, na=False)
            ad_mask |= self.df['Class'].str.contains(keyword, case=False, na=False)
        
        ad_data = self.df[ad_mask].copy()
        
        print(f"✅ 发现AD相关数据: {len(ad_data):,} 条 ({len(ad_data)/len(self.df)*100:.1f}%)")
        
        if len(ad_data) > 0:
            print(f"   相关问题示例:")
            for q in ad_data['Question'].unique()[:3]:
                print(f"     • {q}")
        
        return ad_data
    
    def create_target_variable(self, df):
        """创建目标变量"""
        print("\n🎯 创建目标变量...")
        
        # 方案1: 基于Data_Value创建风险等级
        if 'Data_Value' in df.columns:
            # 移除缺失值
            df_clean = df.dropna(subset=['Data_Value']).copy()
            
            if len(df_clean) > 0:
                # 根据数值分布创建风险等级
                percentiles = df_clean['Data_Value'].quantile([0.33, 0.67])
                
                def categorize_risk(value):
                    if value <= percentiles[0.33]:
                        return 0  # 低风险
                    elif value <= percentiles[0.67]:
                        return 1  # 中风险
                    else:
                        return 2  # 高风险
                
                df_clean['Risk_Level'] = df_clean['Data_Value'].apply(categorize_risk)
                
                print(f"✅ 基于Data_Value创建风险等级:")
                risk_counts = df_clean['Risk_Level'].value_counts().sort_index()
                for level, count in risk_counts.items():
                    risk_name = ['低风险', '中风险', '高风险'][level]
                    print(f"   {risk_name}: {count:,} 条 ({count/len(df_clean)*100:.1f}%)")
                
                return df_clean
        
        # 方案2: 基于年龄分层创建目标
        if 'Stratification1' in df.columns:
            age_data = df[df['StratificationCategory1'] == 'AGE'].copy()
            
            if len(age_data) > 0:
                # 创建年龄组目标变量
                def categorize_age_risk(age_group):
                    if pd.isna(age_group):
                        return None
                    age_str = str(age_group).lower()
                    if any(x in age_str for x in ['65+', '75+', '85+']):
                        return 2  # 高风险年龄组
                    elif any(x in age_str for x in ['55-64', '65-74']):
                        return 1  # 中风险年龄组
                    else:
                        return 0  # 低风险年龄组
                
                age_data['Age_Risk'] = age_data['Stratification1'].apply(categorize_age_risk)
                age_data = age_data.dropna(subset=['Age_Risk'])
                
                print(f"✅ 基于年龄分层创建目标变量:")
                age_risk_counts = age_data['Age_Risk'].value_counts().sort_index()
                for level, count in age_risk_counts.items():
                    risk_name = ['低风险年龄', '中风险年龄', '高风险年龄'][int(level)]
                    print(f"   {risk_name}: {count:,} 条")
                
                return age_data
        
        print("⚠️ 无法创建明确的目标变量，使用原始分类")
        return df
    
    def feature_engineering(self, df):
        """特征工程"""
        print("\n🔧 特征工程...")
        
        features_df = df.copy()
        
        # 1. 时间特征
        features_df['Year_Span'] = features_df['YearEnd'] - features_df['YearStart']
        
        # 2. 地理特征编码
        le_location = LabelEncoder()
        features_df['Location_Encoded'] = le_location.fit_transform(features_df['LocationDesc'])
        self.encoders['location'] = le_location
        
        # 3. 分类特征编码
        categorical_cols = ['Class', 'Topic', 'Datasource', 'Data_Value_Type']
        
        for col in categorical_cols:
            if col in features_df.columns:
                le = LabelEncoder()
                features_df[f'{col}_Encoded'] = le.fit_transform(features_df[col].astype(str))
                self.encoders[col] = le
        
        # 4. 分层特征处理
        if 'Stratification1' in features_df.columns:
            le_strat1 = LabelEncoder()
            features_df['Stratification1_Encoded'] = le_strat1.fit_transform(
                features_df['Stratification1'].astype(str)
            )
            self.encoders['stratification1'] = le_strat1
        
        # 5. 数值特征标准化
        numeric_cols = ['Data_Value', 'Low_Confidence_Limit', 'High_Confidence_Limit']
        numeric_cols = [col for col in numeric_cols if col in features_df.columns]
        
        if numeric_cols:
            # 填充缺失值
            for col in numeric_cols:
                features_df[col] = features_df[col].fillna(features_df[col].median())
        
        # 6. 创建组合特征
        if 'Data_Value' in features_df.columns and 'Low_Confidence_Limit' in features_df.columns:
            features_df['Confidence_Range'] = (
                features_df['High_Confidence_Limit'] - features_df['Low_Confidence_Limit']
            )
        
        print(f"✅ 特征工程完成，特征数: {features_df.shape[1]}")
        
        return features_df
    
    def prepare_deep_learning_data(self, df, target_col):
        """准备深度学习数据"""
        print(f"\n📊 准备深度学习数据...")
        
        # 选择特征列
        feature_cols = [
            'YearStart', 'Location_Encoded', 'Class_Encoded', 'Topic_Encoded',
            'Data_Value', 'Low_Confidence_Limit', 'High_Confidence_Limit',
            'Confidence_Range', 'Year_Span'
        ]
        
        # 添加可用的编码特征
        for col in df.columns:
            if col.endswith('_Encoded') and col not in feature_cols:
                feature_cols.append(col)
        
        # 过滤存在的列
        available_cols = [col for col in feature_cols if col in df.columns]
        
        # 准备特征矩阵
        X = df[available_cols].copy()
        
        # 处理缺失值
        X = X.fillna(X.median())
        
        # 准备目标变量
        y = df[target_col].copy()
        
        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)
        
        print(f"✅ 数据准备完成:")
        print(f"   特征维度: {X_scaled.shape}")
        print(f"   目标分布: {y.value_counts().to_dict()}")
        
        return X_scaled, y, available_cols
    
    def build_deep_model(self, input_dim, n_classes):
        """构建深度学习模型"""
        print(f"\n🏗️ 构建深度学习模型...")
        
        model = models.Sequential([
            # 输入层
            layers.Dense(512, activation='relu', input_shape=(input_dim,)),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            # 隐藏层1
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            # 隐藏层2
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.4),
            
            # 隐藏层3
            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.4),
            
            # 输出层
            layers.Dense(n_classes, activation='softmax')
        ])
        
        # 编译模型
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print(f"✅ 模型构建完成:")
        print(f"   参数量: {model.count_params():,}")
        
        return model
    
    def train_model(self, X, y, validation_split=0.2, epochs=50):
        """训练模型"""
        print(f"\n🚀 开始训练模型...")
        
        # 划分数据
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=validation_split, random_state=42, stratify=y
        )
        
        # 构建模型
        n_classes = len(np.unique(y))
        self.model = self.build_deep_model(X.shape[1], n_classes)
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-6
            )
        ]
        
        # 训练模型
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=128,
            callbacks=callbacks_list,
            verbose=1
        )
        
        # 评估模型
        train_acc = self.model.evaluate(X_train, y_train, verbose=0)[1]
        val_acc = self.model.evaluate(X_val, y_val, verbose=0)[1]
        
        print(f"\n📊 训练完成:")
        print(f"   训练准确率: {train_acc:.4f}")
        print(f"   验证准确率: {val_acc:.4f}")
        print(f"   过拟合差距: {train_acc - val_acc:.4f}")
        
        return history
    
    def run_complete_pipeline(self):
        """运行完整的深度学习流水线"""
        print("🚀 启动AD和老龄化深度学习流水线")
        print("=" * 60)
        
        # 1. 加载数据
        df = self.load_and_explore_data()
        
        # 2. 识别AD相关数据
        ad_data = self.identify_ad_related_data()
        
        # 3. 选择处理数据
        if len(ad_data) > 1000:
            working_data = ad_data
            print(f"✅ 使用AD相关数据: {len(working_data):,} 条")
        else:
            # 如果AD数据太少，使用全部数据的样本
            working_data = df.sample(n=min(50000, len(df)), random_state=42)
            print(f"✅ 使用随机样本数据: {len(working_data):,} 条")
        
        # 4. 创建目标变量
        processed_data = self.create_target_variable(working_data)
        
        # 5. 特征工程
        featured_data = self.feature_engineering(processed_data)
        
        # 6. 确定目标列
        target_cols = [col for col in featured_data.columns 
                      if col in ['Risk_Level', 'Age_Risk']]
        
        if not target_cols:
            # 创建基于Class的目标变量
            le_target = LabelEncoder()
            featured_data['Target'] = le_target.fit_transform(featured_data['Class'])
            target_col = 'Target'
            print(f"✅ 使用Class作为目标变量: {featured_data['Target'].nunique()} 个类别")
        else:
            target_col = target_cols[0]
        
        # 7. 准备深度学习数据
        X, y, feature_names = self.prepare_deep_learning_data(featured_data, target_col)
        
        # 8. 训练模型
        history = self.train_model(X, y)
        
        # 9. 保存模型
        self.model.save('AD_aging_deep_model.h5')
        
        print(f"\n🎉 深度学习流水线完成!")
        print(f"📁 模型已保存: AD_aging_deep_model.h5")
        
        return self.model, history


def main():
    """主函数"""
    # 创建深度学习处理器
    processor = ADAgingDeepLearner()
    
    # 运行完整流水线
    model, history = processor.run_complete_pipeline()
    
    print("\n💡 后续建议:")
    print("1. 使用更多AD特异性特征")
    print("2. 尝试不同的模型架构")
    print("3. 进行超参数优化")
    print("4. 添加时间序列分析")


if __name__ == "__main__":
    main()
