# -*- coding: utf-8 -*-
"""
AI痴呆症识别器 - 工作版本
包含HTML报告功能，确保能正常启动
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")


class AIDetectionApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("AI痴呆症识别器 v1.0 - 双模型版(MRI)")
        self.root.geometry("1200x700")  # 增加窗口宽度
        self.root.resizable(True, True)

        # 初始化变量
        self.model = None  # 痴呆症分类模型
        self.mri_model = None  # MRI图像检测模型
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False

        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)',
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]

        # MRI检测标签 (类别0=MRI图像, 类别1=非MRI图像)
        self.mri_labels = ['MRI图像', '非MRI图像']

        # 创建界面
        self.create_widgets()
        self.load_models_async()

    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🧠 AI痴呆症识别器 - 工作版",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))

        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=400)  # 增加宽度

        self.create_left_panel()
        self.create_right_panel()
        self.create_status_bar()

    def create_left_panel(self):
        """创建左侧图像显示面板"""
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 医学影像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))

        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)

    def create_right_panel(self):
        """创建右侧控制面板"""
        # 控制按钮区域 - 缩小
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=10, pady=10)  # 减少边距

        # 选择图像按钮 - 缩小
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择影像文件",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=3)  # 减少间距

        # 开始分析按钮 - 缩小
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🔍 开始AI分析",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.start_analysis,
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", pady=3)  # 减少间距

        # 摄像头控制按钮 - 缩小
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=3)  # 减少间距

        # 拍照分析按钮 - 缩小
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="📸 拍照分析",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=3)  # 减少间距

        # 屏幕截图按钮 - 缩小
        self.screenshot_btn = ctk.CTkButton(
            control_frame,
            text="🖥️ 屏幕截图分析",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.capture_screenshot,
            fg_color="orange"
        )
        self.screenshot_btn.pack(fill="x", pady=3)  # 减少间距





        # HTML报告按钮 - 缩小
        self.html_btn = ctk.CTkButton(
            control_frame,
            text="🌐 生成HTML报告",
            font=ctk.CTkFont(size=12, weight="bold"),  # 减小字体
            height=32,  # 减小高度
            command=self.generate_html_report,
            state="disabled",
            fg_color="orange"
        )
        self.html_btn.pack(fill="x", pady=3)  # 减少间距

        # 进度条 - 缩小
        self.progress = ctk.CTkProgressBar(control_frame, height=15)  # 减小高度
        self.progress.pack(fill="x", pady=5)  # 减少间距
        self.progress.set(0)

        # 结果显示区域
        self.create_results_panel()

        # 功能按钮
        self.create_function_buttons()

    def create_results_panel(self):
        """创建结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)  # 减少左右边距

        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))

        self.result_label = ctk.CTkLabel(
            results_frame,
            text="等待分析...",
            font=ctk.CTkFont(size=14),
            wraplength=360,  # 增加文字换行宽度
            justify="center"
        )
        self.result_label.pack(pady=10)

        self.confidence_label = ctk.CTkLabel(
            results_frame,
            text="",
            font=ctk.CTkFont(size=12),
            wraplength=360,  # 添加换行宽度
            justify="center"  # 居中对齐
        )
        self.confidence_label.pack(pady=5)

        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=300)  # 大幅增加分析结果框高度
        self.details_frame.pack(fill="both", expand=True, padx=2, pady=5)  # 让分析结果框占据更多空间

    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=10, pady=5)  # 减少边距

        # 保存结果按钮 - 缩小
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled",
            height=30  # 减小高度
        )
        self.save_btn.pack(fill="x", pady=2)  # 减少间距

        # 生成PDF报告按钮 - 缩小
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="green",
            height=30  # 减小高度
        )
        self.pdf_btn.pack(fill="x", pady=2)  # 减少间距

        # 查看历史按钮 - 缩小
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history,
            height=30  # 减小高度
        )
        self.history_btn.pack(fill="x", pady=2)  # 减少间距

        # 关于按钮 - 缩小
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about,
            height=30  # 减小高度
        )
        self.about_btn.pack(fill="x", pady=2)  # 减少间距

    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载AI模型...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)

        self.model_status = ctk.CTkLabel(
            self.status_frame,
            text="⏳ 加载中",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        self.model_status.pack(side="right", padx=20, pady=10)

        # MRI模型状态
        self.mri_model_status = ctk.CTkLabel(
            self.status_frame,
            text="⏳ MRI模型加载中",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        self.mri_model_status.pack(side="right", padx=(0, 20), pady=10)

    def load_models_async(self):
        """异步加载双模型"""

        def load_models():
            try:
                import warnings
                warnings.filterwarnings('ignore')

                import tensorflow as tf
                from tensorflow.keras.preprocessing import image
                import numpy as np

                tf.get_logger().setLevel('ERROR')

                try:
                    import absl.logging
                    absl.logging.set_verbosity(absl.logging.ERROR)
                except:
                    pass

                # 加载痴呆症分类模型
                dementia_model_path = r"D:\模型开发\MRI_class.h5"
                dementia_loaded = False
                if os.path.exists(dementia_model_path):
                    self.model = tf.keras.models.load_model(dementia_model_path)
                    dementia_loaded = True
                    self.root.after(0, self.on_dementia_model_loaded, True)
                else:
                    self.root.after(0, self.on_dementia_model_loaded, False)

                # 加载MRI图像检测模型
                mri_model_path = r"D:\模型开发\picture_class.h5"
                mri_loaded = False
                if os.path.exists(mri_model_path):
                    self.mri_model = tf.keras.models.load_model(mri_model_path)
                    mri_loaded = True
                    self.root.after(0, self.on_mri_model_loaded, True)
                else:
                    self.root.after(0, self.on_mri_model_loaded, False)

                # 保存TensorFlow相关模块
                if dementia_loaded or mri_loaded:
                    self.tf = tf
                    self.image_module = image
                    self.np = np

            except Exception as e:
                self.root.after(0, self.on_model_error, str(e))

        threading.Thread(target=load_models, daemon=True).start()

    def on_dementia_model_loaded(self, success):
        """痴呆症模型加载完成回调"""
        if success:
            self.model_status.configure(text="🟢 痴呆症模型已加载", text_color="green")
            self.update_overall_status()
        else:
            self.model_status.configure(text="� 痴呆症模型未找到", text_color="red")
            messagebox.showerror("错误", "未找到痴呆症模型文件：D:\\模型开发\\MRI_class.h5")

    def on_mri_model_loaded(self, success):
        """MRI模型加载完成回调"""
        if success:
            self.mri_model_status.configure(text="🟢 MRI检测模型已加载", text_color="green")
            self.update_overall_status()
        else:
            self.mri_model_status.configure(text="🔴 MRI模型未找到", text_color="red")
            messagebox.showwarning("警告", "未找到MRI检测模型文件：D:\\模型开发\\picture_class.h5\n将跳过MRI图像验证功能")

    def update_overall_status(self):
        """更新整体状态"""
        if self.model and self.mri_model:
            self.status_label.configure(text="✅ 双模型系统已就绪 (痴呆症分析 + MRI检测)")
        elif self.model:
            self.status_label.configure(text="⚠️ 痴呆症模型已就绪 (缺少MRI检测)")
        elif self.mri_model:
            self.status_label.configure(text="⚠️ MRI检测模型已就绪 (缺少痴呆症分析)")
        else:
            self.status_label.configure(text="❌ 模型未加载")

    def on_model_error(self, error):
        """模型加载错误回调"""
        self.status_label.configure(text="❌ 模型加载失败")
        self.model_status.configure(text="🔴 错误", text_color="red")
        self.mri_model_status.configure(text="🔴 错误", text_color="red")
        messagebox.showerror("错误", f"模型加载失败：{error}")

    def select_image(self):
        """选择图像文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择医学影像文件",
            filetypes=file_types
        )

        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"📁 已选择: {os.path.basename(file_path)}")

    def display_image(self, image_path):
        """显示选择的图像"""
        try:
            pil_image = Image.open(image_path)
            display_size = (400, 400)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(pil_image)
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图像：{e}")

    def start_analysis(self):
        """开始AI分析"""
        if not self.model:
            messagebox.showerror("错误", "痴呆症分析模型未加载")
            return

        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择图像文件")
            return

        self.analyze_btn.configure(state="disabled", text="🔄 分析中...")
        self.progress.set(0)
        threading.Thread(target=self.perform_analysis, daemon=True).start()

    def perform_analysis(self):
        """执行AI分析 - 包含MRI检测和痴呆症分析"""
        try:
            self.root.after(0, lambda: self.progress.set(0.1))

            # 第一步：MRI图像检测（如果MRI模型可用）
            mri_result = None
            if self.mri_model:
                mri_result = self.detect_mri_image()
                self.root.after(0, lambda: self.progress.set(0.3))
            else:
                self.root.after(0, lambda: self.progress.set(0.3))

            # 第二步：加载和预处理图像用于痴呆症分析
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            self.root.after(0, lambda: self.progress.set(0.6))

            # 第三步：进行痴呆症预测
            predictions = self.model.predict(img_array, verbose=0)
            predicted_class = self.np.argmax(predictions, axis=1)
            prediction_probs = predictions[0].tolist()

            self.root.after(0, lambda: self.progress.set(0.9))

            # 准备结果
            result_data = {
                'predicted_class': predicted_class[0],
                'predicted_class_name': self.class_labels[predicted_class[0]],
                'confidence': max(prediction_probs),
                'probabilities': prediction_probs,
                'image_path': self.current_image_path,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'mri_detection': mri_result  # 添加MRI检测结果
            }

            self.root.after(0, lambda: self.progress.set(1.0))
            self.root.after(0, self.display_results, result_data)

        except Exception as e:
            self.root.after(0, self.on_analysis_error, str(e))

    def detect_mri_image(self):
        """检测图像是否为MRI图像"""
        try:
            # 加载和预处理图像用于MRI检测
            # 注意：这里的target_size可能需要根据你的MRI模型要求调整
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            # 进行MRI检测预测
            mri_predictions = self.mri_model.predict(img_array, verbose=0)
            mri_predicted_class = self.np.argmax(mri_predictions, axis=1)
            mri_confidence = max(mri_predictions[0])

            return {
                'is_mri': mri_predicted_class[0] == 0,  # 类别0表示MRI图像
                'confidence': mri_confidence,
                'class_name': self.mri_labels[mri_predicted_class[0]],
                'probabilities': mri_predictions[0].tolist()
            }

        except Exception as e:
            print(f"MRI检测失败: {e}")
            return None

    def display_results(self, result_data):
        """显示分析结果"""
        self.results_history.append(result_data)

        class_name = result_data['predicted_class_name']
        confidence = result_data['confidence']
        mri_result = result_data.get('mri_detection')

        # 检查MRI检测结果并显示警告
        if mri_result:
            if not mri_result['is_mri']:
                # 显示警告对话框
                warning_msg = f"⚠️ 图像验证警告\n\n"
                warning_msg += f"MRI检测结果: {mri_result['class_name']}\n"
                warning_msg += f"检测置信度: {mri_result['confidence']:.2%}\n\n"
                warning_msg += "该图像可能不是MRI扫描图像。\n"
                warning_msg += "分析结果可能不准确，建议使用MRI图像进行分析。\n\n"
                warning_msg += "是否继续查看分析结果？"

                if not messagebox.askyesno("图像验证警告", warning_msg):
                    # 用户选择不继续，重置状态
                    self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
                    self.progress.set(0)
                    self.status_label.configure(text="⚠️ 分析已取消")
                    return

        # 显示痴呆症分析结果
        result_text = f"🎯 痴呆症分析结果:\n{class_name}"
        self.result_label.configure(text=result_text, text_color="white")
        confidence_text = f"置信度: {confidence:.2%}"  # 简化置信度文本
        self.confidence_label.configure(text=confidence_text, text_color="lightblue")

        # 清空详细结果框
        for widget in self.details_frame.winfo_children():
            widget.destroy()

        # 显示MRI检测结果（如果有）
        if mri_result:
            mri_frame = ctk.CTkFrame(self.details_frame)
            mri_frame.pack(fill="x", pady=5)

            mri_color = "green" if mri_result['is_mri'] else "orange"
            mri_icon = "✅" if mri_result['is_mri'] else "⚠️"

            mri_label = ctk.CTkLabel(
                mri_frame,
                text=f"{mri_icon} MRI检测: {mri_result['class_name']} ({mri_result['confidence']:.2%})",
                font=ctk.CTkFont(size=11, weight="bold"),
                text_color=mri_color,
                wraplength=350  # 添加文字换行
            )
            mri_label.pack(padx=5, pady=5)  # 减少边距

        # 显示痴呆症分析详细概率
        dementia_title = ctk.CTkLabel(
            self.details_frame,
            text="📊 痴呆症分析详情:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        dementia_title.pack(pady=(10, 5))

        for i, prob in enumerate(result_data['probabilities']):
            prob_frame = ctk.CTkFrame(self.details_frame)
            prob_frame.pack(fill="x", pady=2)

            label_text = self.class_labels[i].split('(')[0]
            prob_label = ctk.CTkLabel(
                prob_frame,
                text=f"{label_text}: {prob:.2%}",
                font=ctk.CTkFont(size=11),
                wraplength=200  # 添加文字换行
            )
            prob_label.pack(side="left", padx=5, pady=5)  # 减少边距

            prob_bar = ctk.CTkProgressBar(prob_frame, width=120, height=12)  # 增加进度条尺寸
            prob_bar.pack(side="right", padx=5, pady=5)  # 减少边距
            prob_bar.set(prob)

        # 重置按钮状态
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.save_btn.configure(state="normal")
        self.pdf_btn.configure(state="normal")
        self.html_btn.configure(state="normal")
        self.progress.set(0)

        # 根据MRI检测结果设置状态
        if mri_result and not mri_result['is_mri']:
            self.status_label.configure(text="⚠️ 分析完成 (非MRI图像警告)")
        else:
            self.status_label.configure(text="✅ 分析完成")

    def on_analysis_error(self, error):
        """分析错误处理"""
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.progress.set(0)
        self.status_label.configure(text="❌ 分析失败")
        messagebox.showerror("分析错误", f"AI分析失败：{error}")

    def save_results(self):
        """保存分析结果"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results_history, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"结果已保存到：{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败：{e}")

    def generate_pdf_report(self):
        """生成PDF报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        latest_result = self.results_history[-1]
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        file_path = filedialog.asksaveasfilename(
            title="保存PDF报告",
            defaultextension=".pdf",
            initialfile=default_name,
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_pdf_report(latest_result, file_path)
                messagebox.showinfo("成功", f"PDF报告已生成：{file_path}")
                if messagebox.askyesno("打开报告", "是否立即打开PDF报告？"):
                    os.startfile(file_path)
            except Exception as e:
                # PDF失败，创建文本报告
                text_path = file_path.replace('.pdf', '.txt')
                self.create_text_report(latest_result, text_path)
                messagebox.showinfo("成功", f"文本报告已生成：{text_path}")
                if messagebox.askyesno("打开报告", "是否立即打开文本报告？"):
                    os.startfile(text_path)

    def generate_html_report(self):
        """生成HTML报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        latest_result = self.results_history[-1]
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        file_path = filedialog.asksaveasfilename(
            title="保存HTML报告",
            defaultextension=".html",
            initialfile=default_name,
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_html_report(latest_result, file_path)
                messagebox.showinfo("成功", f"HTML报告已生成：{file_path}")
                if messagebox.askyesno("打开报告", "是否在浏览器中打开HTML报告？"):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"HTML报告生成失败：{e}")

    def create_html_report(self, result_data, file_path):
        """创建包含原始图像的HTML报告"""
        try:
            import base64

            # 读取原始图像并转换为base64
            image_section = ""

            try:
                with open(result_data['image_path'], 'rb') as img_file:
                    image_data = img_file.read()
                    image_base64 = base64.b64encode(image_data).decode('utf-8')

                    # 根据文件扩展名确定MIME类型
                    image_ext = os.path.splitext(result_data['image_path'])[1].lower()
                    if image_ext in ['.jpg', '.jpeg']:
                        image_mime = 'image/jpeg'
                    elif image_ext == '.png':
                        image_mime = 'image/png'
                    elif image_ext == '.bmp':
                        image_mime = 'image/bmp'
                    else:
                        image_mime = 'image/jpeg'

                    image_section = f'''
                    <div style="text-align: center; margin: 20px 0; padding: 20px; background: #f0f8ff; border-radius: 10px;">
                        <h2>📷 原始医学影像</h2>
                        <img src="data:{image_mime};base64,{image_base64}" alt="分析图像" style="max-width: 400px; max-height: 400px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        <p style="margin-top: 10px; color: #666;">上图为本次分析的原始医学影像</p>
                    </div>
                    '''
            except Exception as e:
                print(f"无法读取图像文件: {e}")
                image_section = '''
                <div style="text-align: center; margin: 20px 0; padding: 20px; background: #ffe6e6; border-radius: 10px;">
                    <h2>📷 原始医学影像</h2>
                    <p style="color: #e74c3c;">⚠️ 无法显示原始图像</p>
                </div>
                '''

            # 创建MRI检测结果部分
            mri_section = ""
            mri_result = result_data.get('mri_detection')
            if mri_result:
                mri_color = "#27ae60" if mri_result['is_mri'] else "#e67e22"
                mri_icon = "✅" if mri_result['is_mri'] else "⚠️"
                mri_bg_color = "#d5f4e6" if mri_result['is_mri'] else "#fdeaa7"

                mri_section = f'''
                <div style="background: {mri_bg_color}; padding: 20px; margin: 20px 0; border-radius: 10px; border-left: 4px solid {mri_color};">
                    <h2>{mri_icon} MRI图像检测结果</h2>
                    <p><strong>检测结果:</strong> <span style="color: {mri_color}; font-weight: bold;">{mri_result['class_name']}</span></p>
                    <p><strong>检测置信度:</strong> {mri_result['confidence']:.2%}</p>
                    {'<p style="color: #e67e22;"><strong>⚠️ 警告:</strong> 该图像可能不是MRI扫描图像，分析结果可能不准确。</p>' if not mri_result['is_mri'] else '<p style="color: #27ae60;">✅ 图像验证通过，这是一张MRI扫描图像。</p>'}
                </div>
                '''

            # 创建痴呆症分析概率分布列表
            prob_list_html = ""
            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                if '(' in class_name:
                    chinese_name = class_name.split('(')[1].replace(')', '')
                    english_name = class_name.split('(')[0]
                else:
                    chinese_name = class_name
                    english_name = class_name

                prob_list_html += f'''
                <div style="background: rgba(255,255,255,0.8); padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #3498db;">
                    <strong>{chinese_name}</strong><br>
                    <small>{english_name}</small><br>
                    <span style="font-size: 1.2em; color: #2c3e50;">{prob:.4f} ({prob:.2%})</span>
                </div>
                '''

            # 创建HTML内容
            html_content = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI痴呆症诊断报告 - {result_data['timestamp']}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            font-size: 2.2em;
            margin: 0 0 10px 0;
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .info-section {{
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }}
        .result-section {{
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            text-align: center;
        }}
        .result-highlight {{
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px 0;
        }}
        .confidence {{
            font-size: 1.3em;
            color: #27ae60;
            font-weight: bold;
        }}
        .prob-section {{
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }}
        .disclaimer {{
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            text-align: center;
        }}
        .footer {{
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 0.9em;
        }}
        .print-btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 15px;
        }}
        .print-btn:hover {{
            background: #2980b9;
        }}
        @media print {{
            body {{ background: white; padding: 0; }}
            .container {{ box-shadow: none; }}
            .print-btn {{ display: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI痴呆症诊断报告</h1>
            <p>基于双模型深度学习的医学影像智能分析系统</p>
            <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>
        </div>

        <div class="content">
            <div class="section info-section">
                <h2>📋 基本信息</h2>
                <p><strong>分析时间:</strong> {result_data['timestamp']}</p>
                <p><strong>图像文件:</strong> {os.path.basename(result_data['image_path'])}</p>
                <p><strong>文件大小:</strong> {self.get_file_size(result_data['image_path'])}</p>
                <p><strong>分析模式:</strong> 双模型系统 (MRI检测 + 痴呆症分析)</p>
            </div>

            {image_section}

            {mri_section}

            <div class="section result-section">
                <h2>🎯 痴呆症AI分析结果</h2>
                <div class="result-highlight">
                    {result_data['predicted_class_name']}
                </div>
                <div class="confidence">
                    置信度: {result_data['confidence']:.2%}
                </div>
            </div>

            <div class="section prob-section">
                <h2>📊 痴呆症分析详细概率分布</h2>
                {prob_list_html}
            </div>

            <div class="section disclaimer">
                <h2>⚠️ 重要医学声明</h2>
                <p><strong>本AI分析结果仅供研究和参考使用，不能替代专业医学诊断。</strong></p>
                <p>任何医疗决策都应该咨询合格的医疗专业人员。</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 AI Medical Solutions | 报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
            '''

            # 保存HTML文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            raise Exception(f"HTML报告生成失败: {e}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"

    def create_pdf_report(self, result_data, file_path):
        """创建PDF报告"""
        try:
            from fpdf import FPDF

            pdf = FPDF()
            pdf.add_page()

            # 标题
            pdf.set_font('Arial', 'B', 16)
            pdf.cell(0, 10, 'AI Dementia Detection Report', 0, 1, 'C')
            pdf.ln(10)

            # 基本信息
            pdf.set_font('Arial', '', 12)
            pdf.cell(0, 8, f"Analysis Date: {result_data['timestamp']}", 0, 1)
            pdf.cell(0, 8, f"Image File: {os.path.basename(result_data['image_path'])}", 0, 1)
            pdf.ln(5)

            # 分析结果
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Analysis Results:', 0, 1)
            pdf.set_font('Arial', '', 11)

            predicted_class = result_data['predicted_class_name']
            confidence = result_data['confidence']

            pdf.cell(0, 8, f"Predicted Classification: {predicted_class}", 0, 1)
            pdf.cell(0, 8, f"Confidence Level: {confidence:.2%}", 0, 1)
            pdf.ln(5)

            # 详细概率分布
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Detailed Probability Distribution:', 0, 1)
            pdf.set_font('Arial', '', 10)

            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                pdf.cell(0, 6, f"  {class_name}: {prob:.4f} ({prob:.2%})", 0, 1)

            pdf.ln(10)

            # 免责声明
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(0, 6, 'IMPORTANT DISCLAIMER:', 0, 1)
            pdf.set_font('Arial', '', 9)
            disclaimer = ("This AI analysis is for research and reference purposes only. "
                          "It cannot replace professional medical diagnosis. "
                          "Please consult qualified healthcare professionals.")

            words = disclaimer.split()
            line = ""
            for word in words:
                if len(line + word) < 80:
                    line += word + " "
                else:
                    pdf.cell(0, 5, line.strip(), 0, 1)
                    line = word + " "
            if line:
                pdf.cell(0, 5, line.strip(), 0, 1)

            pdf.output(file_path)

        except ImportError:
            raise Exception("PDF库未安装，请安装fpdf2: pip install fpdf2")
        except Exception as e:
            raise Exception(f"PDF创建失败: {e}")

    def create_text_report(self, result_data, file_path):
        """创建文本报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("AI痴呆症检测报告\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"分析时间: {result_data['timestamp']}\n")
            f.write(f"图像文件: {os.path.basename(result_data['image_path'])}\n\n")
            f.write(f"预测结果: {result_data['predicted_class_name']}\n")
            f.write(f"置信度: {result_data['confidence']:.2%}\n\n")
            f.write("详细概率分布:\n")
            for i, prob in enumerate(result_data['probabilities']):
                f.write(f"  {self.class_labels[i]}: {prob:.4f} ({prob:.2%})\n")
            f.write("\n重要声明:\n")
            f.write("此结果仅供研究和参考使用，不能替代专业医学诊断。\n")
            f.write("请咨询合格的医疗专业人员获取准确诊断。\n")

    def show_history(self):
        """显示历史记录"""
        if not self.results_history:
            messagebox.showinfo("提示", "暂无历史记录")
            return

        history_window = ctk.CTkToplevel(self.root)
        history_window.title("分析历史")
        history_window.geometry("600x400")

        history_frame = ctk.CTkScrollableFrame(history_window)
        history_frame.pack(fill="both", expand=True, padx=20, pady=20)

        for i, record in enumerate(reversed(self.results_history)):
            record_frame = ctk.CTkFrame(history_frame)
            record_frame.pack(fill="x", pady=5)

            info_text = f"#{len(self.results_history) - i} - {record['timestamp']}\n"
            info_text += f"结果: {record['predicted_class_name']}\n"
            info_text += f"置信度: {record['confidence']:.2%}"

            record_label = ctk.CTkLabel(record_frame, text=info_text, font=ctk.CTkFont(size=12), justify="left")
            record_label.pack(padx=15, pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """
🧠 AI痴呆症识别器 v1.0 - 双模型版

基于双模型深度学习的医学影像智能分析系统

✨ 主要功能:
• 🔍 CT图像智能检测验证
• 🧠 多类别痴呆症检测分析
• ⚠️ 图像类型验证警告
• 📊 概率分布可视化
• 📋 结果历史记录
• 📄 专业PDF报告生成
• 🌐 HTML报告包含原始图像

🤖 双模型系统:
• MRI检测模型: 验证输入图像类型
• 痴呆症分析模型: 4类痴呆症分类
• 智能警告: 非MRI图像提醒

⚠️ 重要声明:
本软件仅供研究和参考使用，
不能替代专业医学诊断。
请咨询专业医生获取准确诊断。

© 2024 AI Medical Solutions
        """
        messagebox.showinfo("关于软件", about_text)

    def toggle_camera(self):
        """切换摄像头状态"""
        if not self.camera_active:
            self.start_camera()
        else:
            self.stop_camera()

    def start_camera(self):
        """启动摄像头"""
        try:
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                messagebox.showerror("错误", "无法打开摄像头")
                return

            self.camera_active = True
            self.camera_btn.configure(text="📹 关闭摄像头", fg_color="red")
            self.capture_btn.configure(state="normal")
            self.status_label.configure(text="📹 摄像头已启动")

            # 开始摄像头预览
            self.update_camera_feed()

        except Exception as e:
            messagebox.showerror("错误", f"摄像头启动失败：{e}")

    def stop_camera(self):
        """停止摄像头"""
        self.camera_active = False
        if self.camera:
            self.camera.release()
            self.camera = None

        self.camera_btn.configure(text="📹 启动摄像头", fg_color="green")
        self.capture_btn.configure(state="disabled")
        self.status_label.configure(text="📹 摄像头已关闭")

        # 恢复默认图像显示
        self.image_label.configure(
            image="",
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP",
            text_color="gray"
        )

    def update_camera_feed(self):
        """更新摄像头画面"""
        if self.camera_active and self.camera:
            try:
                ret, frame = self.camera.read()
                if ret:
                    # 调整图像大小
                    frame = cv2.resize(frame, (400, 400))
                    # 转换颜色格式
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    # 转换为PIL图像
                    pil_image = Image.fromarray(frame_rgb)
                    # 转换为tkinter可用的格式
                    photo = ImageTk.PhotoImage(pil_image)

                    # 更新显示
                    self.image_label.configure(image=photo, text="")
                    self.image_label.image = photo

                    # 继续更新
                    self.root.after(30, self.update_camera_feed)
                else:
                    self.stop_camera()
                    messagebox.showerror("错误", "摄像头读取失败")
            except Exception as e:
                self.stop_camera()
                messagebox.showerror("错误", f"摄像头更新失败：{e}")

    def capture_and_analyze(self):
        """拍照并分析"""
        if not self.camera_active or not self.camera:
            messagebox.showerror("错误", "摄像头未启动")
            return

        try:
            # 拍照
            ret, frame = self.camera.read()
            if not ret:
                messagebox.showerror("错误", "拍照失败")
                return

            # 保存拍照图像
            import tempfile
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_image_path = os.path.join(temp_dir, f"camera_capture_{timestamp}.jpg")

            cv2.imwrite(temp_image_path, frame)

            # 停止摄像头预览，保持当前画面
            self.camera_active = False
            self.camera_btn.configure(text="📹 启动摄像头", fg_color="green")
            self.capture_btn.configure(state="disabled")

            # 设置当前图像路径
            self.current_image_path = temp_image_path

            # 显示拍照的图像
            self.display_captured_image(frame)

            # 启用分析按钮
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"📸 已拍照，图像已保存")

            messagebox.showinfo("拍照成功", "图像已拍摄，可以开始分析")

        except Exception as e:
            messagebox.showerror("错误", f"拍照失败：{e}")

    def display_captured_image(self, frame):
        """显示拍摄的图像"""
        try:
            # 调整图像大小
            frame_resized = cv2.resize(frame, (400, 400))
            # 转换颜色格式
            frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
            # 转换为PIL图像
            pil_image = Image.fromarray(frame_rgb)
            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(pil_image)

            # 更新显示
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo

        except Exception as e:
            print(f"显示拍摄图像失败：{e}")

    def capture_screenshot(self):
        """屏幕截图功能"""
        try:
            # 导入截图库
            try:
                import pyautogui
            except ImportError:
                messagebox.showerror("错误", "缺少pyautogui库，请安装：pip install pyautogui")
                return

            # 隐藏主窗口，避免截图包含应用界面
            self.root.withdraw()

            # 等待一小段时间让窗口完全隐藏
            self.root.after(500, self.perform_screenshot)

        except Exception as e:
            messagebox.showerror("错误", f"屏幕截图准备失败：{e}")
            self.root.deiconify()  # 恢复窗口显示

    def perform_screenshot(self):
        """执行屏幕截图"""
        try:
            import pyautogui
            import tempfile

            # 显示截图提示窗口
            screenshot_window = self.create_screenshot_window()

        except Exception as e:
            messagebox.showerror("错误", f"屏幕截图失败：{e}")
            self.root.deiconify()

    def create_screenshot_window(self):
        """创建截图选择窗口"""
        try:
            import pyautogui
            import tempfile

            # 创建截图选择窗口
            screenshot_window = ctk.CTkToplevel()
            screenshot_window.title("屏幕截图")
            screenshot_window.geometry("400x300")
            screenshot_window.attributes("-topmost", True)

            # 窗口内容
            title_label = ctk.CTkLabel(
                screenshot_window,
                text="🖥️ 屏幕截图选择",
                font=ctk.CTkFont(size=18, weight="bold")
            )
            title_label.pack(pady=20)

            info_label = ctk.CTkLabel(
                screenshot_window,
                text="请选择截图方式：",
                font=ctk.CTkFont(size=14)
            )
            info_label.pack(pady=10)

            # 全屏截图按钮
            fullscreen_btn = ctk.CTkButton(
                screenshot_window,
                text="📺 全屏截图",
                font=ctk.CTkFont(size=14, weight="bold"),
                height=40,
                command=lambda: self.take_fullscreen_screenshot(screenshot_window),
                fg_color="blue"
            )
            fullscreen_btn.pack(fill="x", padx=20, pady=10)

            # 区域截图按钮
            region_btn = ctk.CTkButton(
                screenshot_window,
                text="🎯 选择区域截图",
                font=ctk.CTkFont(size=14, weight="bold"),
                height=40,
                command=lambda: self.take_region_screenshot(screenshot_window),
                fg_color="green"
            )
            region_btn.pack(fill="x", padx=20, pady=10)

            # 取消按钮
            cancel_btn = ctk.CTkButton(
                screenshot_window,
                text="❌ 取消",
                font=ctk.CTkFont(size=14),
                height=35,
                command=lambda: self.cancel_screenshot(screenshot_window),
                fg_color="red"
            )
            cancel_btn.pack(fill="x", padx=20, pady=10)

            return screenshot_window

        except Exception as e:
            messagebox.showerror("错误", f"创建截图窗口失败：{e}")
            self.root.deiconify()

    def take_fullscreen_screenshot(self, screenshot_window):
        """全屏截图"""
        try:
            import pyautogui
            import tempfile

            screenshot_window.destroy()

            # 等待窗口关闭
            self.root.after(200, lambda: self.execute_fullscreen_screenshot())

        except Exception as e:
            messagebox.showerror("错误", f"全屏截图失败：{e}")
            self.root.deiconify()

    def execute_fullscreen_screenshot(self):
        """执行全屏截图"""
        try:
            import pyautogui
            import tempfile

            # 截取全屏
            screenshot = pyautogui.screenshot()

            # 保存截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_dir = tempfile.gettempdir()
            screenshot_path = os.path.join(temp_dir, f"screenshot_{timestamp}.png")
            screenshot.save(screenshot_path)

            # 恢复主窗口
            self.root.deiconify()

            # 设置截图为当前分析图像
            self.current_image_path = screenshot_path
            self.display_image(screenshot_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"🖥️ 已截取全屏图像")

            messagebox.showinfo("截图成功", "全屏截图已完成，可以开始分析")

        except Exception as e:
            self.root.deiconify()
            messagebox.showerror("错误", f"全屏截图执行失败：{e}")

    def take_region_screenshot(self, screenshot_window):
        """区域截图"""
        try:
            screenshot_window.destroy()

            # 显示区域选择说明
            instruction_window = ctk.CTkToplevel()
            instruction_window.title("区域截图说明")
            instruction_window.geometry("350x200")
            instruction_window.attributes("-topmost", True)

            info_text = """
🎯 区域截图说明

1. 点击"开始选择"按钮
2. 用鼠标拖拽选择要截图的区域
3. 松开鼠标完成截图

注意：选择区域时请确保包含
要分析的医学影像内容
            """

            info_label = ctk.CTkLabel(
                instruction_window,
                text=info_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            info_label.pack(pady=20, padx=20)

            start_btn = ctk.CTkButton(
                instruction_window,
                text="🎯 开始选择区域",
                command=lambda: self.start_region_selection(instruction_window),
                fg_color="green"
            )
            start_btn.pack(pady=10)

            cancel_btn = ctk.CTkButton(
                instruction_window,
                text="❌ 取消",
                command=lambda: self.cancel_screenshot(instruction_window),
                fg_color="red"
            )
            cancel_btn.pack(pady=5)

        except Exception as e:
            messagebox.showerror("错误", f"区域截图准备失败：{e}")
            self.root.deiconify()

    def start_region_selection(self, instruction_window):
        """开始区域选择"""
        try:
            instruction_window.destroy()

            # 使用简单的区域选择方法
            messagebox.showinfo("区域截图", "即将开始区域截图\n\n请在3秒后用鼠标拖拽选择区域")

            self.root.after(3000, self.execute_region_screenshot)

        except Exception as e:
            messagebox.showerror("错误", f"区域选择失败：{e}")
            self.root.deiconify()

    def execute_region_screenshot(self):
        """执行区域截图"""
        try:
            import pyautogui
            import tempfile

            # 让用户手动选择区域（简化版本）
            # 这里使用一个简单的方法：让用户点击两个点来定义矩形区域
            messagebox.showinfo("区域截图", "请点击要截图区域的左上角")

            # 获取鼠标位置作为起始点
            start_pos = pyautogui.position()

            messagebox.showinfo("区域截图", "请点击要截图区域的右下角")

            # 获取鼠标位置作为结束点
            end_pos = pyautogui.position()

            # 计算区域
            left = min(start_pos.x, end_pos.x)
            top = min(start_pos.y, end_pos.y)
            width = abs(end_pos.x - start_pos.x)
            height = abs(end_pos.y - start_pos.y)

            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(left, top, width, height))

            # 保存截图
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_dir = tempfile.gettempdir()
            screenshot_path = os.path.join(temp_dir, f"region_screenshot_{timestamp}.png")
            screenshot.save(screenshot_path)

            # 恢复主窗口
            self.root.deiconify()

            # 设置截图为当前分析图像
            self.current_image_path = screenshot_path
            self.display_image(screenshot_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"🎯 已截取区域图像")

            messagebox.showinfo("截图成功", "区域截图已完成，可以开始分析")

        except Exception as e:
            self.root.deiconify()
            messagebox.showerror("错误", f"区域截图执行失败：{e}")

    def cancel_screenshot(self, window):
        """取消截图"""
        window.destroy()
        self.root.deiconify()
        self.status_label.configure(text="📷 截图已取消")

    def on_closing(self):
        """应用程序关闭处理"""
        if self.camera_active:
            self.stop_camera()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        try:
            self.root.mainloop()
        finally:
            if self.camera:
                self.camera.release()
            cv2.destroyAllWindows()


if __name__ == "__main__":
    app = AIDetectionApp()
    app.run()
