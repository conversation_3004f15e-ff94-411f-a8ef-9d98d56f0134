"""
🧠 完整深度学习EEG训练器 (CPU版本)
输出单一完整模型，支持多轮次训练
专为CPU环境优化
"""

import os
import numpy as np
import pandas as pd

# 强制使用CPU，避免GPU问题
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
from tensorflow.keras import layers, Model, optimizers, callbacks
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import mne
import warnings
warnings.filterwarnings('ignore')

# 确保TensorFlow使用CPU
tf.config.set_visible_devices([], 'GPU')
tf.get_logger().setLevel('ERROR')

print("💻 强制使用CPU模式，避免GPU兼容性问题")
print("🔧 CPU训练模式：稳定可靠，完整功能")

class CompleteEEGTrainer:
    """完整深度学习EEG训练器 (CPU优化版本)"""

    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.label_encoder = LabelEncoder()

        # EEG参数
        self.n_channels = 19
        self.n_samples = 128
        self.n_classes = None

        print("🧠 完整深度学习EEG训练器 (CPU版本)")
        print(f"📁 数据路径: {self.data_path}")
        print(f"📊 EEG参数: {self.n_channels}通道, {self.n_samples}采样点")
        print("💻 运行模式: CPU Only")
    
    def load_and_process_data(self, split_name):
        """加载并处理数据"""
        print(f"\n📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        # 读取标签
        labels_df = pd.read_csv(labels_file, sep='\t')
        print(f"📋 找到 {len(labels_df)} 个受试者")
        
        # 收集数据
        all_data = []
        all_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找.set文件
            set_files = [f for f in os.listdir(split_dir) 
                        if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                # 读取EEG数据
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                data = raw.get_data()
                
                # 创建epochs
                epochs = self.create_epochs(data)
                
                for epoch in epochs:
                    all_data.append(epoch)
                    all_labels.append(label)
                
                if len(epochs) > 0:
                    print(f"✅ {subject_id}: {len(epochs)} epochs")
                
            except Exception as e:
                print(f"❌ 处理{subject_id}失败: {e}")
                continue
        
        if len(all_data) == 0:
            raise ValueError("没有成功加载任何数据")
        
        X = np.array(all_data, dtype=np.float32)
        y = np.array(all_labels)
        
        print(f"📊 {split_name}数据: {X.shape}")
        print(f"📋 标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    
    def create_epochs(self, data):
        """创建标准化epochs"""
        n_channels, n_timepoints = data.shape
        n_epochs = n_timepoints // self.n_samples
        
        epochs = []
        for i in range(n_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            
            epoch = data[:, start_idx:end_idx]
            
            # 调整通道数
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]
            elif epoch.shape[0] < self.n_channels:
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            epochs.append(epoch)
        
        return epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("🔧 预处理数据...")
        
        # 标准化EEG数据
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 1e-8:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
            self.n_classes = len(self.label_encoder.classes_)
            print(f"📋 标签编码: {dict(zip(self.label_encoder.classes_, range(self.n_classes)))}")
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 转换为one-hot编码
        y_categorical = tf.keras.utils.to_categorical(y_encoded, self.n_classes)
        
        # 添加通道维度 (batch, channels, time, 1)
        X_processed = np.expand_dims(X_processed, axis=-1)
        
        print(f"✅ 预处理完成: {X_processed.shape} -> {y_categorical.shape}")
        return X_processed, y_categorical
    
    def build_complete_model(self):
        """构建完整的深度学习模型 (CPU优化版本)"""
        print(f"🏗️ 构建CPU优化模型 (类别数: {self.n_classes})...")
        
        # 输入层
        inputs = layers.Input(shape=(self.n_channels, self.n_samples, 1), name='eeg_input')
        
        # 第一层：时间卷积
        x = layers.Conv2D(32, (1, 7), padding='same', activation='relu', name='temporal_conv1')(inputs)
        x = layers.BatchNormalization(name='bn1')(x)
        
        # 第二层：空间卷积
        x = layers.Conv2D(32, (self.n_channels, 1), activation='relu', name='spatial_conv')(x)
        x = layers.BatchNormalization(name='bn2')(x)
        x = layers.Dropout(0.25, name='dropout1')(x)
        
        # 第三层：深层时间特征
        x = layers.Conv2D(64, (1, 7), padding='same', activation='relu', name='temporal_conv2')(x)
        x = layers.BatchNormalization(name='bn3')(x)
        x = layers.MaxPooling2D((1, 4), name='pool1')(x)
        x = layers.Dropout(0.25, name='dropout2')(x)
        
        # 第四层：高级特征
        x = layers.Conv2D(128, (1, 7), padding='same', activation='relu', name='temporal_conv3')(x)
        x = layers.BatchNormalization(name='bn4')(x)
        x = layers.MaxPooling2D((1, 4), name='pool2')(x)
        x = layers.Dropout(0.25, name='dropout3')(x)
        
        # 第五层：最终特征提取
        x = layers.Conv2D(256, (1, 5), padding='same', activation='relu', name='temporal_conv4')(x)
        x = layers.BatchNormalization(name='bn5')(x)
        x = layers.GlobalAveragePooling2D(name='global_pool')(x)
        
        # 分类头
        x = layers.Dense(512, activation='relu', name='fc1')(x)
        x = layers.BatchNormalization(name='bn6')(x)
        x = layers.Dropout(0.5, name='dropout4')(x)
        
        x = layers.Dense(256, activation='relu', name='fc2')(x)
        x = layers.BatchNormalization(name='bn7')(x)
        x = layers.Dropout(0.5, name='dropout5')(x)
        
        x = layers.Dense(128, activation='relu', name='fc3')(x)
        x = layers.Dropout(0.3, name='dropout6')(x)
        
        # 输出层
        outputs = layers.Dense(self.n_classes, activation='softmax', name='predictions')(x)
        
        # 创建模型
        model = Model(inputs=inputs, outputs=outputs, name='CompleteEEGModel')
        
        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print(f"✅ 完整模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model
    
    def train_complete_model(self, epochs=80, batch_size=16):
        """训练完整模型 (CPU优化版本)"""
        print(f"🚀 开始完整模型训练 (CPU模式)...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        print("⏰ CPU训练较慢，请耐心等待...")
        
        # 加载所有数据
        X_train, y_train = self.load_and_process_data('train')
        X_val, y_val = self.load_and_process_data('val')
        
        # 预处理数据
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 构建模型
        self.model = self.build_complete_model()
        
        # 设置回调函数
        callback_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=20,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                'complete_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
        ]
        
        # 开始训练
        print("🎯 开始多轮次训练...")
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callback_list,
            verbose=1
        )
        
        print("✅ 训练完成!")
        
        # 保存完整模型
        self.model.save('final_complete_eeg_model.h5', save_format='h5')
        print("💾 完整模型已保存: final_complete_eeg_model.h5")
        
        # 保存标签编码器
        import joblib
        joblib.dump(self.label_encoder, 'eeg_label_encoder.pkl')
        print("💾 标签编码器已保存: eeg_label_encoder.pkl")
        
        return history
    
    def evaluate_complete_model(self):
        """评估完整模型"""
        print("\n📊 评估完整模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_and_process_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        
        # 预测
        print("🔮 进行预测...")
        y_pred_prob = self.model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)
        
        # 计算指标
        test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
        
        print(f"📈 完整模型测试结果:")
        print(f"   准确率: {test_acc:.4f}")
        print(f"   损失: {test_loss:.4f}")
        
        # 详细分类报告
        class_names = self.label_encoder.classes_
        report = classification_report(y_true, y_pred, target_names=class_names)
        print(f"\n📋 详细分类报告:")
        print(report)
        
        # 保存预测结果
        results = {
            'accuracy': test_acc,
            'loss': test_loss,
            'predictions': y_pred,
            'probabilities': y_pred_prob,
            'true_labels': y_true,
            'class_names': class_names
        }
        
        import joblib
        joblib.dump(results, 'complete_model_results.pkl')
        print("💾 评估结果已保存: complete_model_results.pkl")
        
        return results
    
    def save_training_summary(self, history):
        """保存训练摘要"""
        print("\n📄 保存训练摘要...")
        
        # 保存训练历史
        history_df = pd.DataFrame(history.history)
        history_df.to_csv('training_history.csv', index=False)
        
        # 找到最佳epoch
        best_epoch = np.argmax(history.history['val_accuracy'])
        best_val_acc = max(history.history['val_accuracy'])
        final_train_acc = history.history['accuracy'][-1]
        
        # 创建摘要
        summary = {
            'model_name': 'CompleteEEGModel',
            'total_epochs': len(history.history['accuracy']),
            'best_epoch': best_epoch + 1,
            'best_validation_accuracy': best_val_acc,
            'final_training_accuracy': final_train_acc,
            'model_parameters': self.model.count_params(),
            'n_classes': self.n_classes,
            'class_names': self.label_encoder.classes_.tolist()
        }
        
        # 保存摘要
        import json
        with open('training_summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"🏆 最佳验证准确率: {best_val_acc:.4f} (第{best_epoch+1}轮)")
        print("💾 训练摘要已保存: training_summary.json")


def main():
    """主函数"""
    print("🧠 完整深度学习EEG训练系统 (CPU版本)")
    print("🎯 输出单一完整模型，支持多轮次训练")
    print("💻 CPU模式：稳定可靠，避免GPU兼容性问题")
    print("=" * 60)

    # 创建训练器
    trainer = CompleteEEGTrainer()

    # 训练完整模型 (CPU优化参数)
    history = trainer.train_complete_model(epochs=80, batch_size=16)
    
    # 保存训练摘要
    trainer.save_training_summary(history)
    
    # 评估模型
    results = trainer.evaluate_complete_model()
    
    print(f"\n🎉 完整训练完成!")
    print(f"🏆 最终准确率: {results['accuracy']:.4f}")
    print(f"\n📁 输出文件:")
    print(f"   🎯 final_complete_eeg_model.h5 - 完整训练模型")
    print(f"   📊 complete_model_results.pkl - 评估结果")
    print(f"   📋 eeg_label_encoder.pkl - 标签编码器")
    print(f"   📈 training_history.csv - 训练历史")
    print(f"   📄 training_summary.json - 训练摘要")


if __name__ == "__main__":
    main()
