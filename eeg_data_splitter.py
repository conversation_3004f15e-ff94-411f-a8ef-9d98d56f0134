"""
📊 EEG数据集划分器
基于优化方案生成训练集、验证集和测试集
请根据您的实际路径修改配置部分
"""

import os
import zipfile
import json
import pandas as pd
import numpy as np
from collections import Counter
from sklearn.model_selection import train_test_split
import shutil
import warnings
warnings.filterwarnings('ignore')

class EEGDataSplitter:
    """EEG数据集划分器"""
    
    def __init__(self):
        # ==========================================
        # 🔧 配置部分 - 请根据您的实际路径修改
        # ==========================================
        
        # 1. 数据集路径配置
        self.zip_path = "D:/模型开发/EEG.zip"  # 📍 修改为您的EEG数据集ZIP文件路径
        self.extract_path = "D:/模型开发/EEG_extracted"  # 📍 修改为您希望解压到的目录
        
        # 2. 输出路径配置
        self.output_base_path = "D:/模型开发/EEG_splits"  # 📍 修改为您希望保存划分结果的目录
        
        # 3. 划分比例配置 (针对双模型联用优化)
        self.train_ratio = 0.70  # 训练集比例
        self.val_ratio = 0.15    # 验证集比例
        self.test_ratio = 0.15   # 测试集比例
        
        # ==========================================
        # 内部变量 - 无需修改
        # ==========================================
        self.patient_labels = {}
        self.label_definitions = {}
        self.split_result = {}
        
    def load_patient_labels(self):
        """加载患者标签信息"""
        print("📋 加载患者标签信息...")
        print(f"📁 数据集路径: {self.zip_path}")
        
        if not os.path.exists(self.zip_path):
            print(f"❌ 错误: 数据集文件不存在")
            print(f"   请检查路径: {self.zip_path}")
            print(f"   请修改脚本中的 self.zip_path 变量")
            return False
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                # 读取标签定义
                with zip_ref.open('dataset/participants.json') as f:
                    participants_json = json.load(f)
                    self.label_definitions = participants_json['Group']['Levels']
                
                # 读取患者数据
                with zip_ref.open('dataset/participants.tsv') as f:
                    content = f.read().decode('utf-8')
                    lines = content.strip().split('\n')
                    header = lines[0].split('\t')
                    
                    participant_col = header.index('participant_id')
                    group_col = header.index('Group')
                    
                    for line in lines[1:]:
                        if line.strip():
                            parts = line.split('\t')
                            if len(parts) > max(participant_col, group_col):
                                participant_id = parts[participant_col].strip()
                                group_label = parts[group_col].strip()
                                self.patient_labels[participant_id] = group_label
            
            print(f"✅ 成功加载 {len(self.patient_labels)} 个患者的标签")
            return True
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def display_patient_distribution(self):
        """显示患者分布"""
        print(f"\n📊 患者分布统计:")
        print("=" * 50)
        
        label_counts = Counter(self.patient_labels.values())
        total_patients = len(self.patient_labels)
        
        for label_code, count in sorted(label_counts.items()):
            if label_code in self.label_definitions:
                description = self.label_definitions[label_code]
                percentage = (count / total_patients) * 100
                print(f"🏷️ {label_code} - {description}:")
                print(f"   患者数量: {count} 人 ({percentage:.1f}%)")
                print()
        
        print(f"📋 总患者数: {total_patients}")
    
    def create_stratified_split(self):
        """创建分层划分"""
        print(f"\n🔄 创建分层数据划分...")
        print(f"📊 划分比例: 训练{self.train_ratio*100:.0f}% | 验证{self.val_ratio*100:.0f}% | 测试{self.test_ratio*100:.0f}%")
        
        # 准备数据
        subject_ids = list(self.patient_labels.keys())
        labels = list(self.patient_labels.values())
        
        # 第一次划分: 分出测试集
        train_val_ids, test_ids, train_val_labels, test_labels = train_test_split(
            subject_ids, labels,
            test_size=self.test_ratio,
            random_state=42,
            stratify=labels
        )
        
        # 第二次划分: 从训练+验证中分出验证集
        val_ratio_adjusted = self.val_ratio / (self.train_ratio + self.val_ratio)
        train_ids, val_ids, train_labels, val_labels = train_test_split(
            train_val_ids, train_val_labels,
            test_size=val_ratio_adjusted,
            random_state=42,
            stratify=train_val_labels
        )
        
        # 保存划分结果
        self.split_result = {
            'train': {
                'subject_ids': train_ids,
                'labels': train_labels,
                'count': len(train_ids)
            },
            'val': {
                'subject_ids': val_ids,
                'labels': val_labels,
                'count': len(val_ids)
            },
            'test': {
                'subject_ids': test_ids,
                'labels': test_labels,
                'count': len(test_ids)
            }
        }
        
        # 显示划分结果
        self.display_split_results()
        
        return True
    
    def display_split_results(self):
        """显示划分结果"""
        print(f"\n✅ 数据划分完成!")
        print("=" * 60)
        
        class_names = {
            'A': '阿尔茨海默病',
            'C': '健康对照',
            'F': '额颞叶痴呆'
        }
        
        for split_name, split_data in self.split_result.items():
            print(f"\n📋 {split_name.upper()}集 ({split_data['count']} 人):")
            
            # 统计各类别数量
            label_counts = Counter(split_data['labels'])
            for label, count in sorted(label_counts.items()):
                class_name = class_names.get(label, label)
                percentage = count / split_data['count'] * 100
                print(f"   {label} ({class_name}): {count} 人 ({percentage:.1f}%)")
            
            # 显示具体的患者ID (前10个)
            print(f"   患者ID示例: {', '.join(split_data['subject_ids'][:10])}")
            if len(split_data['subject_ids']) > 10:
                print(f"   ... 还有 {len(split_data['subject_ids'])-10} 个")
    
    def extract_and_organize_data(self):
        """解压并组织数据文件"""
        print(f"\n📦 解压并组织数据文件...")
        print(f"📁 解压目标路径: {self.extract_path}")
        
        # 创建解压目录
        os.makedirs(self.extract_path, exist_ok=True)
        
        # 解压数据集
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.extract_path)
            print(f"✅ 数据集解压完成")
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            return False
        
        # 查找EEG文件
        eeg_files = {}
        dataset_root = os.path.join(self.extract_path, "dataset")
        
        for subject_id in self.patient_labels.keys():
            subject_dir = os.path.join(dataset_root, subject_id, "eeg")
            if os.path.exists(subject_dir):
                set_files = [f for f in os.listdir(subject_dir) if f.endswith('.set')]
                if set_files:
                    eeg_files[subject_id] = os.path.join(subject_dir, set_files[0])
        
        print(f"✅ 找到 {len(eeg_files)} 个EEG文件")
        self.eeg_files = eeg_files
        
        return True
    
    def create_split_directories(self):
        """创建划分目录结构"""
        print(f"\n📁 创建划分目录结构...")
        print(f"📍 输出基础路径: {self.output_base_path}")
        
        # 创建主目录
        os.makedirs(self.output_base_path, exist_ok=True)
        
        # 为每个划分创建目录
        split_dirs = {}
        for split_name in ['train', 'val', 'test']:
            split_dir = os.path.join(self.output_base_path, split_name)
            os.makedirs(split_dir, exist_ok=True)
            split_dirs[split_name] = split_dir
            print(f"   📂 {split_name}集目录: {split_dir}")
        
        self.split_dirs = split_dirs
        return True
    
    def copy_files_to_splits(self):
        """复制文件到对应的划分目录"""
        print(f"\n📋 复制文件到划分目录...")
        
        for split_name, split_data in self.split_result.items():
            split_dir = self.split_dirs[split_name]
            subject_ids = split_data['subject_ids']
            
            print(f"\n🔄 处理 {split_name.upper()}集 ({len(subject_ids)} 个文件)...")
            
            copied_count = 0
            for subject_id in subject_ids:
                if subject_id in self.eeg_files:
                    source_file = self.eeg_files[subject_id]
                    if os.path.exists(source_file):
                        # 目标文件名
                        filename = os.path.basename(source_file)
                        target_file = os.path.join(split_dir, filename)
                        
                        # 复制文件
                        try:
                            shutil.copy2(source_file, target_file)
                            copied_count += 1
                        except Exception as e:
                            print(f"   ⚠️ 复制失败 {subject_id}: {e}")
                    else:
                        print(f"   ⚠️ 文件不存在: {subject_id}")
                else:
                    print(f"   ⚠️ 未找到EEG文件: {subject_id}")
            
            print(f"   ✅ {split_name.upper()}集: 成功复制 {copied_count}/{len(subject_ids)} 个文件")
    
    def save_split_metadata(self):
        """保存划分元数据"""
        print(f"\n💾 保存划分元数据...")
        
        # 准备元数据
        metadata = {
            'dataset_info': {
                'source_zip': self.zip_path,
                'extract_path': self.extract_path,
                'output_path': self.output_base_path,
                'total_patients': len(self.patient_labels),
                'split_ratios': {
                    'train': self.train_ratio,
                    'val': self.val_ratio,
                    'test': self.test_ratio
                }
            },
            'label_definitions': self.label_definitions,
            'patient_labels': self.patient_labels,
            'split_result': self.split_result,
            'file_paths': {
                split_name: {
                    'directory': self.split_dirs[split_name],
                    'subjects': split_data['subject_ids'],
                    'labels': split_data['labels']
                }
                for split_name, split_data in self.split_result.items()
            }
        }
        
        # 保存到JSON文件
        metadata_file = os.path.join(self.output_base_path, "split_metadata.json")
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            print(f"✅ 元数据已保存: {metadata_file}")
        except Exception as e:
            print(f"❌ 元数据保存失败: {e}")
        
        # 保存简化的标签文件
        for split_name, split_data in self.split_result.items():
            label_file = os.path.join(self.split_dirs[split_name], "labels.txt")
            try:
                with open(label_file, 'w', encoding='utf-8') as f:
                    f.write("subject_id\tlabel\tlabel_name\n")
                    for subject_id, label in zip(split_data['subject_ids'], split_data['labels']):
                        label_name = self.label_definitions.get(label, label)
                        f.write(f"{subject_id}\t{label}\t{label_name}\n")
                print(f"✅ {split_name.upper()}集标签文件: {label_file}")
            except Exception as e:
                print(f"❌ {split_name}集标签文件保存失败: {e}")
    
    def generate_summary_report(self):
        """生成总结报告"""
        print(f"\n📊 生成总结报告...")
        
        report_file = os.path.join(self.output_base_path, "split_summary.txt")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("EEG数据集划分总结报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"数据集信息:\n")
                f.write(f"  源文件: {self.zip_path}\n")
                f.write(f"  输出目录: {self.output_base_path}\n")
                f.write(f"  总患者数: {len(self.patient_labels)}\n\n")
                
                f.write(f"划分比例:\n")
                f.write(f"  训练集: {self.train_ratio*100:.0f}%\n")
                f.write(f"  验证集: {self.val_ratio*100:.0f}%\n")
                f.write(f"  测试集: {self.test_ratio*100:.0f}%\n\n")
                
                f.write(f"划分结果:\n")
                for split_name, split_data in self.split_result.items():
                    f.write(f"  {split_name.upper()}集: {split_data['count']} 人\n")
                    label_counts = Counter(split_data['labels'])
                    for label, count in sorted(label_counts.items()):
                        label_name = self.label_definitions.get(label, label)
                        f.write(f"    {label} ({label_name}): {count} 人\n")
                    f.write(f"\n")
                
                f.write(f"文件路径:\n")
                for split_name in ['train', 'val', 'test']:
                    f.write(f"  {split_name.upper()}集目录: {self.split_dirs[split_name]}\n")
            
            print(f"✅ 总结报告已保存: {report_file}")
            
        except Exception as e:
            print(f"❌ 总结报告保存失败: {e}")
    
    def run_complete_split(self):
        """运行完整的数据划分流程"""
        print("🚀 开始EEG数据集划分流程")
        print("=" * 60)
        
        # 步骤1: 加载患者标签
        if not self.load_patient_labels():
            return False
        
        # 步骤2: 显示患者分布
        self.display_patient_distribution()
        
        # 步骤3: 创建分层划分
        if not self.create_stratified_split():
            return False
        
        # 步骤4: 解压并组织数据
        if not self.extract_and_organize_data():
            return False
        
        # 步骤5: 创建划分目录
        if not self.create_split_directories():
            return False
        
        # 步骤6: 复制文件到划分目录
        self.copy_files_to_splits()
        
        # 步骤7: 保存元数据
        self.save_split_metadata()
        
        # 步骤8: 生成总结报告
        self.generate_summary_report()
        
        print(f"\n🎉 数据划分完成!")
        print("=" * 60)
        print(f"📁 输出目录: {self.output_base_path}")
        print(f"📋 查看详细信息: {os.path.join(self.output_base_path, 'split_summary.txt')}")
        print(f"📊 元数据文件: {os.path.join(self.output_base_path, 'split_metadata.json')}")
        
        return True


def main():
    """主函数"""
    print("📊 EEG数据集划分器")
    print("=" * 50)
    print("⚠️ 使用前请检查并修改脚本顶部的路径配置!")
    print()
    
    # 创建划分器
    splitter = EEGDataSplitter()
    
    # 运行完整划分流程
    success = splitter.run_complete_split()
    
    if success:
        print(f"\n✅ 所有步骤完成!")
        print(f"📋 下一步: 使用划分好的数据训练EEG模型")
    else:
        print(f"\n❌ 划分过程中出现错误，请检查配置和路径")


if __name__ == "__main__":
    main()
