#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双模型AI痴呆症识别系统 - 主程序入口
Main entry point for Dual Model AI Dementia Detection System
"""

import sys
import os
import argparse
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dual_model_detector import (
    DualModelDetector, 
    get_version, 
    get_system_info,
    quick_start,
    GUI_AVAILABLE
)

def setup_logging(level=logging.INFO):
    """设置日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('dual_model_detector.log', encoding='utf-8')
        ]
    )

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='双模型AI痴呆症识别系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --gui                    # 启动GUI界面
  %(prog)s --image image.jpg        # 分析单张图像
  %(prog)s --batch images/          # 批量分析
  %(prog)s --version                # 显示版本信息
  %(prog)s --system-info            # 显示系统信息
        """
    )
    
    # 基本选项
    parser.add_argument(
        '--version', 
        action='version', 
        version=f'双模型AI痴呆症识别系统 v{get_version()}'
    )
    
    parser.add_argument(
        '--system-info',
        action='store_true',
        help='显示系统信息'
    )
    
    parser.add_argument(
        '--quick-start',
        action='store_true',
        help='显示快速启动指南'
    )
    
    # 运行模式
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--gui',
        action='store_true',
        help='启动GUI界面 (默认模式)'
    )
    
    mode_group.add_argument(
        '--cli',
        action='store_true',
        help='使用命令行模式'
    )
    
    # 分析选项
    parser.add_argument(
        '--image',
        type=str,
        help='要分析的图像文件路径'
    )
    
    parser.add_argument(
        '--batch',
        type=str,
        help='批量分析的图像文件夹路径'
    )
    
    # 模型选项
    parser.add_argument(
        '--ct-model',
        type=str,
        default=r"D:\模型开发\ct_other_model.h5",
        help='CT图像识别模型路径'
    )
    
    parser.add_argument(
        '--symptom-model',
        type=str,
        default=r"D:\模型开发\ct_class.h5",
        help='症状分析模型路径'
    )
    
    # 输出选项
    parser.add_argument(
        '--output',
        type=str,
        help='结果输出文件路径'
    )
    
    parser.add_argument(
        '--report',
        choices=['pdf', 'html', 'json'],
        help='生成报告格式'
    )
    
    parser.add_argument(
        '--no-gui-fallback',
        action='store_true',
        help='禁用GUI回退，强制使用CLI'
    )
    
    # 调试选项
    parser.add_argument(
        '--debug',
        action='store_true',
        help='启用调试模式'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='详细输出'
    )
    
    return parser.parse_args()

def run_gui_mode(args):
    """运行GUI模式"""
    if not GUI_AVAILABLE:
        print("❌ GUI功能不可用，缺少customtkinter依赖")
        print("请运行: pip install customtkinter")
        if not args.no_gui_fallback:
            print("🔄 自动切换到命令行模式...")
            return run_cli_mode(args)
        return 1
    
    try:
        from dual_model_detector.gui import DualModelAIDetectionApp
        
        print("🚀 启动双模型AI痴呆症识别系统 GUI...")
        
        # 创建应用实例
        app = DualModelAIDetectionApp()
        
        # 如果指定了模型路径，更新配置
        if args.ct_model:
            app.ct_detection_path = args.ct_model
        if args.symptom_model:
            app.ct_classification_path = args.symptom_model
        
        # 运行应用
        app.run()
        return 0
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

def run_cli_mode(args):
    """运行命令行模式"""
    try:
        # 创建检测器
        detector = DualModelDetector(
            ct_model_path=args.ct_model,
            classification_model_path=args.symptom_model
        )
        
        if args.image:
            # 分析单张图像
            print(f"🔍 分析图像: {args.image}")
            result = detector.analyze_image(args.image)
            
            # 显示结果
            print_analysis_result(result)
            
            # 保存结果
            if args.output:
                save_result(result, args.output, args.report)
                
        elif args.batch:
            # 批量分析
            print(f"📂 批量分析文件夹: {args.batch}")
            results = detector.batch_analyze(args.batch)
            
            # 显示结果
            for i, result in enumerate(results, 1):
                print(f"\n--- 结果 {i} ---")
                print_analysis_result(result)
            
            # 保存结果
            if args.output:
                save_batch_results(results, args.output, args.report)
        else:
            print("❌ 请指定要分析的图像 (--image) 或文件夹 (--batch)")
            return 1
            
        return 0
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1

def print_analysis_result(result):
    """打印分析结果"""
    print(f"\n{'='*60}")
    print("🧠 双模型分析结果")
    print(f"{'='*60}")
    
    # CT检测结果
    ct_result = result.get('ct_detection', {})
    is_ct = ct_result.get('is_ct', False)
    ct_confidence = ct_result.get('confidence', 0)
    
    print(f"🔍 CT图像检测: {'✅ 是CT图像' if is_ct else '❌ 不是CT图像'}")
    print(f"📊 CT检测置信度: {ct_confidence:.2%}")
    
    # 症状分析结果
    if result.get('analysis_completed', False):
        symptom_result = result.get('symptom_analysis', {})
        predicted_class = symptom_result.get('predicted_class_name', 'Unknown')
        confidence = symptom_result.get('confidence', 0)
        
        print(f"\n🧠 症状分析结果: {predicted_class}")
        print(f"📊 症状分析置信度: {confidence:.2%}")
        
        # 详细概率分布
        probabilities = symptom_result.get('probabilities', [])
        if probabilities:
            print(f"\n📈 详细概率分布:")
            class_labels = [
                "轻度痴呆", "中度痴呆", "无痴呆", "非常轻度痴呆"
            ]
            for i, prob in enumerate(probabilities):
                if i < len(class_labels):
                    print(f"   {class_labels[i]}: {prob:.2%}")
    else:
        print(f"\n⚠️ 症状分析: 未执行（非CT图像）")
    
    print(f"{'='*60}")

def save_result(result, output_path, report_format):
    """保存单个分析结果"""
    try:
        if report_format == 'json' or output_path.endswith('.json'):
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"✅ 结果已保存到: {output_path}")
            
        elif report_format == 'pdf':
            from dual_model_detector.reports import PDFReportGenerator
            generator = PDFReportGenerator()
            generator.generate_report(result, output_path)
            print(f"✅ PDF报告已生成: {output_path}")
            
        elif report_format == 'html':
            from dual_model_detector.reports import HTMLReportGenerator
            generator = HTMLReportGenerator()
            generator.generate_report(result, output_path)
            print(f"✅ HTML报告已生成: {output_path}")
            
    except Exception as e:
        print(f"❌ 保存失败: {e}")

def save_batch_results(results, output_path, report_format):
    """保存批量分析结果"""
    try:
        import json
        
        # 总是保存JSON格式的批量结果
        json_path = output_path if output_path.endswith('.json') else f"{output_path}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"✅ 批量结果已保存到: {json_path}")
        
        # 如果指定了其他格式，为每个结果生成单独的报告
        if report_format in ['pdf', 'html']:
            for i, result in enumerate(results):
                if result.get('analysis_completed', False):
                    base_name = os.path.splitext(output_path)[0]
                    report_path = f"{base_name}_result_{i+1}.{report_format}"
                    save_result(result, report_path, report_format)
                    
    except Exception as e:
        print(f"❌ 批量保存失败: {e}")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志级别
    log_level = logging.DEBUG if args.debug else logging.INFO
    setup_logging(log_level)
    
    # 处理信息查询命令
    if args.system_info:
        info = get_system_info()
        print("🖥️ 系统信息:")
        for key, value in info.items():
            print(f"   {key}: {value}")
        return 0
    
    if args.quick_start:
        quick_start()
        return 0
    
    # 确定运行模式
    if args.cli or args.image or args.batch:
        return run_cli_mode(args)
    else:
        # 默认启动GUI模式
        return run_gui_mode(args)

if __name__ == "__main__":
    sys.exit(main())
