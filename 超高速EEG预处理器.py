"""
⚡ 超高速EEG预处理器
专门优化预处理速度的EEG数据处理器
"""

import os
import numpy as np
import pandas as pd
import mne
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
from functools import partial
import time
import warnings
warnings.filterwarnings('ignore')

class UltraFastEEGPreprocessor:
    """超高速EEG预处理器"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets", n_jobs=-1):
        self.data_path = data_path
        self.n_jobs = n_jobs if n_jobs != -1 else mp.cpu_count()
        
        # EEG参数
        self.n_channels = 19
        self.n_samples = 128
        self.sampling_rate = 128
        
        print(f"⚡ 超高速EEG预处理器初始化")
        print(f"🔥 使用 {self.n_jobs} 个CPU核心并行处理")
    
    def load_single_subject_fast(self, args):
        """快速加载单个被试数据"""
        split_dir, subject_id, label = args
        
        try:
            set_files = [f for f in os.listdir(split_dir) 
                        if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                return None, None
            
            set_file = os.path.join(split_dir, set_files[0])
            
            # 快速加载EEG数据
            raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
            data = raw.get_data()
            
            # 超高速epochs创建
            epochs = self.create_epochs_vectorized(data)
            
            # 返回epochs和对应的标签
            labels = [label] * len(epochs)
            
            return epochs, labels
            
        except Exception as e:
            print(f"❌ 加载{subject_id}失败: {e}")
            return None, None
    
    def create_epochs_vectorized(self, data):
        """向量化创建epochs"""
        n_channels, n_timepoints = data.shape
        
        # 🚀 超快速通道处理
        if n_channels != self.n_channels:
            if n_channels > self.n_channels:
                data = data[:self.n_channels, :]
            else:
                padded_data = np.zeros((self.n_channels, n_timepoints))
                padded_data[:n_channels, :] = data
                data = padded_data
        
        # 🚀 向量化epochs切片
        n_epochs = n_timepoints // self.n_samples
        if n_epochs == 0:
            return []
        
        # 一次性重塑所有epochs
        valid_length = n_epochs * self.n_samples
        reshaped_data = data[:, :valid_length].reshape(
            self.n_channels, n_epochs, self.n_samples
        )
        
        # 转置为 (n_epochs, n_channels, n_samples)
        epochs = reshaped_data.transpose(1, 0, 2)
        
        return epochs.tolist()
    
    def load_data_parallel(self, split_name):
        """并行加载数据"""
        print(f"🚀 并行加载{split_name}数据...")
        start_time = time.time()
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        labels_df = pd.read_csv(labels_file, sep='\t')
        
        # 准备并行处理参数
        args_list = [(split_dir, row['subject_id'], row['label']) 
                     for _, row in labels_df.iterrows()]
        
        # 🔥 并行处理所有被试
        X_data = []
        y_labels = []
        
        with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
            results = executor.map(self.load_single_subject_fast, args_list)
            
            for epochs, labels in results:
                if epochs is not None and labels is not None:
                    X_data.extend(epochs)
                    y_labels.extend(labels)
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        load_time = time.time() - start_time
        print(f"✅ {split_name}数据加载完成: {X.shape}")
        print(f"⏱️ 加载耗时: {load_time:.2f}秒")
        
        return X, y
    
    def preprocess_ultra_fast(self, X, y=None):
        """超高速预处理"""
        print("⚡ 超高速预处理中...")
        start_time = time.time()
        
        print(f"📊 输入数据形状: {X.shape}")
        
        # 🚀 超级向量化标准化
        # 使用NumPy的广播机制，一次性计算所有统计量
        means = np.mean(X, axis=2, keepdims=True)  # (n_samples, n_channels, 1)
        stds = np.std(X, axis=2, keepdims=True)    # (n_samples, n_channels, 1)
        
        # 避免除零，使用where函数向量化处理
        stds = np.where(stds == 0, 1.0, stds)
        
        # 🚀 一行代码完成所有标准化
        X_processed = (X - means) / stds
        
        # 添加通道维度
        X_processed = np.expand_dims(X_processed, axis=-1)
        
        preprocess_time = time.time() - start_time
        print(f"✅ 超高速预处理完成: {X_processed.shape}")
        print(f"⏱️ 预处理耗时: {preprocess_time:.2f}秒")
        print(f"🎯 标准化统计: 均值={np.mean(X_processed):.6f}, 标准差={np.std(X_processed):.6f}")
        
        if y is not None:
            return X_processed, y
        else:
            return X_processed
    
    def benchmark_preprocessing(self, split_name='train'):
        """预处理性能基准测试"""
        print("🏁 预处理性能基准测试")
        print("=" * 50)
        
        # 测试数据加载速度
        print("📂 测试数据加载速度...")
        start_time = time.time()
        X, y = self.load_data_parallel(split_name)
        load_time = time.time() - start_time
        
        print(f"📊 数据加载结果:")
        print(f"   数据形状: {X.shape}")
        print(f"   加载时间: {load_time:.2f}秒")
        print(f"   加载速度: {X.shape[0]/load_time:.1f} 样本/秒")
        
        # 测试预处理速度
        print("\n🔧 测试预处理速度...")
        start_time = time.time()
        X_processed = self.preprocess_ultra_fast(X)
        preprocess_time = time.time() - start_time
        
        print(f"📊 预处理结果:")
        print(f"   处理形状: {X_processed.shape}")
        print(f"   处理时间: {preprocess_time:.2f}秒")
        print(f"   处理速度: {X.shape[0]/preprocess_time:.1f} 样本/秒")
        
        # 总体性能
        total_time = load_time + preprocess_time
        print(f"\n🏆 总体性能:")
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   总速度: {X.shape[0]/total_time:.1f} 样本/秒")
        
        return X_processed, y
    
    def compare_with_original(self, X_sample):
        """与原始方法对比"""
        print("\n⚖️ 性能对比测试")
        print("-" * 30)
        
        # 原始慢速方法
        print("🐌 测试原始方法...")
        start_time = time.time()
        X_slow = self.preprocess_slow_method(X_sample)
        slow_time = time.time() - start_time
        
        # 超高速方法
        print("⚡ 测试超高速方法...")
        start_time = time.time()
        X_fast = self.preprocess_ultra_fast(X_sample)
        fast_time = time.time() - start_time
        
        # 验证结果一致性
        diff = np.mean(np.abs(X_slow - X_fast))
        
        print(f"📊 对比结果:")
        print(f"   原始方法耗时: {slow_time:.2f}秒")
        print(f"   超高速方法耗时: {fast_time:.2f}秒")
        print(f"   加速倍数: {slow_time/fast_time:.1f}x")
        print(f"   结果差异: {diff:.8f} (应接近0)")
        
        if diff < 1e-6:
            print("✅ 结果完全一致!")
        else:
            print("⚠️ 结果存在微小差异")
    
    def preprocess_slow_method(self, X):
        """原始慢速预处理方法 (用于对比)"""
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 0:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        return np.expand_dims(X_processed, axis=-1)
    
    def memory_efficient_processing(self, split_name, batch_size=1000):
        """内存高效的批量处理"""
        print(f"💾 内存高效批量处理 (batch_size={batch_size})")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        labels_df = pd.read_csv(labels_file, sep='\t')
        
        total_samples = len(labels_df)
        n_batches = (total_samples + batch_size - 1) // batch_size
        
        all_X = []
        all_y = []
        
        for batch_idx in range(n_batches):
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, total_samples)
            
            print(f"📦 处理批次 {batch_idx+1}/{n_batches} ({start_idx}-{end_idx})")
            
            batch_df = labels_df.iloc[start_idx:end_idx]
            args_list = [(split_dir, row['subject_id'], row['label']) 
                        for _, row in batch_df.iterrows()]
            
            # 处理当前批次
            X_batch = []
            y_batch = []
            
            with ProcessPoolExecutor(max_workers=self.n_jobs) as executor:
                results = executor.map(self.load_single_subject_fast, args_list)
                
                for epochs, labels in results:
                    if epochs is not None and labels is not None:
                        X_batch.extend(epochs)
                        y_batch.extend(labels)
            
            if X_batch:
                X_batch = np.array(X_batch)
                X_batch_processed = self.preprocess_ultra_fast(X_batch)
                
                all_X.append(X_batch_processed)
                all_y.extend(y_batch)
        
        # 合并所有批次
        final_X = np.concatenate(all_X, axis=0)
        final_y = np.array(all_y)
        
        print(f"✅ 批量处理完成: {final_X.shape}")
        return final_X, final_y


def main():
    """主函数 - 性能测试"""
    print("⚡ 超高速EEG预处理器性能测试")
    print("=" * 50)
    
    preprocessor = UltraFastEEGPreprocessor()
    
    # 运行基准测试
    X_processed, y = preprocessor.benchmark_preprocessing('train')
    
    # 性能对比 (使用小样本)
    if len(X_processed) > 100:
        sample_X = X_processed[:100]  # 取前100个样本对比
        preprocessor.compare_with_original(sample_X)
    
    print("\n🎉 性能测试完成!")
    print("💡 使用建议:")
    print("   1. 大数据集使用并行加载")
    print("   2. 内存不足时使用批量处理")
    print("   3. 向量化操作替代循环")


if __name__ == "__main__":
    main()
