import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.optimizers import Adam

# 1. 加载现有的模型
model_path = '/kaggle/input/aikxy/tensorflow2/default/1/model.h5'
model = load_model(model_path)

# 2. 设置训练集和验证集路径
train_dir = '/kaggle/working/combined_data/train'
valid_dir = '/kaggle/working/combined_data/validation'

# 3. 使用ImageDataGenerator来预处理图像数据（可以应用数据增强）
train_datagen = ImageDataGenerator(
    rescale=1./255,  # 归一化
    rotation_range=30,  # 旋转角度范围
    width_shift_range=0.2,  # 宽度偏移
    height_shift_range=0.2,  # 高度偏移
    shear_range=0.2,  # 剪切强度
    zoom_range=0.2,  # 缩放范围
    horizontal_flip=True,  # 水平翻转
    fill_mode='nearest'  # 填充模式
)

valid_datagen = ImageDataGenerator(rescale=1./255)  # 验证集只需要归一化

# 4. 加载训练集和验证集图像
train_generator = train_datagen.flow_from_directory(
    train_dir,
    target_size=(148, 148),  # 调整图片大小与模型输入大小一致
    batch_size=32,
    class_mode='categorical'  # 假设是多类分类任务
)

valid_generator = valid_datagen.flow_from_directory(
    valid_dir,
    target_size=(148, 148),  # 同样调整图片大小
    batch_size=32,
    class_mode='categorical'
)

# 5. 编译模型
model.compile(optimizer=Adam(learning_rate=0.0001), loss='categorical_crossentropy', metrics=['accuracy'])

# 6. 继续训练模型
history = model.fit(
    train_generator,
    steps_per_epoch=train_generator.samples // train_generator.batch_size,
    epochs=10,  # 根据需要设置训练轮数
    validation_data=valid_generator,
    validation_steps=valid_generator.samples // valid_generator.batch_size
)

# 7. 保存训练后的模型
model.save('/kaggle/working/updated_model.h5')

# 输出训练过程的损失和准确率
print(f"Training completed. Model saved as '/kaggle/working/updated_model.h5'")