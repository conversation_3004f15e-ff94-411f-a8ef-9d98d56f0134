# -*- coding: utf-8 -*-
"""
从 Untitled-2 提取的HTML模块和分析结果相关代码
包含完整的HTML生成功能和分析结果显示逻辑
"""

import os
import base64
from datetime import datetime
from tkinter import filedialog, messagebox
import customtkinter as ctk

# ==================== 分析结果显示部分 ====================

def create_results_panel(self):
    """创建分析结果显示面板"""
    results_frame = ctk.CTkFrame(self.right_panel)
    results_frame.pack(fill="both", expand=True, padx=20, pady=10)
    
    # 结果标题
    results_title = ctk.CTkLabel(
        results_frame,
        text="📊 分析结果",
        font=ctk.CTkFont(size=16, weight="bold")
    )
    results_title.pack(pady=(15, 10))
    
    # 主要结果显示
    self.result_label = ctk.CTkLabel(
        results_frame,
        text="等待分析...",
        font=ctk.CTkFont(size=14, weight="bold"),
        wraplength=320
    )
    self.result_label.pack(pady=10)
    
    # 置信度显示
    self.confidence_label = ctk.CTkLabel(
        results_frame,
        text="",
        font=ctk.CTkFont(size=12)
    )
    self.confidence_label.pack(pady=5)
    
    # 详细概率显示框
    self.details_frame = ctk.CTkScrollableFrame(results_frame, height=120)
    self.details_frame.pack(fill="x", padx=10, pady=10)

def display_results(self, result_data):
    """显示分析结果"""
    # 保存到历史记录
    self.results_history.append(result_data)
    
    # 显示主要结果 - 简化版本，无警告
    class_name = result_data['predicted_class_name']
    confidence = result_data['confidence']

    # 简化显示，只区分强制分析
    if result_data.get('forced_analysis', False):
        result_text = f"⚡ 强制分析结果:\n{class_name}"
        text_color = "orange"
    else:
        result_text = f"🎯 预测结果:\n{class_name}"
        text_color = "white"

    self.result_label.configure(
        text=result_text,
        text_color=text_color
    )

    self.confidence_label.configure(
        text=f"🎯 置信度: {confidence:.2%}",
        text_color="lightblue"
    )
    
    # 清空详细结果框
    for widget in self.details_frame.winfo_children():
        widget.destroy()
        
    # 显示详细概率
    for i, prob in enumerate(result_data['probabilities']):
        prob_frame = ctk.CTkFrame(self.details_frame)
        prob_frame.pack(fill="x", pady=2)
        
        prob_label = ctk.CTkLabel(
            prob_frame,
            text=f"{self.class_labels[i]}: {prob:.2%}",
            font=ctk.CTkFont(size=11)
        )
        prob_label.pack(side="left", padx=10, pady=5)
        
        # 概率条
        prob_bar = ctk.CTkProgressBar(prob_frame, width=100, height=10)
        prob_bar.pack(side="right", padx=10, pady=5)
        prob_bar.set(prob)
    
    # 启用功能按钮
    self.save_btn.configure(state="normal")
    self.pdf_btn.configure(state="normal")
    self.html_btn.configure(state="normal")
    self.progress.set(0)

    # 更新状态
    self.status_label.configure(text="✅ 分析完成")

def save_results(self):
    """保存分析结果"""
    if not self.results_history:
        messagebox.showwarning("警告", "没有可保存的结果")
        return
        
    file_path = filedialog.asksaveasfilename(
        title="保存分析结果",
        defaultextension=".json",
        filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt")]
    )
    
    if file_path:
        try:
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.results_history, f, ensure_ascii=False, indent=2)
            messagebox.showinfo("成功", "分析结果已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败：{e}")

# ==================== HTML生成模块 ====================

def generate_html_report(self):
    """生成HTML可视化报告"""
    if not self.results_history:
        messagebox.showwarning("警告", "没有可生成报告的结果")
        return

    # 获取最新结果
    latest_result = self.results_history[-1]

    # 让用户选择保存位置和文件名
    default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

    file_path = filedialog.asksaveasfilename(
        title="保存HTML报告",
        defaultextension=".html",
        initialname=default_name,
        filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
    )

    if file_path:
        try:
            self.create_html_report(latest_result, file_path)
            messagebox.showinfo("成功", f"HTML报告已生成：{file_path}")

            # 询问是否打开报告
            if messagebox.askyesno("打开报告", "是否立即在浏览器中打开HTML报告？"):
                import webbrowser
                webbrowser.open(f"file://{os.path.abspath(file_path)}")

        except Exception as e:
            messagebox.showerror("错误", f"HTML报告生成失败：{e}")

def create_html_report(self, result_data, file_path):
    """创建HTML可视化报告"""
    try:
        # 读取图像并转换为base64
        image_base64 = ""
        try:
            with open(result_data['image_path'], 'rb') as img_file:
                image_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                image_ext = os.path.splitext(result_data['image_path'])[1].lower()
                if image_ext in ['.jpg', '.jpeg']:
                    image_mime = 'image/jpeg'
                elif image_ext == '.png':
                    image_mime = 'image/png'
                else:
                    image_mime = 'image/jpeg'
        except:
            pass

        # 生成概率分布图表数据
        chart_data = []
        for i, prob in enumerate(result_data['probabilities']):
            class_name = self.class_labels[i].split('(')[1].replace(')', '') if '(' in self.class_labels[i] else self.class_labels[i]
            chart_data.append({'name': class_name, 'value': prob * 100})

        # HTML模板
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI痴呆症诊断报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .info-section {{
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }}
        .result-section {{
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }}
        .chart-section {{
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }}
        .recommendation-section {{
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
        }}
        .disclaimer-section {{
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-left: 5px solid #e17055;
        }}
        .section h2 {{
            color: #2d3436;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
            margin-top: 0;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .analysis-image {{
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .result-highlight {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3436;
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 20px 0;
        }}
        .confidence-bar {{
            width: 100%;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }}
        .confidence-fill {{
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9, #74b9ff);
            border-radius: 15px;
            transition: width 0.5s ease;
        }}
        .chart-container {{
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }}
        .recommendation-list {{
            list-style: none;
            padding: 0;
        }}
        .recommendation-list li {{
            background: rgba(255,255,255,0.7);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #00b894;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            background: #2d3436;
            color: white;
        }}
        @media print {{
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI痴呆症诊断报告</h1>
            <p>基于深度学习的医学影像智能分析</p>
        </div>

        <div class="content">
            <div class="section info-section">
                <h2>📋 基本信息</h2>
                <p><strong>分析时间:</strong> {result_data['timestamp']}</p>
                <p><strong>图像文件:</strong> {os.path.basename(result_data['image_path'])}</p>
                <p><strong>分析模型:</strong> AI痴呆症检测模型 v1.0</p>
                {"<p><strong>图像验证:</strong> " + result_data.get('validation_info', {}).get('confidence', '未知') + "置信度</p>" if 'validation_info' in result_data else ""}
            </div>

            {"<div class='image-container'><img src='data:" + image_mime + ";base64," + image_base64 + "' alt='分析图像' class='analysis-image'></div>" if image_base64 else ""}

            <div class="section result-section">
                <h2>🎯 AI分析结果</h2>
                <div class="result-highlight">
                    预测分类: {result_data['predicted_class_name']}
                </div>
                <p><strong>置信度:</strong> {result_data['confidence']:.2%}</p>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: {result_data['confidence']*100}%"></div>
                </div>
                {"<p style='color: red;'><strong>⚠️ 注意:</strong> 此结果为强制分析，图像验证未通过</p>" if result_data.get('forced_analysis', False) else ""}
            </div>

            <div class="section chart-section">
                <h2>📊 详细概率分布</h2>
                <div class="chart-container">
                    <canvas id="probabilityChart"></canvas>
                </div>
            </div>

            <div class="section recommendation-section">
                <h2>🏥 综合诊断与建议</h2>
"""

        # 添加诊断内容
        predicted_class = result_data['predicted_class_name']
        if "NonDemented" in predicted_class:
            html_content += """
                <div class="result-highlight" style="color: #00b894;">
                    风险等级: 低风险 ✅
                </div>
                <p>根据AI分析，未检测到明显的痴呆症状。大脑结构显示正常特征。</p>
                <ul class="recommendation-list">
                    <li>🏃‍♂️ 继续保持规律的体育锻炼</li>
                    <li>🥗 维持健康的饮食习惯</li>
                    <li>🧠 保持智力活动和社交互动</li>
                    <li>🏥 定期进行健康检查</li>
                </ul>
"""
        elif "VeryMild" in predicted_class:
            html_content += """
                <div class="result-highlight" style="color: #fdcb6e;">
                    风险等级: 轻微风险 ⚠️
                </div>
                <p>检测到非常轻微的认知变化。这可能是正常老化过程，但建议进行更详细的评估。</p>
                <ul class="recommendation-list">
                    <li>👨‍⚕️ 建议咨询神经科医生</li>
                    <li>🧪 进行详细的神经心理学评估</li>
                    <li>📅 定期随访观察变化</li>
                    <li>🧘‍♀️ 加强认知训练活动</li>
                </ul>
"""
