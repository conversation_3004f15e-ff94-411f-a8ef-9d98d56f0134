# -*- coding: utf-8 -*-
#!/usr/bin/env python
"""
AI痴呆症识别程序 - IDE友好版本
直接点击运行按钮即可使用
"""

import os
import warnings
import sys

# 设置环境变量来隐藏TensorFlow的日志信息（必须在导入TensorFlow之前设置）
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 隐藏INFO和WARNING信息
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # 关闭oneDNN优化信息

# 隐藏其他警告信息
warnings.filterwarnings('ignore')

def main():
    """主函数"""
    print("🧠 AI痴呆症识别程序启动中...")
    print("=" * 50)
    
    # 检查依赖
    try:
        print("📦 正在加载TensorFlow...")
        import tensorflow as tf
        from tensorflow.keras.preprocessing import image
        import numpy as np
        
        # 设置TensorFlow日志级别
        tf.get_logger().setLevel('ERROR')
        
        # 隐藏absl警告
        import absl.logging
        absl.logging.set_verbosity(absl.logging.ERROR)
        
        print(f"✅ TensorFlow加载成功 (版本: {tf.__version__})")
        
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {e}")
        print("💡 解决方案: 请安装TensorFlow")
        print("   命令: pip install tensorflow")
        input("按Enter键退出...")
        return
    
    # 检查文件路径
    model_path = r"D:\模型开发\升级model.h5"
    image_path = r"D:\模型开发\1.jpg"
    
    print(f"📁 检查文件...")
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("💡 请确认模型文件路径是否正确")
        input("按Enter键退出...")
        return
        
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        print("💡 请确认图片文件路径是否正确")
        input("按Enter键退出...")
        return
    
    print("✅ 所有文件检查通过")
    
    try:
        # 加载模型
        print("🤖 正在加载AI模型...")
        model = tf.keras.models.load_model(model_path)
        print("✅ 模型加载成功")
        
        # 类别标签映射
        class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 图像预处理函数
        def load_and_preprocess_image(img_path):
            img = image.load_img(img_path, target_size=(150, 150))
            img_array = image.img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0
            return img_array, img
        
        # 处理图像
        print("🖼️ 正在处理图像...")
        processed_image, original_image = load_and_preprocess_image(image_path)
        print("✅ 图像处理完成")
        
        # 进行预测
        print("🔮 正在进行AI预测...")
        predictions = model.predict(processed_image)
        predicted_class = np.argmax(predictions, axis=1)
        prediction_probs = predictions[0].tolist()
        predicted_class_name = class_labels[predicted_class[0]]
        confidence = max(prediction_probs)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎯 AI预测结果")
        print("=" * 50)
        print(f"📊 预测类别: {predicted_class_name}")
        print(f"🎯 置信度: {confidence:.2%}")
        print("\n📈 各类别概率:")
        for i, prob in enumerate(prediction_probs):
            print(f"   {class_labels[i]}: {prob:.4f} ({prob:.2%})")
        
        print("\n" + "=" * 50)
        print("✅ 预测完成！")
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("💡 请检查模型文件和图片文件是否正确")
    
    # 等待用户输入（防止窗口立即关闭）
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
