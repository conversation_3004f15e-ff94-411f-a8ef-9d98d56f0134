# 🖥️ 屏幕截图功能使用说明

## 🎯 功能概述

双模型系统现在支持屏幕截图功能，可以直接捕获屏幕上的医学影像进行AI分析。

## 🚀 主要特性

### 📺 **全屏截图**
- 捕获整个屏幕内容
- 适合分析全屏显示的医学影像
- 一键操作，简单快捷

### 🎯 **区域截图**
- 精确选择屏幕区域
- 适合捕获特定的影像区域
- 避免无关内容干扰

### 🔄 **智能窗口管理**
- 截图时自动隐藏应用窗口
- 避免截图包含应用界面
- 截图完成后自动恢复窗口

## 🎮 使用步骤

### 1. 启动屏幕截图
- 点击 **"🖥️ 屏幕截图分析"** 按钮（橙色）
- 应用窗口会自动隐藏
- 弹出截图选择窗口

### 2. 选择截图方式

#### 📺 全屏截图
1. 点击 **"📺 全屏截图"** 按钮
2. 系统自动截取整个屏幕
3. 图像自动保存并显示在应用中
4. 可立即开始AI分析

#### 🎯 区域截图
1. 点击 **"🎯 选择区域截图"** 按钮
2. 阅读操作说明
3. 点击 **"🎯 开始选择区域"**
4. 按提示点击区域的左上角
5. 再点击区域的右下角
6. 系统自动截取选定区域

### 3. 开始分析
- 截图完成后，图像会显示在左侧面板
- **"🔍 开始AI分析"** 按钮变为可用
- 点击开始双模型分析（CT检测 + 痴呆症分析）

## 🎨 界面状态

### 截图选择窗口
- **📺 全屏截图** (蓝色) - 捕获整个屏幕
- **🎯 选择区域截图** (绿色) - 精确选择区域
- **❌ 取消** (红色) - 取消截图操作

### 区域选择说明
- 详细的操作指导
- **🎯 开始选择区域** (绿色) - 开始区域选择
- **❌ 取消** (红色) - 取消操作

## 🔧 技术特性

### 自动窗口管理
- 截图前自动隐藏主窗口
- 避免应用界面出现在截图中
- 截图完成后自动恢复窗口显示

### 智能文件管理
- 截图自动保存到系统临时文件夹
- 文件名包含时间戳：`screenshot_YYYYMMDD_HHMMSS.png`
- 区域截图：`region_screenshot_YYYYMMDD_HHMMSS.png`

### 错误处理
- 检测pyautogui库是否安装
- 提供详细的错误提示
- 自动恢复窗口状态

## 💡 使用场景

### 医学影像分析
1. **PACS系统截图**: 直接从医院PACS系统截取CT影像
2. **文档图像**: 截取PDF或文档中的医学影像
3. **网页内容**: 从医学网站截取影像资料
4. **演示文稿**: 从PPT或其他演示文稿截取影像

### 教学和研究
1. **教学材料**: 截取教学软件中的影像
2. **研究资料**: 从研究文献截取影像
3. **对比分析**: 截取多个影像进行对比

## 🎯 最佳实践

### 全屏截图
- 适合单一大图像的分析
- 确保屏幕上只显示目标影像
- 关闭不必要的窗口和通知

### 区域截图
- 适合精确捕获特定区域
- 选择区域时确保包含完整的影像内容
- 避免选择过小的区域影响分析质量

### 图像质量
- 确保屏幕分辨率足够高
- 避免截图时有遮挡或反光
- 选择对比度良好的显示设置

## 🔄 工作流程

```
点击"屏幕截图分析" → 选择截图方式 → 执行截图
    ↓
图像显示在应用中 → 点击"开始AI分析"
    ↓
CT检测 + 痴呆症分析 → 查看结果 → 生成报告
```

## ⚠️ 注意事项

### 系统要求
- 需要安装pyautogui库（已自动安装）
- Windows系统支持
- 需要屏幕访问权限

### 使用建议
- 截图前确保目标内容清晰可见
- 避免在截图过程中移动鼠标
- 区域选择时要准确点击目标位置

### 隐私安全
- 截图文件保存在临时文件夹
- 建议分析完成后及时清理临时文件
- 注意保护患者隐私信息

---

现在你的双模型系统具备了完整的屏幕截图功能！可以直接从屏幕捕获医学影像进行AI分析。
