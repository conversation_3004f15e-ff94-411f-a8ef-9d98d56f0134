"""
快速测试训练器 - 验证基本功能
"""

import pandas as pd
import numpy as np
import os
import time

print("⚡ 快速测试训练器")
print(f"⏰ 开始: {time.strftime('%H:%M:%S')}")

# 测试数据加载
data_path = r"D:\模型开发\audio\processed_datasets"
print(f"📂 数据路径: {data_path}")

try:
    print("📊 测试数据加载...")
    
    # 检查文件是否存在
    train_file = os.path.join(data_path, "train_set_scaled.csv")
    test_file = os.path.join(data_path, "test_set_scaled.csv")
    
    print(f"   训练文件存在: {os.path.exists(train_file)}")
    print(f"   测试文件存在: {os.path.exists(test_file)}")
    
    if os.path.exists(train_file) and os.path.exists(test_file):
        # 加载数据
        train_data = pd.read_csv(train_file)
        test_data = pd.read_csv(test_file)
        
        print(f"✅ 数据加载成功!")
        print(f"   训练集形状: {train_data.shape}")
        print(f"   测试集形状: {test_data.shape}")
        
        # 检查列
        print(f"   训练集列数: {len(train_data.columns)}")
        print(f"   前5列: {list(train_data.columns[:5])}")
        
        # 检查标签
        if 'diagnosis_encoded' in train_data.columns:
            labels = train_data['diagnosis_encoded'].unique()
            print(f"   标签类别: {labels}")
            print(f"   标签分布: {train_data['diagnosis_encoded'].value_counts().to_dict()}")
        
        # 准备特征
        feature_cols = [col for col in train_data.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
        print(f"   特征数量: {len(feature_cols)}")
        
        X_train = train_data[feature_cols].values
        y_train = train_data['diagnosis_encoded'].values
        X_test = test_data[feature_cols].values
        y_test = test_data['diagnosis_encoded'].values
        
        print(f"   X_train形状: {X_train.shape}")
        print(f"   y_train形状: {y_train.shape}")
        print(f"   X_test形状: {X_test.shape}")
        print(f"   y_test形状: {y_test.shape}")
        
        # 快速训练一个简单模型
        print("\n🚀 快速训练测试...")
        
        try:
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.metrics import accuracy_score
            
            print("   创建随机森林...")
            rf = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
            
            print("   训练中...")
            rf.fit(X_train, y_train)
            
            print("   预测中...")
            pred = rf.predict(X_test)
            
            accuracy = accuracy_score(y_test, pred)
            print(f"✅ 快速测试完成!")
            print(f"   准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
            
            if accuracy >= 0.7:
                print("🎯 基本功能正常，可以进行完整训练!")
            else:
                print("⚠️ 准确率较低，需要检查数据质量")
            
        except Exception as e:
            print(f"❌ 模型训练失败: {e}")
        
        # 测试保存功能
        print("\n💾 测试保存功能...")
        output_path = r"D:\模型开发\audio"
        
        try:
            os.makedirs(output_path, exist_ok=True)
            
            # 创建测试文件
            test_file_path = os.path.join(output_path, "test_output.txt")
            with open(test_file_path, "w", encoding='utf-8') as f:
                f.write(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"数据加载: 成功\n")
                f.write(f"模型训练: 成功\n")
                f.write(f"准确率: {accuracy:.4f}\n")
            
            print(f"✅ 保存测试成功!")
            print(f"   测试文件: {test_file_path}")
            
            # 删除测试文件
            os.remove(test_file_path)
            print("   测试文件已清理")
            
        except Exception as e:
            print(f"❌ 保存测试失败: {e}")
    
    else:
        print("❌ 数据文件不存在!")
        print("   请检查数据路径是否正确")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print(f"\n⏰ 结束: {time.strftime('%H:%M:%S')}")
print("⚡ 快速测试完成")
