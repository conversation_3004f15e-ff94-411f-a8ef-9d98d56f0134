{"summary_time": "2025-06-24 12:37:10", "total_models_found": 2, "best_overall_accuracy": 0.6953333333333334, "best_dementia_recall": 0, "targets_achieved": {"90_percent_accuracy": false, "85_percent_medical": false, "dementia_detection_solved": false, "dementia_detection_improved": false}, "all_results": [{"file": "w\\model_info.json", "directory": "w", "data": {"model_type": "neural_network", "accuracy": 0.6953333333333334, "target_achieved": false, "classes": ["Dementia", "MCI", "Normal"]}, "accuracy": 0.6953333333333334, "method": "neural_network", "dementia_recall": 0, "dementia_precision": 0}, {"file": "trained_audio_models\\model_evaluation.json", "directory": "trained_audio_models", "data": {"model_info": {"total_parameters": 35795, "feature_dimensions": 38, "classes": ["Dementia", "MCI", "Normal"], "training_samples": 7004, "validation_samples": 1496, "test_samples": 1500}, "performance": {"test_accuracy": 0.6906666666666667, "classification_report": {"Dementia": {"precision": 0.0, "recall": 0.0, "f1-score": 0.0, "support": 36.0}, "MCI": {"precision": 0.693069306930693, "recall": 0.7730061349693251, "f1-score": 0.7308584686774942, "support": 815.0}, "Normal": {"precision": 0.6869712351945855, "recall": 0.6255778120184899, "f1-score": 0.6548387096774193, "support": 649.0}, "accuracy": 0.6906666666666667, "macro avg": {"precision": 0.46001351404175955, "recall": 0.4661946489959384, "f1-score": 0.46189905945163784, "support": 1500.0}, "weighted avg": {"precision": 0.6737972111932006, "recall": 0.6906666666666667, "f1-score": 0.6804266497018686, "support": 1500.0}}}, "feature_groups": {"demographic": ["age", "gender"], "acoustic": ["f0_mean", "f0_std", "f0_range", "jitter_percent", "shimmer_percent", "hnr_db", "spectral_centroid", "spectral_bandwidth", "spectral_rolloff", "zero_crossing_rate"], "mfcc": ["mfcc_1", "mfcc_2", "mfcc_3", "mfcc_4", "mfcc_5", "mfcc_6", "mfcc_7", "mfcc_8", "mfcc_9", "mfcc_10", "mfcc_11", "mfcc_12", "mfcc_13"], "linguistic": ["education_years", "speech_rate_wpm", "pause_frequency", "pause_duration_mean", "total_words", "unique_words", "type_token_ratio", "information_units", "efficiency_ratio", "coherence_score", "filled_pauses", "repetitions", "semantic_errors"]}}, "accuracy": 0.6906666666666667, "method": "Neural Network", "dementia_recall": 0.0, "dementia_precision": 0.0}]}