"""
🧠 EEG数据集创建器
基于真实EEG_extracted数据，创建训练所需的不同种类数据集
"""

import os
import json
import pandas as pd
import numpy as np
from collections import Counter
import shutil

class EEGDatasetCreator:
    """EEG数据集创建器"""
    
    def __init__(self):
        self.base_path = "EEG_extracted/dataset"
        self.output_path = "EEG_training_datasets"
        
        # 创建输出目录
        os.makedirs(self.output_path, exist_ok=True)
        
        print("🧠 EEG数据集创建器")
        print("=" * 60)
        print(f"📁 源数据路径: {self.base_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def analyze_metadata(self):
        """分析元数据获取标签信息"""
        print("\n📊 第一步：分析元数据...")
        
        # 检查participants.tsv文件
        participants_file = os.path.join(self.base_path, "participants.tsv")
        
        if os.path.exists(participants_file):
            print(f"✅ 找到participants.tsv文件")
            
            # 读取参与者信息
            df = pd.read_csv(participants_file, sep='\t')
            print(f"📋 参与者信息:")
            print(f"   总参与者数: {len(df)}")
            print(f"   列名: {list(df.columns)}")
            print(f"   前5行数据:")
            print(df.head())
            
            # 分析标签分布
            if 'group' in df.columns:
                group_counts = df['group'].value_counts()
                print(f"\n📊 组别分布:")
                for group, count in group_counts.items():
                    print(f"   {group}: {count} 人")
                return df
            elif 'diagnosis' in df.columns:
                diag_counts = df['diagnosis'].value_counts()
                print(f"\n📊 诊断分布:")
                for diag, count in diag_counts.items():
                    print(f"   {diag}: {count} 人")
                return df
            else:
                print(f"⚠️ 未找到明确的标签列")
                print(f"   可用列: {list(df.columns)}")
                return df
        else:
            print(f"❌ 未找到participants.tsv文件")
            return None
    
    def check_data_availability(self):
        """检查数据可用性"""
        print("\n🔍 第二步：检查数据可用性...")
        
        # 检查原始数据
        raw_subjects = []
        if os.path.exists(self.base_path):
            for item in os.listdir(self.base_path):
                if item.startswith('sub-') and os.path.isdir(os.path.join(self.base_path, item)):
                    raw_subjects.append(item)
        
        # 检查预处理数据
        derivatives_path = os.path.join(self.base_path, "derivatives")
        processed_subjects = []
        if os.path.exists(derivatives_path):
            for item in os.listdir(derivatives_path):
                if item.startswith('sub-') and os.path.isdir(os.path.join(derivatives_path, item)):
                    processed_subjects.append(item)
        
        print(f"📁 原始数据: {len(raw_subjects)} 个受试者")
        print(f"📁 预处理数据: {len(processed_subjects)} 个受试者")
        
        # 检查数据完整性
        complete_subjects = []
        for subject in raw_subjects:
            raw_eeg = os.path.join(self.base_path, subject, "eeg", f"{subject}_task-eyesclosed_eeg.set")
            processed_eeg = os.path.join(derivatives_path, subject, "eeg", f"{subject}_task-eyesclosed_eeg.set")
            
            if os.path.exists(raw_eeg) and os.path.exists(processed_eeg):
                complete_subjects.append(subject)
        
        print(f"✅ 完整数据: {len(complete_subjects)} 个受试者")
        
        return {
            'raw_subjects': raw_subjects,
            'processed_subjects': processed_subjects,
            'complete_subjects': complete_subjects
        }
    
    def create_training_splits(self, participants_df, data_info):
        """创建训练数据集划分"""
        print("\n📊 第三步：创建训练数据集划分...")
        
        if participants_df is None:
            print("❌ 没有参与者信息，无法创建标签")
            return None
        
        # 获取标签列
        label_column = None
        if 'group' in participants_df.columns:
            label_column = 'group'
        elif 'diagnosis' in participants_df.columns:
            label_column = 'diagnosis'
        elif 'condition' in participants_df.columns:
            label_column = 'condition'
        else:
            # 尝试其他可能的标签列
            for col in participants_df.columns:
                if col.lower() in ['label', 'class', 'category', 'type']:
                    label_column = col
                    break
        
        if label_column is None:
            print("❌ 未找到标签列")
            return None
        
        print(f"📋 使用标签列: {label_column}")
        
        # 只保留有完整数据的受试者
        complete_df = participants_df[participants_df['participant_id'].isin(
            [f"sub-{s.split('-')[1]}" for s in data_info['complete_subjects']]
        )].copy()
        
        print(f"📊 可用于训练的数据: {len(complete_df)} 个受试者")
        
        # 按标签分层划分
        from sklearn.model_selection import train_test_split
        
        # 第一次划分：训练集 + 临时集
        train_df, temp_df = train_test_split(
            complete_df, 
            test_size=0.4, 
            random_state=42, 
            stratify=complete_df[label_column]
        )
        
        # 第二次划分：验证集 + 测试集
        val_df, test_df = train_test_split(
            temp_df, 
            test_size=0.5, 
            random_state=42, 
            stratify=temp_df[label_column]
        )
        
        splits = {
            'train': train_df,
            'val': val_df,
            'test': test_df
        }
        
        # 显示划分结果
        print(f"\n📊 数据集划分结果:")
        for split_name, split_df in splits.items():
            print(f"   {split_name.upper()}集: {len(split_df)} 个受试者")
            label_counts = split_df[label_column].value_counts()
            for label, count in label_counts.items():
                print(f"     {label}: {count} 人")
        
        return splits, label_column
    
    def create_dataset_files(self, splits, label_column):
        """创建数据集文件"""
        print("\n📁 第四步：创建数据集文件...")
        
        for split_name, split_df in splits.items():
            split_dir = os.path.join(self.output_path, split_name)
            os.makedirs(split_dir, exist_ok=True)
            
            print(f"\n📂 创建 {split_name.upper()} 集...")
            
            # 创建标签文件
            labels_file = os.path.join(split_dir, 'labels.txt')
            with open(labels_file, 'w', encoding='utf-8') as f:
                f.write("subject_id\tlabel\n")
                for _, row in split_df.iterrows():
                    subject_id = row['participant_id']
                    label = row[label_column]
                    f.write(f"{subject_id}\t{label}\n")
            
            print(f"   ✅ 创建标签文件: {labels_file}")
            
            # 复制EEG数据文件（使用预处理版本）
            copied_count = 0
            for _, row in split_df.iterrows():
                subject_id = row['participant_id']
                
                # 源文件路径（预处理版本）
                src_file = os.path.join(
                    self.base_path, 
                    "derivatives", 
                    subject_id, 
                    "eeg", 
                    f"{subject_id}_task-eyesclosed_eeg.set"
                )
                
                # 目标文件路径
                dst_file = os.path.join(split_dir, f"{subject_id}_task-eyesclosed_eeg.set")
                
                if os.path.exists(src_file):
                    shutil.copy2(src_file, dst_file)
                    copied_count += 1
                else:
                    print(f"   ⚠️ 文件不存在: {src_file}")
            
            print(f"   ✅ 复制EEG文件: {copied_count}/{len(split_df)} 个")
            
            # 创建数据集信息文件
            info_file = os.path.join(split_dir, 'dataset_info.json')
            info_data = {
                'split_name': split_name,
                'total_subjects': len(split_df),
                'label_column': label_column,
                'label_distribution': split_df[label_column].value_counts().to_dict(),
                'subjects': split_df['participant_id'].tolist(),
                'data_source': 'EEG_extracted/dataset/derivatives (preprocessed)',
                'file_format': 'EEGLAB .set files',
                'task': 'eyes-closed resting state'
            }
            
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(info_data, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ 创建信息文件: {info_file}")
    
    def create_summary_report(self, splits, label_column):
        """创建总结报告"""
        print("\n📋 第五步：创建总结报告...")
        
        summary = {
            'dataset_creation_summary': {
                'total_splits': len(splits),
                'label_column_used': label_column,
                'data_source': 'EEG_extracted/dataset/derivatives',
                'file_format': 'EEGLAB .set files',
                'task_type': 'eyes-closed resting state EEG'
            },
            'split_details': {}
        }
        
        total_subjects = 0
        overall_distribution = Counter()
        
        for split_name, split_df in splits.items():
            split_info = {
                'subject_count': len(split_df),
                'label_distribution': split_df[label_column].value_counts().to_dict(),
                'subjects': split_df['participant_id'].tolist()
            }
            
            summary['split_details'][split_name] = split_info
            total_subjects += len(split_df)
            overall_distribution.update(split_df[label_column].value_counts().to_dict())
        
        summary['overall_statistics'] = {
            'total_subjects': total_subjects,
            'overall_label_distribution': dict(overall_distribution)
        }
        
        # 保存总结报告
        summary_file = os.path.join(self.output_path, 'dataset_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 总结报告已保存: {summary_file}")
        
        # 显示总结
        print(f"\n📊 数据集创建总结:")
        print(f"   总受试者数: {total_subjects}")
        print(f"   标签列: {label_column}")
        print(f"   整体分布: {dict(overall_distribution)}")
        print(f"   输出目录: {self.output_path}")
        
        return summary
    
    def run_dataset_creation(self):
        """运行完整的数据集创建流程"""
        print("🚀 开始EEG数据集创建流程")
        print("=" * 60)
        
        try:
            # 1. 分析元数据
            participants_df = self.analyze_metadata()
            
            # 2. 检查数据可用性
            data_info = self.check_data_availability()
            
            # 3. 创建训练划分
            result = self.create_training_splits(participants_df, data_info)
            if result is None:
                print("❌ 无法创建数据集划分")
                return False
            
            splits, label_column = result
            
            # 4. 创建数据集文件
            self.create_dataset_files(splits, label_column)
            
            # 5. 创建总结报告
            summary = self.create_summary_report(splits, label_column)
            
            print(f"\n🎉 EEG数据集创建完成!")
            print("=" * 60)
            print(f"📁 输出目录: {self.output_path}")
            print(f"📋 包含: train/, val/, test/ 三个数据集")
            print(f"📄 每个数据集包含: .set文件 + labels.txt + dataset_info.json")
            print(f"📊 总结报告: dataset_summary.json")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据集创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🧠 EEG训练数据集创建系统")
    print("基于真实EEG_extracted数据创建训练所需的数据集")
    print()
    
    # 创建数据集创建器
    creator = EEGDatasetCreator()
    
    # 运行数据集创建
    success = creator.run_dataset_creation()
    
    if success:
        print("\n🏆 数据集创建成功!")
        print("📋 现在可以使用这些数据集训练EEG模型了")
    else:
        print("\n❌ 数据集创建失败")


if __name__ == "__main__":
    main()
