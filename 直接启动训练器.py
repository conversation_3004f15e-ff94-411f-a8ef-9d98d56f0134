"""
直接启动训练器 - 立即开始5小时训练
专注于85-90%+准确率和医疗指标
"""

import pandas as pd
import numpy as np
import os
import time
import warnings
warnings.filterwarnings('ignore')

print("🏥 直接启动训练器")
print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("🎯 目标: 85-90%+ 准确率")
print("=" * 50)

# 记录开始时间
start_time = time.time()
training_duration = 5 * 3600  # 5小时

# 数据路径
data_path = r"D:\模型开发\audio\processed_datasets"
output_path = r"D:\模型开发\audio"

print("📊 加载数据...")
try:
    train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
    val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
    test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))
    
    print(f"✅ 数据加载成功")
    print(f"   训练集: {len(train_data)} 样本")
    print(f"   验证集: {len(val_data)} 样本")
    print(f"   测试集: {len(test_data)} 样本")
    
except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    exit(1)

# 合并训练和验证集
combined_train = pd.concat([train_data, val_data], ignore_index=True)
print(f"   合并训练集: {len(combined_train)} 样本")

# 准备特征和标签
feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
X_train = combined_train[feature_cols].values
y_train = combined_train['diagnosis_encoded'].values
X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"🔧 数据准备完成")
print(f"   特征维度: {X_train.shape[1]}")
print(f"   类别分布: {np.bincount(y_train)}")

# 开始训练多个模型
models = {}
results = {}

print("\n🚀 开始模型训练...")

# 1. 随机森林
print("🌲 训练随机森林...")
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score
    
    rf = RandomForestClassifier(
        n_estimators=300,
        max_depth=20,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42,
        n_jobs=-1,
        class_weight='balanced'
    )
    
    rf.fit(X_train, y_train)
    rf_pred = rf.predict(X_test)
    
    rf_acc = accuracy_score(y_test, rf_pred)
    rf_f1 = f1_score(y_test, rf_pred, average='weighted')
    rf_recall = recall_score(y_test, rf_pred, average='weighted')
    rf_precision = precision_score(y_test, rf_pred, average='weighted')
    
    models['RandomForest'] = rf
    results['RandomForest'] = {
        'accuracy': rf_acc,
        'f1_score': rf_f1,
        'recall': rf_recall,
        'precision': rf_precision
    }
    
    print(f"   随机森林准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")
    print(f"   F1分数: {rf_f1:.4f}")
    print(f"   召回率: {rf_recall:.4f}")
    
except Exception as e:
    print(f"   随机森林训练失败: {e}")

# 2. 梯度提升
print("\n📈 训练梯度提升...")
try:
    from sklearn.ensemble import GradientBoostingClassifier
    
    gb = GradientBoostingClassifier(
        n_estimators=300,
        learning_rate=0.1,
        max_depth=7,
        subsample=0.8,
        random_state=42
    )
    
    gb.fit(X_train, y_train)
    gb_pred = gb.predict(X_test)
    
    gb_acc = accuracy_score(y_test, gb_pred)
    gb_f1 = f1_score(y_test, gb_pred, average='weighted')
    gb_recall = recall_score(y_test, gb_pred, average='weighted')
    gb_precision = precision_score(y_test, gb_pred, average='weighted')
    
    models['GradientBoosting'] = gb
    results['GradientBoosting'] = {
        'accuracy': gb_acc,
        'f1_score': gb_f1,
        'recall': gb_recall,
        'precision': gb_precision
    }
    
    print(f"   梯度提升准确率: {gb_acc:.4f} ({gb_acc*100:.2f}%)")
    print(f"   F1分数: {gb_f1:.4f}")
    print(f"   召回率: {gb_recall:.4f}")
    
except Exception as e:
    print(f"   梯度提升训练失败: {e}")

# 3. 极端随机树
print("\n🌳 训练极端随机树...")
try:
    from sklearn.ensemble import ExtraTreesClassifier
    
    et = ExtraTreesClassifier(
        n_estimators=300,
        max_depth=20,
        min_samples_split=2,
        min_samples_leaf=1,
        random_state=42,
        n_jobs=-1,
        class_weight='balanced'
    )
    
    et.fit(X_train, y_train)
    et_pred = et.predict(X_test)
    
    et_acc = accuracy_score(y_test, et_pred)
    et_f1 = f1_score(y_test, et_pred, average='weighted')
    et_recall = recall_score(y_test, et_pred, average='weighted')
    et_precision = precision_score(y_test, et_pred, average='weighted')
    
    models['ExtraTrees'] = et
    results['ExtraTrees'] = {
        'accuracy': et_acc,
        'f1_score': et_f1,
        'recall': et_recall,
        'precision': et_precision
    }
    
    print(f"   极端随机树准确率: {et_acc:.4f} ({et_acc*100:.2f}%)")
    print(f"   F1分数: {et_f1:.4f}")
    print(f"   召回率: {et_recall:.4f}")
    
except Exception as e:
    print(f"   极端随机树训练失败: {e}")

# 4. 投票集成
print("\n🗳️ 创建投票集成...")
try:
    from sklearn.ensemble import VotingClassifier
    
    # 选择表现最好的模型进行集成
    available_models = [(name, model) for name, model in models.items()]
    
    if len(available_models) >= 2:
        voting_clf = VotingClassifier(
            estimators=available_models,
            voting='soft'
        )
        
        voting_clf.fit(X_train, y_train)
        voting_pred = voting_clf.predict(X_test)
        
        voting_acc = accuracy_score(y_test, voting_pred)
        voting_f1 = f1_score(y_test, voting_pred, average='weighted')
        voting_recall = recall_score(y_test, voting_pred, average='weighted')
        voting_precision = precision_score(y_test, voting_pred, average='weighted')
        
        models['VotingEnsemble'] = voting_clf
        results['VotingEnsemble'] = {
            'accuracy': voting_acc,
            'f1_score': voting_f1,
            'recall': voting_recall,
            'precision': voting_precision
        }
        
        print(f"   投票集成准确率: {voting_acc:.4f} ({voting_acc*100:.2f}%)")
        print(f"   F1分数: {voting_f1:.4f}")
        print(f"   召回率: {voting_recall:.4f}")
    
except Exception as e:
    print(f"   投票集成创建失败: {e}")

# 5. 深度学习模型
print("\n🧠 训练深度学习模型...")
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    
    # 抑制TensorFlow警告
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    # 构建模型
    nn_model = Sequential([
        Dense(128, activation='relu', input_shape=(X_train.shape[1],)),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(64, activation='relu'),
        BatchNormalization(),
        Dropout(0.2),
        
        Dense(32, activation='relu'),
        BatchNormalization(),
        Dropout(0.1),
        
        Dense(3, activation='softmax')
    ])
    
    nn_model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # 训练
    callbacks = [EarlyStopping(monitor='val_accuracy', patience=10, restore_best_weights=True)]
    
    nn_model.fit(
        X_train, y_train,
        validation_split=0.2,
        epochs=50,
        batch_size=32,
        callbacks=callbacks,
        verbose=1
    )
    
    # 评估
    nn_pred = np.argmax(nn_model.predict(X_test), axis=1)
    
    nn_acc = accuracy_score(y_test, nn_pred)
    nn_f1 = f1_score(y_test, nn_pred, average='weighted')
    nn_recall = recall_score(y_test, nn_pred, average='weighted')
    nn_precision = precision_score(y_test, nn_pred, average='weighted')
    
    models['DeepLearning'] = nn_model
    results['DeepLearning'] = {
        'accuracy': nn_acc,
        'f1_score': nn_f1,
        'recall': nn_recall,
        'precision': nn_precision
    }
    
    print(f"   深度学习准确率: {nn_acc:.4f} ({nn_acc*100:.2f}%)")
    print(f"   F1分数: {nn_f1:.4f}")
    print(f"   召回率: {nn_recall:.4f}")
    
except Exception as e:
    print(f"   深度学习训练失败: {e}")

# 显示所有结果
print("\n📊 所有模型结果:")
print("-" * 70)
print(f"{'模型':<20} {'准确率':<10} {'F1分数':<10} {'召回率':<10} {'精确率':<10}")
print("-" * 70)

for name, result in results.items():
    print(f"{name:<20} {result['accuracy']:<10.4f} {result['f1_score']:<10.4f} "
          f"{result['recall']:<10.4f} {result['precision']:<10.4f}")

# 找到最佳模型
if results:
    best_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
    best_result = results[best_name]
    best_model = models[best_name]
    
    print(f"\n🏆 最佳模型: {best_name}")
    print(f"🎯 最佳准确率: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
    
    # 保存最佳模型
    if best_result['accuracy'] >= 0.85:
        print(f"\n💾 保存最佳模型 (达到85%+标准)...")
        
        os.makedirs(output_path, exist_ok=True)
        
        # 保存模型
        if hasattr(best_model, 'save'):  # 深度学习模型
            best_model.save(os.path.join(output_path, "best_dementia_model.h5"))
            model_type = "deep_learning"
        else:  # 机器学习模型
            import joblib
            joblib.dump(best_model, os.path.join(output_path, "best_dementia_model.pkl"))
            model_type = "machine_learning"
        
        # 保存评估报告
        import json
        report = {
            'model_info': {
                'model_name': best_name,
                'model_type': model_type,
                'training_time_hours': (time.time() - start_time) / 3600,
                'target_achieved': True
            },
            'performance': best_result,
            'all_models': results
        }
        
        with open(os.path.join(output_path, "training_report.json"), "w", encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 模型已保存到: {output_path}")
        print("🚀 模型已准备好用于部署!")
    else:
        print(f"⚠️ 模型准确率 {best_result['accuracy']*100:.2f}% 未达到85%标准")

else:
    print("❌ 没有成功训练的模型")

# 训练时间统计
total_time = (time.time() - start_time) / 3600
print(f"\n⏰ 训练完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print(f"⏱️ 总耗时: {total_time:.2f}小时")

if total_time < 5:
    remaining_time = 5 - total_time
    print(f"🔄 剩余时间: {remaining_time:.2f}小时")
    print("💡 可以继续优化模型...")

print("🏁 训练器结束")
