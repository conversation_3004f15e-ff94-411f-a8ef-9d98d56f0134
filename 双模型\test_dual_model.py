# -*- coding: utf-8 -*-
"""
双模型系统测试脚本
用于验证CT检测模型和痴呆症分析模型的集成
"""

import os
import sys

def test_model_paths():
    """测试模型文件是否存在"""
    print("🔍 检查模型文件...")
    
    dementia_model_path = r"D:\模型开发\MRI_class.h5"
    mri_model_path = r"D:\模型开发\picture_class.h5"
    
    print(f"痴呆症模型路径: {dementia_model_path}")
    print(f"存在: {'✅' if os.path.exists(dementia_model_path) else '❌'}")
    
    print(f"MRI检测模型路径: {mri_model_path}")
    print(f"存在: {'✅' if os.path.exists(mri_model_path) else '❌'}")

    return os.path.exists(dementia_model_path), os.path.exists(mri_model_path)

def test_tensorflow_import():
    """测试TensorFlow导入"""
    print("\n🔍 检查TensorFlow...")
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        return True
    except ImportError as e:
        print(f"❌ TensorFlow导入失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🔍 测试模型加载...")
    
    try:
        import tensorflow as tf
        
        # 测试痴呆症模型
        dementia_model_path = r"D:\模型开发\MRI_class.h5"
        if os.path.exists(dementia_model_path):
            try:
                dementia_model = tf.keras.models.load_model(dementia_model_path)
                print("✅ 痴呆症模型加载成功")
                print(f"   输入形状: {dementia_model.input_shape}")
                print(f"   输出形状: {dementia_model.output_shape}")
            except Exception as e:
                print(f"❌ 痴呆症模型加载失败: {e}")
        
        # 测试MRI检测模型
        mri_model_path = r"D:\模型开发\picture_class.h5"
        if os.path.exists(mri_model_path):
            try:
                mri_model = tf.keras.models.load_model(mri_model_path)
                print("✅ MRI检测模型加载成功")
                print(f"   输入形状: {mri_model.input_shape}")
                print(f"   输出形状: {mri_model.output_shape}")
            except Exception as e:
                print(f"❌ MRI检测模型加载失败: {e}")
                
    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")

def test_gui_dependencies():
    """测试GUI依赖"""
    print("\n🔍 检查GUI依赖...")
    
    dependencies = [
        ('tkinter', 'tkinter'),
        ('customtkinter', 'customtkinter'),
        ('PIL', 'PIL'),
        ('cv2', 'cv2'),
        ('numpy', 'numpy')
    ]
    
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name} - 需要安装")

def main():
    """主测试函数"""
    print("🧠 AI痴呆症识别器 - 双模型系统测试")
    print("=" * 50)
    
    # 测试模型文件
    dementia_exists, mri_exists = test_model_paths()
    
    # 测试TensorFlow
    tf_ok = test_tensorflow_import()
    
    # 测试GUI依赖
    test_gui_dependencies()
    
    # 测试模型加载
    if tf_ok and (dementia_exists or mri_exists):
        test_model_loading()

    print("\n" + "=" * 50)
    print("📋 测试总结:")

    if dementia_exists and mri_exists and tf_ok:
        print("✅ 双模型系统准备就绪！")
        print("   - 痴呆症分析模型: 可用")
        print("   - MRI图像检测模型: 可用")
        print("   - 将提供完整的图像验证和分析功能")
    elif dementia_exists and tf_ok:
        print("⚠️ 部分功能可用")
        print("   - 痴呆症分析模型: 可用")
        print("   - MRI图像检测模型: 不可用")
        print("   - 将跳过MRI图像验证功能")
    elif mri_exists and tf_ok:
        print("⚠️ 部分功能可用")
        print("   - 痴呆症分析模型: 不可用")
        print("   - MRI图像检测模型: 可用")
        print("   - 无法进行痴呆症分析")
    else:
        print("❌ 系统未准备就绪")
        print("   请检查模型文件和依赖安装")

if __name__ == "__main__":
    main()
