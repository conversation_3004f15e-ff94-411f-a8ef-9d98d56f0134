"""
训练监控器 - 监控所有并行训练进程
"""

import time
import os
import json
import glob

print("📊 训练监控器启动")
print("🔍 监控所有并行训练进程")
print("=" * 50)

output_path = r"D:\模型开发\audio"
start_time = time.time()

def check_training_progress():
    """检查训练进度"""
    print(f"\n⏰ 检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输出目录中的文件
    if os.path.exists(output_path):
        files = os.listdir(output_path)
        model_files = [f for f in files if f.endswith(('.h5', '.pkl'))]
        report_files = [f for f in files if f.endswith('.json')]
        
        print(f"📁 输出目录状态:")
        print(f"   模型文件: {len(model_files)}")
        print(f"   报告文件: {len(report_files)}")
        
        if model_files:
            print("   🎯 发现模型文件:")
            for model_file in model_files:
                file_path = os.path.join(output_path, model_file)
                file_size = os.path.getsize(file_path) / (1024*1024)  # MB
                mod_time = time.ctime(os.path.getmtime(file_path))
                print(f"     - {model_file} ({file_size:.2f}MB, {mod_time})")
        
        if report_files:
            print("   📊 发现报告文件:")
            for report_file in report_files:
                file_path = os.path.join(output_path, report_file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report = json.load(f)
                    
                    # 提取关键信息
                    if 'training_summary' in report:
                        summary = report['training_summary']
                        print(f"     - {report_file}:")
                        print(f"       最佳模型: {summary.get('best_model', 'N/A')}")
                        print(f"       最佳准确率: {summary.get('best_accuracy', 0):.4f}")
                        print(f"       训练时间: {summary.get('training_time_hours', 0):.2f}小时")
                    
                    elif 'innovation_training' in report:
                        innovation = report['innovation_training']
                        print(f"     - {report_file}:")
                        print(f"       最佳方法: {innovation.get('best_method', 'N/A')}")
                        print(f"       最佳准确率: {innovation.get('best_accuracy', 0):.4f}")
                        print(f"       创新影响: {report.get('breakthrough_analysis', {}).get('innovation_impact', 'N/A')}")
                    
                    elif 'model_info' in report:
                        model_info = report['model_info']
                        print(f"     - {report_file}:")
                        print(f"       模型名称: {model_info.get('model_name', 'N/A')}")
                        print(f"       准确率: {report.get('performance', {}).get('accuracy', 0):.4f}")
                        print(f"       训练时间: {model_info.get('training_duration_hours', 0):.2f}小时")
                
                except Exception as e:
                    print(f"     - {report_file}: 读取失败 ({e})")
    
    else:
        print("📁 输出目录尚未创建")

def monitor_training():
    """持续监控训练"""
    monitoring_duration = 5 * 3600  # 5小时
    check_interval = 300  # 5分钟检查一次
    
    print(f"🔄 开始监控，持续时间: 5小时")
    print(f"📅 检查间隔: {check_interval//60}分钟")
    
    while (time.time() - start_time) < monitoring_duration:
        check_training_progress()
        
        # 检查是否有训练完成
        if os.path.exists(output_path):
            files = os.listdir(output_path)
            model_files = [f for f in files if f.endswith(('.h5', '.pkl'))]
            
            if model_files:
                print("\n🎉 发现训练完成的模型!")
                
                # 分析最佳结果
                best_accuracy = 0
                best_report = None
                
                report_files = [f for f in files if f.endswith('.json')]
                for report_file in report_files:
                    try:
                        with open(os.path.join(output_path, report_file), 'r', encoding='utf-8') as f:
                            report = json.load(f)
                        
                        accuracy = 0
                        if 'training_summary' in report:
                            accuracy = report['training_summary'].get('best_accuracy', 0)
                        elif 'innovation_training' in report:
                            accuracy = report['innovation_training'].get('best_accuracy', 0)
                        elif 'performance' in report:
                            accuracy = report['performance'].get('accuracy', 0)
                        
                        if accuracy > best_accuracy:
                            best_accuracy = accuracy
                            best_report = report_file
                    
                    except:
                        continue
                
                if best_accuracy >= 0.85:
                    print(f"🏆 发现优秀模型!")
                    print(f"   最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
                    print(f"   报告文件: {best_report}")
                    
                    if best_accuracy >= 0.90:
                        print("🎉 突破性成果! 达到90%+准确率!")
                        print("🚀 训练目标已达成!")
                        break
                    else:
                        print("✅ 达到医疗级标准!")
        
        # 等待下次检查
        remaining_time = monitoring_duration - (time.time() - start_time)
        if remaining_time > 0:
            wait_time = min(check_interval, remaining_time)
            print(f"\n⏳ 等待{wait_time//60}分钟后下次检查...")
            print(f"⏰ 剩余监控时间: {remaining_time//3600:.1f}小时")
            time.sleep(wait_time)
        else:
            break
    
    print(f"\n🏁 监控结束")
    print(f"⏰ 总监控时间: {(time.time() - start_time)//3600:.1f}小时")

def final_summary():
    """最终总结"""
    print("\n" + "="*60)
    print("📊 最终训练总结")
    print("="*60)
    
    if os.path.exists(output_path):
        files = os.listdir(output_path)
        model_files = [f for f in files if f.endswith(('.h5', '.pkl'))]
        report_files = [f for f in files if f.endswith('.json')]
        
        print(f"🎯 训练成果:")
        print(f"   生成模型: {len(model_files)} 个")
        print(f"   生成报告: {len(report_files)} 个")
        
        if report_files:
            print(f"\n📈 性能总结:")
            
            all_results = []
            for report_file in report_files:
                try:
                    with open(os.path.join(output_path, report_file), 'r', encoding='utf-8') as f:
                        report = json.load(f)
                    
                    result = {'file': report_file}
                    
                    if 'training_summary' in report:
                        summary = report['training_summary']
                        result['method'] = summary.get('best_model', 'Unknown')
                        result['accuracy'] = summary.get('best_accuracy', 0)
                        result['type'] = 'Standard Training'
                    
                    elif 'innovation_training' in report:
                        innovation = report['innovation_training']
                        result['method'] = innovation.get('best_method', 'Unknown')
                        result['accuracy'] = innovation.get('best_accuracy', 0)
                        result['type'] = 'Innovation Training'
                    
                    elif 'performance' in report:
                        result['method'] = report.get('model_info', {}).get('model_name', 'Unknown')
                        result['accuracy'] = report['performance'].get('accuracy', 0)
                        result['type'] = 'Medical Grade Training'
                    
                    if 'accuracy' in result:
                        all_results.append(result)
                
                except:
                    continue
            
            if all_results:
                # 按准确率排序
                all_results.sort(key=lambda x: x['accuracy'], reverse=True)
                
                print(f"{'排名':<4} {'类型':<20} {'方法':<25} {'准确率':<10}")
                print("-" * 70)
                
                for i, result in enumerate(all_results, 1):
                    accuracy = result['accuracy']
                    status = "🏆" if accuracy >= 0.90 else "✅" if accuracy >= 0.85 else "📈"
                    print(f"{i:<4} {result['type']:<20} {result['method']:<25} {accuracy:<10.4f} {status}")
                
                best_result = all_results[0]
                print(f"\n🏆 最佳成果:")
                print(f"   方法: {best_result['method']}")
                print(f"   准确率: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
                print(f"   类型: {best_result['type']}")
                
                if best_result['accuracy'] >= 0.90:
                    print(f"\n🎉 恭喜! 达到突破性成果!")
                    print(f"🚀 模型性能超越90%准确率!")
                elif best_result['accuracy'] >= 0.85:
                    print(f"\n✅ 优秀! 达到医疗级标准!")
                    print(f"🏥 模型可用于临床辅助诊断!")
                else:
                    print(f"\n📈 良好进展，继续优化中...")
        
        print(f"\n📁 所有文件已保存到: {output_path}")
        print(f"🔍 查看详细报告了解更多信息")
    
    else:
        print("❌ 未发现训练输出")

if __name__ == "__main__":
    try:
        # 立即检查一次当前状态
        check_training_progress()
        
        # 开始持续监控
        monitor_training()
        
        # 最终总结
        final_summary()
        
    except KeyboardInterrupt:
        print("\n⏹️ 监控被用户中断")
        final_summary()
    
    except Exception as e:
        print(f"\n❌ 监控出错: {e}")
        final_summary()
    
    print(f"\n⏰ 监控结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("📊 训练监控器结束")
