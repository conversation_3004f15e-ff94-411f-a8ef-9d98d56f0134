"""
Deep Audio Dataset Analyzer - 深度分析音频数据集
重新仔细学习 D:/模型开发/audio 的所有内容
"""

import os
import json
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def deep_analyze_audio_dataset():
    """深度分析音频数据集的所有内容"""
    
    audio_path = Path(r"D:\模型开发\audio")
    
    if not audio_path.exists():
        print(f"❌ 错误: 路径 {audio_path} 不存在!")
        return None
    
    print("🔍 开始深度分析音频数据集...")
    print("=" * 80)
    
    analysis = {
        'root_structure': {},
        'all_directories': [],
        'all_files_by_type': {},
        'detailed_content': {},
        'dataset_identification': {},
        'potential_datasets': []
    }
    
    # 1. 分析根目录结构
    print("📁 根目录结构分析:")
    root_items = list(audio_path.iterdir())
    for item in root_items:
        if item.is_dir():
            print(f"   📂 目录: {item.name}")
            analysis['root_structure'][item.name] = 'directory'
        else:
            print(f"   📄 文件: {item.name}")
            analysis['root_structure'][item.name] = 'file'
    
    # 2. 深度遍历所有目录
    print(f"\n🗂️ 完整目录树结构:")
    for root, dirs, files in os.walk(audio_path):
        level = root.replace(str(audio_path), '').count(os.sep)
        indent = '  ' * level
        rel_path = os.path.relpath(root, audio_path)
        
        print(f"{indent}📂 {os.path.basename(root)}/")
        analysis['all_directories'].append(rel_path)
        
        # 分析每个目录的文件
        subindent = '  ' * (level + 1)
        for file in files[:5]:  # 只显示前5个文件
            print(f"{subindent}📄 {file}")
        if len(files) > 5:
            print(f"{subindent}... 还有 {len(files)-5} 个文件")
    
    # 3. 按文件类型统计
    print(f"\n📊 文件类型详细统计:")
    file_types = {}
    total_files = 0
    
    for root, dirs, files in os.walk(audio_path):
        for file in files:
            total_files += 1
            ext = Path(file).suffix.lower()
            if ext not in file_types:
                file_types[ext] = []
            file_types[ext].append(os.path.join(root, file))
    
    for ext, files in sorted(file_types.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"   {ext or '无扩展名'}: {len(files)} 个文件")
        analysis['all_files_by_type'][ext] = len(files)
    
    print(f"\n📈 总计: {total_files} 个文件")
    
    # 4. 识别数据集类型
    print(f"\n🎯 数据集类型识别:")
    
    # 检查是否包含特定数据集的标识
    dataset_indicators = {
        'librispeech': ['LibriSpeech', 'librispeech', 'openslr'],
        'commonvoice': ['CommonVoice', 'common_voice', 'mozilla'],
        'voxceleb': ['VoxCeleb', 'voxceleb'],
        'dementiabank': ['DementiaBank', 'dementiabank', 'talkbank'],
        'tedlium': ['TEDLIUM', 'tedlium'],
        'switchboard': ['Switchboard', 'switchboard']
    }
    
    found_datasets = []
    for root, dirs, files in os.walk(audio_path):
        path_str = str(root).lower()
        for dataset, indicators in dataset_indicators.items():
            for indicator in indicators:
                if indicator.lower() in path_str:
                    if dataset not in found_datasets:
                        found_datasets.append(dataset)
                        print(f"   ✅ 发现 {dataset.upper()} 数据集")
    
    analysis['potential_datasets'] = found_datasets
    
    # 5. 分析具体内容样本
    print(f"\n📋 内容样本分析:")
    
    # 查看文本文件内容
    text_files = []
    for root, dirs, files in os.walk(audio_path):
        for file in files:
            if file.endswith(('.txt', '.trans', '.csv')):
                text_files.append(os.path.join(root, file))
                if len(text_files) >= 3:
                    break
        if len(text_files) >= 3:
            break
    
    for text_file in text_files:
        try:
            print(f"\n📝 文件: {os.path.basename(text_file)}")
            with open(text_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()[:500]  # 读取前500字符
                lines = content.split('\n')[:3]  # 显示前3行
                for i, line in enumerate(lines, 1):
                    print(f"   {i}: {line}")
                if len(content) > 500:
                    print("   ...")
        except Exception as e:
            print(f"   ❌ 无法读取: {e}")
    
    # 6. 音频文件分析
    print(f"\n🎵 音频文件分析:")
    audio_extensions = ['.flac', '.wav', '.mp3', '.m4a', '.ogg']
    audio_files = []
    
    for ext in audio_extensions:
        if ext in file_types:
            print(f"   {ext}: {len(file_types[ext])} 个文件")
            audio_files.extend(file_types[ext][:2])  # 取前2个样本
    
    # 显示音频文件样本
    print(f"\n🎧 音频文件样本:")
    for audio_file in audio_files[:5]:
        rel_path = os.path.relpath(audio_file, audio_path)
        file_size = os.path.getsize(audio_file) / (1024*1024)  # MB
        print(f"   📻 {rel_path} ({file_size:.2f} MB)")
    
    return analysis

def generate_comprehensive_summary(analysis):
    """生成综合总结"""
    
    print("\n" + "="*80)
    print("🏗️  音频数据集综合分析总结")
    print("="*80)
    
    if analysis['potential_datasets']:
        print(f"\n🎯 识别到的数据集:")
        for dataset in analysis['potential_datasets']:
            print(f"   ✅ {dataset.upper()}")
    
    print(f"\n📊 数据集规模:")
    total_files = sum(analysis['all_files_by_type'].values())
    print(f"   总文件数: {total_files:,}")
    
    print(f"\n📁 目录结构:")
    print(f"   总目录数: {len(analysis['all_directories'])}")
    
    print(f"\n🎵 主要文件类型:")
    for ext, count in sorted(analysis['all_files_by_type'].items(), 
                           key=lambda x: x[1], reverse=True)[:10]:
        percentage = (count / total_files) * 100
        print(f"   {ext or '无扩展名'}: {count:,} 个 ({percentage:.1f}%)")

if __name__ == "__main__":
    print("🔍 深度音频数据集分析器")
    print("=" * 60)
    
    analysis = deep_analyze_audio_dataset()
    
    if analysis:
        generate_comprehensive_summary(analysis)
        
        # 保存详细报告
        report_path = "深度音频分析报告.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细报告已保存到: {report_path}")
        print("\n✅ 深度分析完成!")
    else:
        print("❌ 分析失败")
