# -*- coding: utf-8 -*-
"""
AI痴呆症识别器 - 现代化GUI版本
专业级Windows应用程序
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np
from fpdf import FPDF
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import inch

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")  # 深色主题
ctk.set_default_color_theme("blue")  # 蓝色主题

class AIDetectionApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("AI痴呆症识别器 v1.0")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.model = None
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        self.real_time_mode = False
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_model_async()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 AI痴呆症识别器",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="基于深度学习的医学影像智能分析系统",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=350)
        
        self.create_left_panel()
        self.create_right_panel()
        
        # 底部状态栏
        self.create_status_bar()
        
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        # 图像显示标题
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 医学影像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 默认显示
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
        
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 控制按钮区域
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=20)
        
        # 选择图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择影像文件",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 开始分析按钮
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🔍 开始AI分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.start_analysis,
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", pady=10)

        # 摄像头控制按钮
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=10)

        # 实时分析开关（修改为拍照分析）
        self.realtime_var = ctk.BooleanVar()
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="� 拍照分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=10)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(control_frame)
        self.progress.pack(fill="x", pady=10)
        self.progress.set(0)
        
        # 结果显示区域
        self.create_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
        
    def create_results_panel(self):
        """创建结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 结果标题
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # 主要结果显示
        self.result_label = ctk.CTkLabel(
            results_frame,
            text="等待分析...",
            font=ctk.CTkFont(size=14),
            wraplength=280
        )
        self.result_label.pack(pady=10)
        
        # 置信度显示
        self.confidence_label = ctk.CTkLabel(
            results_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.confidence_label.pack(pady=5)
        
        # 详细概率显示框
        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=150)
        self.details_frame.pack(fill="x", padx=10, pady=10)
        
    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=20, pady=10)
        
        # 保存结果按钮
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled"
        )
        self.save_btn.pack(fill="x", pady=5)

        # 生成PDF报告按钮
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="orange"
        )
        self.pdf_btn.pack(fill="x", pady=5)

        # 生成HTML报告按钮
        self.html_btn = ctk.CTkButton(
            func_frame,
            text="🌐 生成HTML报告",
            command=self.generate_html_report,
            state="disabled",
            fg_color="purple"
        )
        self.html_btn.pack(fill="x", pady=5)
        
        # 查看历史按钮
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history
        )
        self.history_btn.pack(fill="x", pady=5)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about
        )
        self.about_btn.pack(fill="x", pady=5)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载AI模型...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # 模型状态指示器
        self.model_status = ctk.CTkLabel(
            self.status_frame,
            text="⏳ 加载中",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        self.model_status.pack(side="right", padx=20, pady=10)
        
    def load_model_async(self):
        """异步加载模型"""
        def load_model():
            try:
                import warnings
                warnings.filterwarnings('ignore')
                
                import tensorflow as tf
                from tensorflow.keras.preprocessing import image
                import numpy as np
                
                # 设置TensorFlow日志级别
                tf.get_logger().setLevel('ERROR')
                
                try:
                    import absl.logging
                    absl.logging.set_verbosity(absl.logging.ERROR)
                except:
                    pass
                
                # 加载模型
                model_path = r"D:\模型开发\升级model.h5"
                if os.path.exists(model_path):
                    self.model = tf.keras.models.load_model(model_path)
                    self.tf = tf
                    self.image_module = image
                    self.np = np
                    
                    # 更新UI
                    self.root.after(0, self.on_model_loaded, True)
                else:
                    self.root.after(0, self.on_model_loaded, False)
                    
            except Exception as e:
                self.root.after(0, self.on_model_error, str(e))
        
        # 在后台线程中加载模型
        threading.Thread(target=load_model, daemon=True).start()
        
    def on_model_loaded(self, success):
        """模型加载完成回调"""
        if success:
            self.status_label.configure(text="✅ AI模型已就绪")
            self.model_status.configure(text="🟢 已加载", text_color="green")
        else:
            self.status_label.configure(text="❌ 模型文件未找到")
            self.model_status.configure(text="🔴 未找到", text_color="red")
            messagebox.showerror("错误", "未找到模型文件：D:\\模型开发\\升级model.h5")
            
    def on_model_error(self, error):
        """模型加载错误回调"""
        self.status_label.configure(text="❌ 模型加载失败")
        self.model_status.configure(text="🔴 错误", text_color="red")
        messagebox.showerror("错误", f"模型加载失败：{error}")
        
    def select_image(self):
        """选择图像文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title="选择医学影像文件",
            filetypes=file_types
        )
        
        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"📁 已选择: {os.path.basename(file_path)}")
            
    def display_image(self, image_path):
        """显示选择的图像"""
        try:
            # 加载并调整图像大小
            pil_image = Image.open(image_path)
            
            # 计算合适的显示尺寸
            display_size = (400, 400)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(pil_image)
            
            # 更新显示
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图像：{e}")
            
    def start_analysis(self):
        """开始AI分析"""
        if not self.model:
            messagebox.showerror("错误", "AI模型未加载")
            return
            
        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择图像文件")
            return
            
        # 禁用按钮，显示进度
        self.analyze_btn.configure(state="disabled", text="🔄 分析中...")
        self.progress.set(0)
        
        # 在后台线程中进行分析
        threading.Thread(target=self.perform_analysis, daemon=True).start()
        
    def perform_analysis(self):
        """执行AI分析 - 无验证版本"""
        try:
            # 更新进度
            self.root.after(0, lambda: self.progress.set(0.2))

            # 直接加载和预处理图像，跳过验证
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            self.root.after(0, lambda: self.progress.set(0.6))

            # 进行预测
            predictions = self.model.predict(img_array, verbose=0)
            predicted_class = self.np.argmax(predictions, axis=1)
            prediction_probs = predictions[0].tolist()

            self.root.after(0, lambda: self.progress.set(0.9))

            # 准备结果
            result_data = {
                'predicted_class': predicted_class[0],
                'predicted_class_name': self.class_labels[predicted_class[0]],
                'confidence': max(prediction_probs),
                'probabilities': prediction_probs,
                'image_path': self.current_image_path,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.root.after(0, lambda: self.progress.set(1.0))

            # 更新UI
            self.root.after(0, self.display_results, result_data)

        except Exception as e:
            self.root.after(0, self.on_analysis_error, str(e))
            
    def display_results(self, result_data):
        """显示分析结果"""
        # 保存到历史记录
        self.results_history.append(result_data)
        
        # 显示主要结果 - 简化版本，无警告
        class_name = result_data['predicted_class_name']
        confidence = result_data['confidence']

        # 简化显示，只区分强制分析
        if result_data.get('forced_analysis', False):
            result_text = f"⚡ 强制分析结果:\n{class_name}"
            text_color = "orange"
        else:
            result_text = f"🎯 预测结果:\n{class_name}"
            text_color = "white"

        self.result_label.configure(
            text=result_text,
            text_color=text_color
        )

        self.confidence_label.configure(
            text=f"🎯 置信度: {confidence:.2%}",
            text_color="lightblue"
        )
        
        # 清空详细结果框
        for widget in self.details_frame.winfo_children():
            widget.destroy()
            
        # 显示详细概率
        for i, prob in enumerate(result_data['probabilities']):
            prob_frame = ctk.CTkFrame(self.details_frame)
            prob_frame.pack(fill="x", pady=2)
            
            label_text = self.class_labels[i].split('(')[0]
            prob_label = ctk.CTkLabel(
                prob_frame,
                text=f"{label_text}: {prob:.2%}",
                font=ctk.CTkFont(size=11)
            )
            prob_label.pack(side="left", padx=10, pady=5)
            
            # 概率条
            prob_bar = ctk.CTkProgressBar(prob_frame, width=100, height=10)
            prob_bar.pack(side="right", padx=10, pady=5)
            prob_bar.set(prob)
        
        # 重置按钮状态
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.save_btn.configure(state="normal")
        self.pdf_btn.configure(state="normal")
        self.html_btn.configure(state="normal")
        self.progress.set(0)

        # 更新状态
        self.status_label.configure(text="✅ 分析完成")
        
    def validate_medical_image(self, image_path):
        """无验证 - 所有图像都直接通过"""
        try:
            pil_image = Image.open(image_path)
            img_array = np.array(pil_image)
            height, width = img_array.shape[:2]

            # 简单返回通过结果
            validation_result = {
                'is_valid': True,
                'warnings': [],
                'confidence': "高",
                'image_info': {
                    'size': f"{width}x{height}",
                    'channels': len(img_array.shape),
                    'format': pil_image.format
                }
            }

            return validation_result

        except Exception as e:
            return {
                'is_valid': True,  # 即使出错也允许通过
                'warnings': [],
                'confidence': "高"
            }

    # 删除验证警告方法 - 不再需要

    # 删除强制分析方法 - 不再需要验证，所以不需要强制分析

    def on_analysis_error(self, error):
        """分析错误处理"""
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.progress.set(0)
        self.status_label.configure(text="❌ 分析失败")
        messagebox.showerror("分析错误", f"AI分析失败：{error}")
        
    def save_results(self):
        """保存分析结果"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可保存的结果")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results_history, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "结果已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败：{e}")
                
    def show_history(self):
        """显示历史记录"""
        if not self.results_history:
            messagebox.showinfo("提示", "暂无历史记录")
            return
            
        # 创建历史记录窗口
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("分析历史")
        history_window.geometry("600x400")
        
        # 历史记录列表
        history_frame = ctk.CTkScrollableFrame(history_window)
        history_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        for i, record in enumerate(reversed(self.results_history)):
            record_frame = ctk.CTkFrame(history_frame)
            record_frame.pack(fill="x", pady=5)
            
            info_text = f"#{len(self.results_history)-i} - {record['timestamp']}\n"
            info_text += f"结果: {record['predicted_class_name']}\n"
            info_text += f"置信度: {record['confidence']:.2%}"
            
            record_label = ctk.CTkLabel(
                record_frame,
                text=info_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            record_label.pack(padx=15, pady=10)
            
    def show_about(self):
        """显示关于信息"""
        about_text = """
🧠 AI痴呆症识别器 v1.0

基于深度学习的医学影像智能分析系统

✨ 主要功能:
• 智能图像分析
• 多类别痴呆症检测
• 概率分布可视化
• 结果历史记录
• 专业报告生成

⚠️ 重要声明:
本软件仅供研究和参考使用，
不能替代专业医学诊断。
请咨询专业医生获取准确诊断。

© 2024 AI Medical Solutions
        """
        
        messagebox.showinfo("关于软件", about_text)

    def toggle_camera(self):
        """切换摄像头状态"""
        if not self.camera_active:
            self.start_camera()
        else:
            self.stop_camera()

    def start_camera(self):
        """启动摄像头"""
        try:
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                messagebox.showerror("错误", "无法打开摄像头")
                return

            self.camera_active = True
            self.camera_btn.configure(text="📹 关闭摄像头", fg_color="red")
            self.capture_btn.configure(state="normal")
            self.status_label.configure(text="📹 摄像头已启动 - 可以拍照进行分析")

            # 显示摄像头使用说明
            info_text = ("📹 摄像头模式说明：\n\n"
                        "• 摄像头主要用于预览和拍照\n"
                        "• 点击'拍照分析'可保存当前画面并分析\n"
                        "• 注意：请确保拍摄的是医学影像\n"
                        "• 普通摄像头图像不适合痴呆症分析")

            messagebox.showinfo("摄像头使用说明", info_text)

            # 开始摄像头循环
            self.camera_loop()

        except Exception as e:
            messagebox.showerror("错误", f"摄像头启动失败：{e}")

    def stop_camera(self):
        """停止摄像头"""
        self.camera_active = False

        if self.camera:
            self.camera.release()
            self.camera = None

        self.camera_btn.configure(text="📹 启动摄像头", fg_color="green")
        self.capture_btn.configure(state="disabled")
        self.status_label.configure(text="📹 摄像头已关闭")

        # 恢复默认显示
        self.image_label.configure(
            image="",
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP"
        )

    def camera_loop(self):
        """摄像头循环显示"""
        if self.camera_active and self.camera:
            ret, frame = self.camera.read()
            if ret:
                # 保存当前帧供拍照使用
                self.current_frame = frame.copy()

                # 转换颜色空间
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 调整大小
                height, width = frame_rgb.shape[:2]
                max_size = 400
                if width > height:
                    new_width = max_size
                    new_height = int(height * max_size / width)
                else:
                    new_height = max_size
                    new_width = int(width * max_size / height)

                frame_resized = cv2.resize(frame_rgb, (new_width, new_height))

                # 转换为PhotoImage
                pil_image = Image.fromarray(frame_resized)
                photo = ImageTk.PhotoImage(pil_image)

                # 更新显示
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo

            # 继续循环
            self.root.after(30, self.camera_loop)  # 约33 FPS

    def capture_and_analyze(self):
        """拍照并分析"""
        if not self.camera_active or not hasattr(self, 'current_frame'):
            messagebox.showerror("错误", "摄像头未启动或无可用画面")
            return

        try:
            # 保存当前帧为临时文件
            import tempfile
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, "camera_capture.jpg")

            # 保存图像
            cv2.imwrite(temp_path, self.current_frame)

            # 显示拍照确认
            confirm_text = ("📸 已拍照！\n\n"
                          "⚠️ 重要提醒：\n"
                          "• 请确保拍摄的是医学影像（如显示器上的MRI、CT图像）\n"
                          "• 普通摄像头拍摄的日常场景不适合痴呆症分析\n"
                          "• 分析结果仅供参考\n\n"
                          "是否继续分析此图像？")

            result = messagebox.askyesno("拍照确认", confirm_text)

            if result:
                # 设置为当前分析图像
                self.current_image_path = temp_path

                # 显示拍摄的图像
                self.display_image(temp_path)

                # 开始分析
                self.start_analysis()

                self.status_label.configure(text="📸 已拍照并开始分析")
            else:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                self.status_label.configure(text="📸 拍照已取消")

        except Exception as e:
            messagebox.showerror("错误", f"拍照失败：{e}")

    def generate_pdf_report(self):
        """生成PDF诊断报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        # 获取最新结果
        latest_result = self.results_history[-1]

        # 让用户选择保存位置和文件名
        from datetime import datetime
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        file_path = filedialog.asksaveasfilename(
            title="保存PDF报告",
            defaultextension=".pdf",
            initialname=default_name,
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 创建简单的文本报告（确保能工作）
                self.create_simple_text_report(latest_result, file_path.replace('.pdf', '.txt'))

                # 尝试创建PDF报告
                try:
                    self.create_working_pdf_report(latest_result, file_path)
                    messagebox.showinfo("成功", f"PDF报告已生成：{file_path}")

                    # 询问是否打开报告
                    if messagebox.askyesno("打开报告", "是否立即打开生成的PDF报告？"):
                        os.startfile(file_path)
                except:
                    # PDF失败，使用文本报告
                    text_path = file_path.replace('.pdf', '.txt')
                    messagebox.showinfo("成功", f"文本报告已生成：{text_path}")
                    if messagebox.askyesno("打开报告", "是否立即打开生成的文本报告？"):
                        os.startfile(text_path)

            except Exception as e:
                messagebox.showerror("错误", f"报告生成失败：{e}")

    def generate_html_report(self):
        """生成HTML可视化报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        # 获取最新结果
        latest_result = self.results_history[-1]

        # 让用户选择保存位置和文件名
        from datetime import datetime
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        file_path = filedialog.asksaveasfilename(
            title="保存HTML报告",
            defaultextension=".html",
            initialname=default_name,
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_html_report(latest_result, file_path)
                messagebox.showinfo("成功", f"HTML报告已生成：{file_path}")

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "是否立即在浏览器中打开HTML报告？"):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(file_path)}")

            except Exception as e:
                messagebox.showerror("错误", f"HTML报告生成失败：{e}")

    def create_working_pdf_report(self, result_data, file_path):
        """创建可靠的PDF报告"""
        try:
            from fpdf import FPDF

            pdf = FPDF()
            pdf.add_page()

            # 设置字体
            pdf.set_font('Arial', 'B', 16)
            pdf.cell(0, 10, 'AI Dementia Detection Report', 0, 1, 'C')
            pdf.ln(10)

            # 基本信息
            pdf.set_font('Arial', '', 12)
            pdf.cell(0, 8, f"Analysis Date: {result_data['timestamp']}", 0, 1)
            pdf.cell(0, 8, f"Image File: {os.path.basename(result_data['image_path'])}", 0, 1)
            pdf.ln(5)

            # 分析结果
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Analysis Results:', 0, 1)
            pdf.set_font('Arial', '', 11)

            predicted_class = result_data['predicted_class_name']
            confidence = result_data['confidence']

            pdf.cell(0, 8, f"Predicted Classification: {predicted_class}", 0, 1)
            pdf.cell(0, 8, f"Confidence Level: {confidence:.2%}", 0, 1)
            pdf.ln(5)

            # 详细概率分布
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Detailed Probability Distribution:', 0, 1)
            pdf.set_font('Arial', '', 10)

            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                pdf.cell(0, 6, f"  {class_name}: {prob:.4f} ({prob:.2%})", 0, 1)

            pdf.ln(10)

            # 免责声明
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(0, 6, 'IMPORTANT DISCLAIMER:', 0, 1)
            pdf.set_font('Arial', '', 9)
            disclaimer = ("This AI analysis is for research and reference purposes only. "
                         "It cannot replace professional medical diagnosis.")
            pdf.cell(0, 5, disclaimer, 0, 1)

            pdf.output(file_path)

        except Exception as e:
            raise Exception(f"PDF creation failed: {e}")

    def create_html_report(self, result_data, file_path):
                    self.set_font('Arial', 'B', 18)
                    self.set_text_color(0, 51, 102)  # 深蓝色
                    self.cell(0, 15, '🧠 AI痴呆症诊断报告', 0, 1, 'C')
                    self.set_text_color(0, 0, 0)  # 恢复黑色
                    self.ln(5)

                def footer(self):
                    self.set_y(-15)
                    self.set_font('Arial', 'I', 8)
                    self.set_text_color(128, 128, 128)
                    self.cell(0, 10, f'第 {self.page_no()} 页 | 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}', 0, 0, 'C')
                    self.set_text_color(0, 0, 0)

            pdf = EnhancedPDFReport()
            pdf.add_page()

            # 报告基本信息
            pdf.set_font('Arial', 'B', 14)
            pdf.set_fill_color(240, 248, 255)  # 浅蓝色背景
            pdf.cell(0, 10, '📋 基本信息', 0, 1, 'L', True)
            pdf.ln(2)

            pdf.set_font('Arial', '', 11)
            pdf.cell(0, 8, f"📅 分析日期: {result_data['timestamp']}", 0, 1)
            pdf.cell(0, 8, f"📁 图像文件: {os.path.basename(result_data['image_path'])}", 0, 1)

            # 添加验证信息
            if 'validation_info' in result_data:
                validation = result_data['validation_info']
                confidence = validation.get('confidence', '未知')
                pdf.cell(0, 8, f"🔍 图像验证: {confidence}置信度", 0, 1)

            pdf.ln(5)

            # 尝试添加图像
            try:
                # 创建图像的缩略图
                from PIL import Image as PILImage
                img = PILImage.open(result_data['image_path'])

                # 调整图像大小
                img.thumbnail((150, 150), PILImage.Resampling.LANCZOS)

                # 保存为临时文件
                temp_img_path = "temp_report_image.jpg"
                img.save(temp_img_path, "JPEG")

                # 添加到PDF
                pdf.set_font('Arial', 'B', 14)
                pdf.set_fill_color(240, 248, 255)
                pdf.cell(0, 10, '🖼️ 分析图像', 0, 1, 'L', True)
                pdf.ln(2)

                pdf.image(temp_img_path, x=30, w=150)
                pdf.ln(80)

                # 删除临时文件
                if os.path.exists(temp_img_path):
                    os.remove(temp_img_path)

            except Exception as e:
                pdf.set_font('Arial', '', 10)
                pdf.cell(0, 8, f"⚠️ 无法显示图像: {str(e)}", 0, 1)
                pdf.ln(5)

            # 分析结果
            pdf.set_font('Arial', 'B', 14)
            pdf.set_fill_color(255, 248, 240)  # 浅橙色背景
            pdf.cell(0, 10, '🎯 AI分析结果', 0, 1, 'L', True)
            pdf.ln(2)

            predicted_class = result_data['predicted_class_name']
            confidence = result_data['confidence']

            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, f"预测分类: {predicted_class}", 0, 1)
            pdf.set_font('Arial', '', 11)
            pdf.cell(0, 8, f"置信度: {confidence:.2%}", 0, 1)

            # 添加强制分析标记
            if result_data.get('forced_analysis', False):
                pdf.set_text_color(255, 0, 0)  # 红色
                pdf.cell(0, 8, "⚠️ 注意: 此结果为强制分析，图像验证未通过", 0, 1)
                pdf.set_text_color(0, 0, 0)  # 恢复黑色

            pdf.ln(5)

            # 详细概率分布
            pdf.set_font('Arial', 'B', 14)
            pdf.set_fill_color(248, 255, 248)  # 浅绿色背景
            pdf.cell(0, 10, '📊 详细概率分布', 0, 1, 'L', True)
            pdf.ln(2)

            pdf.set_font('Arial', '', 10)
            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i].split('(')[0]  # 只显示英文部分
                chinese_name = self.class_labels[i].split('(')[1].replace(')', '') if '(' in self.class_labels[i] else ''

                # 创建简单的进度条
                bar_length = int(prob * 30)
                bar = "█" * bar_length + "░" * (30 - bar_length)

                pdf.cell(0, 6, f"{chinese_name:<12} {bar} {prob:.4f} ({prob:.2%})", 0, 1)

            pdf.ln(5)

            # 综合诊断
            pdf.set_font('Arial', 'B', 14)
            pdf.set_fill_color(255, 240, 245)  # 浅粉色背景
            pdf.cell(0, 10, '🏥 综合诊断', 0, 1, 'L', True)
            pdf.ln(2)

            pdf.set_font('Arial', '', 11)

            # 根据结果给出详细诊断
            if "NonDemented" in predicted_class:
                diagnosis = ("根据AI分析，未检测到明显的痴呆症状。大脑结构显示正常特征。"
                           "建议继续保持健康的生活方式，定期进行健康检查。")
                risk_level = "低风险"
                color = (0, 128, 0)  # 绿色
            elif "VeryMild" in predicted_class:
                diagnosis = ("检测到非常轻微的认知变化。这可能是正常老化过程，"
                           "但建议进行更详细的神经心理学评估以确认。")
                risk_level = "轻微风险"
                color = (255, 165, 0)  # 橙色
            elif "Mild" in predicted_class:
                diagnosis = ("检测到轻度痴呆的特征。建议尽快咨询神经科医生，"
                           "进行全面的认知评估和制定相应的治疗计划。")
                risk_level = "中等风险"
                color = (255, 140, 0)  # 深橙色
            else:
                diagnosis = ("检测到中度痴呆的特征。强烈建议立即就医，"
                           "需要专业医生的诊断和治疗干预。")
                risk_level = "高风险"
                color = (255, 0, 0)  # 红色

            pdf.set_text_color(*color)
            pdf.cell(0, 8, f"风险等级: {risk_level}", 0, 1)
            pdf.set_text_color(0, 0, 0)

            # 分行显示诊断
            words = diagnosis.split()
            line = ""
            for word in words:
                if len(line + word) < 60:
                    line += word + " "
                else:
                    pdf.cell(0, 6, line.strip(), 0, 1)
                    line = word + " "
            if line:
                pdf.cell(0, 6, line.strip(), 0, 1)

            pdf.ln(5)

            # 健康建议
            pdf.set_font('Arial', 'B', 14)
            pdf.set_fill_color(240, 255, 240)  # 浅绿色背景
            pdf.cell(0, 10, '💡 健康建议', 0, 1, 'L', True)
            pdf.ln(2)

            pdf.set_font('Arial', '', 10)
            recommendations = [
                "• 保持规律的体育锻炼，每周至少150分钟中等强度运动",
                "• 维持健康的饮食习惯，多食用富含Omega-3的食物",
                "• 保持社交活动和智力刺激，如阅读、解谜等",
                "• 确保充足的睡眠，每晚7-9小时",
                "• 定期进行医学检查和认知评估",
                "• 如有疑虑，及时咨询专业医生"
            ]

            for rec in recommendations:
                pdf.cell(0, 6, rec, 0, 1)

            pdf.ln(5)

            # 免责声明
            pdf.set_font('Arial', 'B', 12)
            pdf.set_text_color(255, 0, 0)
            pdf.cell(0, 8, '⚠️ 重要免责声明', 0, 1)
            pdf.set_text_color(0, 0, 0)
            pdf.set_font('Arial', '', 9)

            disclaimer = ("本AI分析仅供研究和参考使用，不能替代专业医学诊断。"
                         "任何医疗决策都应基于合格医疗专业人员的建议。"
                         "请咨询神经科医生或相关专家获取准确的诊断和治疗方案。")

            words = disclaimer.split()
            line = ""
            for word in words:
                if len(line + word) < 70:
                    line += word + " "
                else:
                    pdf.cell(0, 5, line.strip(), 0, 1)
                    line = word + " "
            if line:
                pdf.cell(0, 5, line.strip(), 0, 1)

            pdf.output(file_path)

        except ImportError:
            # 如果fpdf不可用，使用简单的文本报告
            self.create_simple_text_report(result_data, file_path.replace('.pdf', '.txt'))
        except Exception as e:
            # 如果增强版失败，尝试简单版本
            self.create_simple_pdf_report(result_data, file_path)

    def create_html_report(self, result_data, file_path):
        """创建HTML可视化报告"""
        try:
            import base64
            from datetime import datetime

            # 读取图像并转换为base64
            image_base64 = ""
            try:
                with open(result_data['image_path'], 'rb') as img_file:
                    image_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                    image_ext = os.path.splitext(result_data['image_path'])[1].lower()
                    if image_ext in ['.jpg', '.jpeg']:
                        image_mime = 'image/jpeg'
                    elif image_ext == '.png':
                        image_mime = 'image/png'
                    else:
                        image_mime = 'image/jpeg'
            except:
                pass

            # 生成概率分布图表数据
            chart_data = []
            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i].split('(')[1].replace(')', '') if '(' in self.class_labels[i] else self.class_labels[i]
                chart_data.append({'name': class_name, 'value': prob * 100})

            # HTML模板
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI痴呆症诊断报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .info-section {{
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }}
        .result-section {{
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }}
        .chart-section {{
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }}
        .recommendation-section {{
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
        }}
        .disclaimer-section {{
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-left: 5px solid #e17055;
        }}
        .section h2 {{
            color: #2d3436;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
            margin-top: 0;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .analysis-image {{
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .result-highlight {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3436;
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 20px 0;
        }}
        .confidence-bar {{
            width: 100%;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }}
        .confidence-fill {{
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9, #74b9ff);
            border-radius: 15px;
            transition: width 0.5s ease;
        }}
        .chart-container {{
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }}
        .recommendation-list {{
            list-style: none;
            padding: 0;
        }}
        .recommendation-list li {{
            background: rgba(255,255,255,0.7);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #00b894;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            background: #2d3436;
            color: white;
        }}
        @media print {{
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI痴呆症诊断报告</h1>
            <p>基于深度学习的医学影像智能分析</p>
        </div>

        <div class="content">
            <div class="section info-section">
                <h2>📋 基本信息</h2>
                <p><strong>分析时间:</strong> {result_data['timestamp']}</p>
                <p><strong>图像文件:</strong> {os.path.basename(result_data['image_path'])}</p>
                <p><strong>分析模型:</strong> AI痴呆症检测模型 v1.0</p>
                {"<p><strong>图像验证:</strong> " + result_data.get('validation_info', {}).get('confidence', '未知') + "置信度</p>" if 'validation_info' in result_data else ""}
            </div>

            {"<div class='image-container'><img src='data:" + image_mime + ";base64," + image_base64 + "' alt='分析图像' class='analysis-image'></div>" if image_base64 else ""}

            <div class="section result-section">
                <h2>🎯 AI分析结果</h2>
                <div class="result-highlight">
                    预测分类: {result_data['predicted_class_name']}
                </div>
                <p><strong>置信度:</strong> {result_data['confidence']:.2%}</p>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: {result_data['confidence']*100}%"></div>
                </div>
                {"<p style='color: red;'><strong>⚠️ 注意:</strong> 此结果为强制分析，图像验证未通过</p>" if result_data.get('forced_analysis', False) else ""}
            </div>

            <div class="section chart-section">
                <h2>📊 详细概率分布</h2>
                <div class="chart-container">
                    <canvas id="probabilityChart"></canvas>
                </div>
            </div>

            <div class="section recommendation-section">
                <h2>🏥 综合诊断与建议</h2>
"""

            # 添加诊断内容
            predicted_class = result_data['predicted_class_name']
            if "NonDemented" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #00b894;">
                    风险等级: 低风险 ✅
                </div>
                <p>根据AI分析，未检测到明显的痴呆症状。大脑结构显示正常特征。</p>
                <ul class="recommendation-list">
                    <li>🏃‍♂️ 继续保持规律的体育锻炼</li>
                    <li>🥗 维持健康的饮食习惯</li>
                    <li>🧠 保持智力活动和社交互动</li>
                    <li>🏥 定期进行健康检查</li>
                </ul>
"""
            elif "VeryMild" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #fdcb6e;">
                    风险等级: 轻微风险 ⚠️
                </div>
                <p>检测到非常轻微的认知变化。这可能是正常老化过程，但建议进行更详细的评估。</p>
                <ul class="recommendation-list">
                    <li>👨‍⚕️ 建议咨询神经科医生</li>
                    <li>🧪 进行详细的神经心理学评估</li>
                    <li>📅 定期随访观察变化</li>
                    <li>🧘‍♀️ 加强认知训练活动</li>
                </ul>
"""
            elif "Mild" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #e17055;">
                    风险等级: 中等风险 ⚠️
                </div>
                <p>检测到轻度痴呆的特征。建议尽快咨询神经科医生，进行全面的认知评估。</p>
                <ul class="recommendation-list">
                    <li>🚨 尽快就医咨询专业医生</li>
                    <li>🧠 进行全面的认知功能评估</li>
                    <li>💊 考虑药物治疗干预</li>
                    <li>👨‍👩‍👧‍👦 家属应给予更多关注和支持</li>
                </ul>
"""
            else:
                html_content += """
                <div class="result-highlight" style="color: #d63031;">
                    风险等级: 高风险 🚨
                </div>
                <p>检测到中度痴呆的特征。强烈建议立即就医，需要专业医生的诊断和治疗干预。</p>
                <ul class="recommendation-list">
                    <li>🏥 立即就医，寻求专业治疗</li>
                    <li>💊 制定个性化治疗方案</li>
                    <li>🏠 考虑日常生活照护安排</li>
                    <li>👨‍👩‍👧‍👦 家属需要专业指导和支持</li>
                </ul>
"""

            html_content += f"""
            </div>

            <div class="section disclaimer-section">
                <h2>⚠️ 重要免责声明</h2>
                <p><strong>本AI分析仅供研究和参考使用，不能替代专业医学诊断。</strong></p>
                <p>任何医疗决策都应基于合格医疗专业人员的建议。请咨询神经科医生或相关专家获取准确的诊断和治疗方案。</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 AI Medical Solutions | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>

    <script>
        // 创建概率分布图表
        const ctx = document.getElementById('probabilityChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'doughnut',
            data: {{
                labels: {[item['name'] for item in chart_data]},
                datasets: [{{
                    data: {[item['value'] for item in chart_data]},
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom',
                        labels: {{
                            padding: 20,
                            font: {{
                                size: 14
                            }}
                        }}
                    }},
                    tooltip: {{
                        callbacks: {{
                            label: function(context) {{
                                return context.label + ': ' + context.parsed.toFixed(2) + '%';
                            }}
                        }}
                    }}
                }}
            }}
        }});

        // 添加打印功能
        function printReport() {{
            window.print();
        }}

        // 添加打印按钮
        document.addEventListener('DOMContentLoaded', function() {{
            const printBtn = document.createElement('button');
            printBtn.innerHTML = '🖨️ 打印报告';
            printBtn.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 10px 20px; background: #74b9ff; color: white; border: none; border-radius: 5px; cursor: pointer; z-index: 1000;';
            printBtn.onclick = printReport;
            document.body.appendChild(printBtn);
        }});
    </script>
</body>
</html>
"""

            # 写入HTML文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            # 如果HTML生成失败，创建简单的HTML
            self.create_simple_html_report(result_data, file_path)

    def create_simple_html_report(self, result_data, file_path):
        """创建简单的HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI痴呆症诊断报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ text-align: center; color: #333; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>AI痴呆症诊断报告</h1>
    </div>
    <div class="section">
        <h2>基本信息</h2>
        <p>分析时间: {result_data['timestamp']}</p>
        <p>图像文件: {os.path.basename(result_data['image_path'])}</p>
    </div>
    <div class="section">
        <h2>分析结果</h2>
        <p>预测分类: {result_data['predicted_class_name']}</p>
        <p>置信度: {result_data['confidence']:.2%}</p>
    </div>
    <div class="section">
        <h2>概率分布</h2>
"""
        for i, prob in enumerate(result_data['probabilities']):
            html_content += f"<p>{self.class_labels[i]}: {prob:.4f} ({prob:.2%})</p>"

        html_content += """
    </div>
    <div class="section">
        <h2>免责声明</h2>
        <p>本AI分析仅供研究和参考使用，不能替代专业医学诊断。</p>
    </div>
</body>
</html>
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def create_simple_text_report(self, result_data, file_path):
        """创建简单的文本报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("AI痴呆症检测报告\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"分析时间: {result_data['timestamp']}\n")
            f.write(f"图像文件: {os.path.basename(result_data['image_path'])}\n\n")
            f.write(f"预测结果: {result_data['predicted_class_name']}\n")
            f.write(f"置信度: {result_data['confidence']:.2%}\n\n")
            f.write("详细概率分布:\n")
            for i, prob in enumerate(result_data['probabilities']):
                f.write(f"  {self.class_labels[i]}: {prob:.4f} ({prob:.2%})\n")
            f.write("\n注意：此结果仅供参考，不能替代专业医学诊断。\n")

    def test_image_validation(self):
        """测试图像验证功能"""
        if not self.current_image_path:
            messagebox.showwarning("警告", "请先选择一张图像")
            return

        try:
            # 执行验证
            validation_result = self.validate_medical_image(self.current_image_path)

            # 显示详细验证结果
            result_window = ctk.CTkToplevel(self.root)
            result_window.title("图像验证详细结果")
            result_window.geometry("600x500")

            # 创建滚动框
            scroll_frame = ctk.CTkScrollableFrame(result_window)
            scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)

            # 基本信息
            info_text = f"""
🔍 图像验证详细报告

📁 文件: {os.path.basename(self.current_image_path)}
📏 尺寸: {validation_result['image_info']['size']}
📊 通道: {validation_result['image_info']['channels']}
📄 格式: {validation_result['image_info']['format']}

🎯 医学评分: {validation_result.get('medical_score', 0)}
⚠️ 警告数量: {len(validation_result['warnings'])}
🔒 验证结果: {'通过' if validation_result['is_valid'] else '拒绝'}
📈 置信度: {validation_result.get('confidence', '未知')}

⚠️ 警告列表:
"""

            if validation_result['warnings']:
                for i, warning in enumerate(validation_result['warnings'], 1):
                    info_text += f"{i}. {warning}\n"
            else:
                info_text += "无警告\n"

            if 'recommendation' in validation_result:
                info_text += f"\n💡 建议: {validation_result['recommendation']}"

            info_label = ctk.CTkLabel(
                scroll_frame,
                text=info_text,
                font=ctk.CTkFont(size=12),
                justify="left"
            )
            info_label.pack(pady=10, padx=10)

        except Exception as e:
            messagebox.showerror("错误", f"验证测试失败：{e}")

    def force_analysis_without_validation(self):
        """强制分析（跳过验证）"""
        if not self.model:
            messagebox.showerror("错误", "AI模型未加载")
            return

        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择图像文件")
            return

        # 确认对话框
        if not messagebox.askyesno("确认强制分析",
                                  "⚠️ 您确定要跳过图像验证直接进行分析吗？\n\n"
                                  "这可能会对不合适的图像产生无意义的结果。"):
            return

        # 禁用按钮，显示进度
        self.analyze_btn.configure(state="disabled", text="⚡ 强制分析中...")
        self.force_btn.configure(state="disabled")
        self.progress.set(0)

        # 在后台线程中进行强制分析
        threading.Thread(target=self.perform_force_analysis, daemon=True).start()

    def perform_force_analysis(self):
        """执行强制分析（跳过验证）"""
        try:
            self.root.after(0, lambda: self.progress.set(0.3))

            # 加载和预处理图像
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            self.root.after(0, lambda: self.progress.set(0.6))

            # 进行预测
            predictions = self.model.predict(img_array, verbose=0)
            predicted_class = self.np.argmax(predictions, axis=1)
            prediction_probs = predictions[0].tolist()

            self.root.after(0, lambda: self.progress.set(0.9))

            # 准备结果（标记为强制分析）
            result_data = {
                'predicted_class': predicted_class[0],
                'predicted_class_name': self.class_labels[predicted_class[0]],
                'confidence': max(prediction_probs),
                'probabilities': prediction_probs,
                'image_path': self.current_image_path,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'forced_analysis': True
            }

            self.root.after(0, lambda: self.progress.set(1.0))
            self.root.after(0, self.display_results, result_data)

        except Exception as e:
            self.root.after(0, self.on_analysis_error, str(e))
        finally:
            # 重置按钮状态
            self.root.after(0, lambda: self.analyze_btn.configure(state="normal", text="🔍 开始AI分析"))
            self.root.after(0, lambda: self.force_btn.configure(state="normal"))

    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        finally:
            # 确保摄像头被正确释放
            if self.camera:
                self.camera.release()

if __name__ == "__main__":
    app = AIDetectionApp()
    app.run()
