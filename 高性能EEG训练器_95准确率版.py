"""
🧠 高性能EEG训练器 - 95%准确率版
保持95%以上准确率，同时优化过拟合问题
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras import layers, models, regularizers, callbacks
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import mne
import joblib
import warnings
warnings.filterwarnings('ignore')

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

class HighPerformanceEEGTrainer:
    """高性能EEG训练器 - 95%准确率版"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        # EEG参数
        self.n_channels = 19
        self.n_samples = 128
        self.sampling_rate = 128
        
        print("🧠 高性能EEG训练器初始化 - 95%准确率版")
        print("🎯 目标: 保持95%+准确率，优化泛化能力")
    
    def load_eeg_data(self, split_name):
        """加载EEG数据"""
        print(f"📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        labels_df = pd.read_csv(labels_file, sep='\t')
        
        X_data = []
        y_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            set_files = [f for f in os.listdir(split_dir) 
                        if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                data = raw.get_data()
                
                # 创建高质量epochs
                epochs = self.create_high_quality_epochs(data)
                
                for epoch in epochs:
                    X_data.append(epoch)
                    y_labels.append(label)
                
            except Exception as e:
                print(f"❌ 加载{subject_id}失败: {e}")
                continue
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        print(f"✅ {split_name}数据加载完成: {X.shape}")
        return X, y
    
    def create_high_quality_epochs(self, data):
        """创建高质量epochs - 向量化优化版"""
        n_channels, n_timepoints = data.shape

        # 🚀 向量化处理通道数
        if n_channels > self.n_channels:
            data = data[:self.n_channels, :]
        elif n_channels < self.n_channels:
            padded_data = np.zeros((self.n_channels, n_timepoints))
            padded_data[:n_channels, :] = data
            data = padded_data

        # 🚀 向量化创建基础epochs
        n_basic_epochs = n_timepoints // self.n_samples
        if n_basic_epochs == 0:
            return []

        # 一次性切片所有基础epochs
        basic_epochs = []
        for i in range(n_basic_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            basic_epochs.append(data[:, start_idx:end_idx])

        # 🚀 向量化数据增强
        step_size = self.n_samples // 2
        max_augment = len(basic_epochs)
        augment_epochs = []

        augment_count = 0
        for i in range(0, n_timepoints - self.n_samples, step_size):
            if augment_count >= max_augment:
                break

            start_idx = i
            end_idx = start_idx + self.n_samples
            epoch = data[:, start_idx:end_idx]

            # 向量化噪声添加
            noise = np.random.normal(0, 0.005, epoch.shape)
            epoch_noisy = epoch + noise

            augment_epochs.append(epoch_noisy)
            augment_count += 1

        # 合并所有epochs
        all_epochs = basic_epochs + augment_epochs

        return all_epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据 - 高速向量化版本"""
        print("🔧 预处理数据...")
        print(f"📊 输入数据形状: {X.shape}")

        # 🚀 超高速向量化标准化
        # 计算每个样本每个通道的均值和标准差 (一次性计算)
        means = np.mean(X, axis=2, keepdims=True)  # shape: (n_samples, n_channels, 1)
        stds = np.std(X, axis=2, keepdims=True)    # shape: (n_samples, n_channels, 1)

        # 避免除零错误
        stds = np.where(stds == 0, 1, stds)

        # 向量化标准化 (一行代码完成所有计算)
        X_processed = (X - means) / stds

        print("✅ 向量化标准化完成")

        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)

        n_classes = len(self.label_encoder.classes_)
        y_categorical = tf.keras.utils.to_categorical(y_encoded, n_classes)

        # 添加通道维度
        X_processed = np.expand_dims(X_processed, axis=-1)

        print(f"✅ 预处理完成: {X_processed.shape}")
        print(f"🎯 标准化统计: 均值={np.mean(X_processed):.6f}, 标准差={np.std(X_processed):.6f}")
        return X_processed, y_categorical
    
    def build_high_performance_model(self, n_classes):
        """构建高性能模型 - 95%准确率版"""
        print("🏗️ 构建高性能模型 - 95%准确率版...")
        
        # 输入层
        inputs = layers.Input(shape=(self.n_channels, self.n_samples, 1))
        
        # 第一个卷积块 - 保持足够的特征提取能力
        x = layers.Conv2D(32, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.005))(inputs)  # 轻微正则化
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(32, (self.n_channels, 1), activation='relu',
                         kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.3)(x)  # 适度dropout
        
        # 第二个卷积块
        x = layers.Conv2D(64, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.3)(x)
        
        # 第三个卷积块
        x = layers.Conv2D(128, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.4)(x)
        
        # 第四个卷积块 - 增强特征提取
        x = layers.Conv2D(256, (1, 5), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 2))(x)
        x = layers.Dropout(0.4)(x)
        
        # 全局平均池化
        x = layers.GlobalAveragePooling2D()(x)
        
        # 全连接层 - 保持足够的分类能力
        x = layers.Dense(512, activation='relu',
                        kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        x = layers.Dense(256, activation='relu',
                        kernel_regularizer=regularizers.l2(0.005))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        # 输出层
        outputs = layers.Dense(n_classes, activation='softmax')(x)
        
        model = models.Model(inputs=inputs, outputs=outputs)
        
        # 优化的编译参数
        model.compile(
            optimizer=tf.keras.optimizers.Adam(
                learning_rate=0.001,  # 保持合适的学习率
                beta_1=0.9,
                beta_2=0.999,
                epsilon=1e-7
            ),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print(f"✅ 高性能模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model
    
    def create_balanced_callbacks(self):
        """创建平衡的回调函数"""
        callbacks_list = [
            # 平衡的早停策略
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=12,  # 给模型更多时间达到高准确率
                restore_best_weights=True,
                verbose=1,
                min_delta=0.001,
                mode='max'
            ),
            
            # 温和的学习率调度
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,  # 温和的衰减
                patience=6,
                min_lr=1e-6,
                verbose=1
            ),
            
            # 模型检查点
            callbacks.ModelCheckpoint(
                filepath='best_high_performance_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1,
                mode='max'
            ),
            
            # 自定义回调 - 监控过拟合
            OverfittingMonitor()
        ]
        
        return callbacks_list
    
    def train_high_performance_model(self, epochs=30, batch_size=32):
        """训练高性能模型"""
        print("🚀 开始高性能训练 - 30轮...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        print("🎯 目标: 95%+准确率，优化泛化能力")
        
        # 加载数据
        X_train, y_train = self.load_eeg_data('train')
        X_val, y_val = self.load_eeg_data('val')
        
        # 预处理
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 构建模型
        n_classes = y_train.shape[1]
        self.model = self.build_high_performance_model(n_classes)
        
        # 创建回调
        callbacks_list = self.create_balanced_callbacks()
        
        # 训练模型
        print("🎯 开始训练...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks_list,
            verbose=1,
            shuffle=True
        )
        
        print("✅ 高性能训练完成!")
        
        # 评估模型
        self.evaluate_high_performance_model()
        
        # 保存模型
        self.model.save('final_high_performance_eeg_model.h5')
        joblib.dump(self.label_encoder, 'high_performance_eeg_label_encoder.pkl')
        
        print("💾 高性能模型已保存")
        
        return self.history
    
    def evaluate_high_performance_model(self):
        """评估高性能模型"""
        print("\n📊 评估高性能模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_eeg_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        
        # 预测
        y_pred_prob = self.model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)
        
        # 计算指标
        test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
        
        print(f"📈 高性能模型测试结果:")
        print(f"   测试准确率: {test_acc:.4f} ({test_acc*100:.2f}%)")
        print(f"   测试损失: {test_loss:.4f}")
        
        # 训练历史分析
        if self.history:
            final_train_acc = self.history.history['accuracy'][-1]
            final_val_acc = self.history.history['val_accuracy'][-1]
            max_train_acc = max(self.history.history['accuracy'])
            max_val_acc = max(self.history.history['val_accuracy'])
            overfitting_gap = final_train_acc - final_val_acc
            
            print(f"\n📊 性能分析:")
            print(f"   最终训练准确率: {final_train_acc:.4f} ({final_train_acc*100:.2f}%)")
            print(f"   最终验证准确率: {final_val_acc:.4f} ({final_val_acc*100:.2f}%)")
            print(f"   最高训练准确率: {max_train_acc:.4f} ({max_train_acc*100:.2f}%)")
            print(f"   最高验证准确率: {max_val_acc:.4f} ({max_val_acc*100:.2f}%)")
            print(f"   过拟合差距: {overfitting_gap:.4f} ({overfitting_gap*100:.2f}%)")
            
            # 性能评估
            if final_train_acc >= 0.95:
                print("✅ 成功达到95%+训练准确率目标!")
            else:
                print(f"⚠️ 训练准确率 {final_train_acc*100:.2f}% 未达到95%目标")
            
            if final_val_acc >= 0.85:
                print("✅ 验证准确率表现优秀!")
            elif final_val_acc >= 0.75:
                print("✅ 验证准确率表现良好!")
            else:
                print("⚠️ 验证准确率需要进一步优化")
            
            if overfitting_gap < 0.10:
                print("✅ 过拟合控制优秀!")
            elif overfitting_gap < 0.20:
                print("✅ 过拟合控制良好!")
            else:
                print("⚠️ 仍存在一定程度的过拟合")
        
        return test_acc, test_loss


class OverfittingMonitor(callbacks.Callback):
    """过拟合监控回调"""
    
    def on_epoch_end(self, epoch, logs=None):
        train_acc = logs.get('accuracy')
        val_acc = logs.get('val_accuracy')
        
        if train_acc and val_acc:
            gap = train_acc - val_acc
            if gap > 0.25:  # 过拟合警告阈值
                print(f"\n⚠️ 过拟合警告: 训练验证差距 {gap:.3f}")


def main():
    """主函数"""
    print("🧠 高性能EEG训练器 - 95%准确率版")
    print("=" * 50)
    
    trainer = HighPerformanceEEGTrainer()
    
    # 开始高性能训练
    history = trainer.train_high_performance_model(epochs=30, batch_size=32)
    
    print("\n🎉 高性能训练完成!")
    print("📁 输出文件:")
    print("   🎯 final_high_performance_eeg_model.h5")
    print("   📊 high_performance_eeg_label_encoder.pkl")
    print("   ✅ best_high_performance_eeg_model.h5")
    
    print("\n🏆 性能目标:")
    print("   🎯 训练准确率: ≥95%")
    print("   📊 验证准确率: ≥75%")
    print("   ⚖️ 过拟合差距: <20%")


if __name__ == "__main__":
    main()
