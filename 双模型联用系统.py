"""
双模型联用系统
整合CT图像识别和症状分类两个模型
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import os
from tkinter import filedialog, messagebox
import tkinter as tk
from tkinter import ttk
import threading
from datetime import datetime
import warnings

# 抑制警告
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class DualModelSystem:
    """双模型联用系统"""
    
    def __init__(self):
        # 模型路径
        self.ct_detection_path = r"D:\模型开发\ct_other_model.h5"
        self.ct_classification_path = r"D:\模型开发\ct_class.h5"
        
        # 模型实例
        self.ct_detection_model = None
        self.ct_classification_model = None
        
        # 分类标签（根据您之前的模型）
        self.class_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 测试结果
        self.test_results = []
        
        # 创建GUI
        self.setup_gui()
        self.load_models()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("🧠 AI痴呆症识别系统 - 双模型联用版")
        self.root.geometry("900x700")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="🧠 AI痴呆症识别系统 - 双模型联用版", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="CT图像验证 + 症状智能分析", 
                                  font=("Arial", 12), foreground="gray")
        subtitle_label.grid(row=1, column=0, columnspan=4, pady=(0, 20))
        
        # 模型状态区域
        status_frame = ttk.LabelFrame(main_frame, text="模型状态", padding="10")
        status_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 20))
        
        self.ct_detection_status = ttk.Label(status_frame, text="CT识别模型: 🔴 未加载", 
                                           font=("Arial", 10))
        self.ct_detection_status.grid(row=0, column=0, padx=10, sticky=tk.W)
        
        self.ct_classification_status = ttk.Label(status_frame, text="症状分析模型: 🔴 未加载", 
                                                font=("Arial", 10))
        self.ct_classification_status.grid(row=0, column=1, padx=10, sticky=tk.W)
        
        self.system_status = ttk.Label(status_frame, text="系统状态: ⏳ 初始化中...", 
                                     font=("Arial", 10, "bold"))
        self.system_status.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(0, 20))
        
        ttk.Button(button_frame, text="📁 选择图像分析", 
                  command=self.analyze_single_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📂 批量分析", 
                  command=self.analyze_batch_images).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 模型信息", 
                  command=self.show_model_info).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 清空结果", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='determinate')
        self.progress.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.grid(row=5, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, height=25, width=100, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 统计信息
        self.stats_label = ttk.Label(main_frame, text="统计信息: 暂无分析", 
                                    font=("Arial", 10))
        self.stats_label.grid(row=6, column=0, columnspan=4, pady=(10, 0))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def load_models(self):
        """异步加载模型"""
        def load_models_thread():
            try:
                # 加载CT识别模型
                if os.path.exists(self.ct_detection_path):
                    self.ct_detection_model = tf.keras.models.load_model(self.ct_detection_path)
                    self.root.after(0, self.update_ct_detection_status, True)
                else:
                    self.root.after(0, self.update_ct_detection_status, False)
                
                # 加载症状分析模型
                if os.path.exists(self.ct_classification_path):
                    self.ct_classification_model = tf.keras.models.load_model(self.ct_classification_path)
                    self.root.after(0, self.update_ct_classification_status, True)
                else:
                    self.root.after(0, self.update_ct_classification_status, False)
                
                # 检查系统状态
                if self.ct_detection_model and self.ct_classification_model:
                    self.root.after(0, self.update_system_status, "ready")
                else:
                    self.root.after(0, self.update_system_status, "error")
                    
            except Exception as e:
                self.root.after(0, self.update_system_status, f"error: {e}")
        
        threading.Thread(target=load_models_thread, daemon=True).start()
    
    def update_ct_detection_status(self, success):
        """更新CT识别模型状态"""
        if success:
            self.ct_detection_status.config(text="CT识别模型: 🟢 已加载", foreground="green")
            self.log_result("✅ CT识别模型加载成功")
        else:
            self.ct_detection_status.config(text="CT识别模型: 🔴 加载失败", foreground="red")
            self.log_result("❌ CT识别模型加载失败")
    
    def update_ct_classification_status(self, success):
        """更新症状分析模型状态"""
        if success:
            self.ct_classification_status.config(text="症状分析模型: 🟢 已加载", foreground="green")
            self.log_result("✅ 症状分析模型加载成功")
        else:
            self.ct_classification_status.config(text="症状分析模型: 🔴 加载失败", foreground="red")
            self.log_result("❌ 症状分析模型加载失败")
    
    def update_system_status(self, status):
        """更新系统状态"""
        if status == "ready":
            self.system_status.config(text="系统状态: ✅ 就绪", foreground="green")
            self.log_result("🎉 双模型系统初始化完成，可以开始分析！")
        elif status == "error":
            self.system_status.config(text="系统状态: ❌ 错误", foreground="red")
            self.log_result("❌ 系统初始化失败，请检查模型文件")
        else:
            self.system_status.config(text=f"系统状态: ❌ {status}", foreground="red")
            self.log_result(f"❌ 系统错误: {status}")
    
    def preprocess_image(self, image_path):
        """预处理图像"""
        try:
            # 加载图像
            image = Image.open(image_path)
            
            # 转换为RGB
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整尺寸为150x150（与您的模型一致）
            image = image.resize((150, 150))
            
            # 转换为数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
        except Exception as e:
            self.log_result(f"❌ 图像预处理失败: {e}")
            return None
    
    def detect_ct_image(self, image_array):
        """步骤1: 检测是否为CT图像"""
        try:
            if self.ct_detection_model is None:
                return None, "CT识别模型未加载"
            
            # 进行预测
            prediction = self.ct_detection_model.predict(image_array, verbose=0)
            confidence = float(prediction[0][0])
            is_ct = confidence > 0.5
            
            return {
                'is_ct': is_ct,
                'confidence': confidence,
                'raw_prediction': prediction[0].tolist()
            }, None
            
        except Exception as e:
            return None, str(e)
    
    def classify_symptoms(self, image_array):
        """步骤2: 分析症状（仅在确认为CT图像后执行）"""
        try:
            if self.ct_classification_model is None:
                return None, "症状分析模型未加载"
            
            # 进行预测
            prediction = self.ct_classification_model.predict(image_array, verbose=0)
            predicted_class_idx = np.argmax(prediction, axis=1)[0]
            confidence = float(np.max(prediction))
            probabilities = prediction[0].tolist()
            
            # 构建详细结果
            result = {
                'predicted_class': self.class_labels[predicted_class_idx],
                'predicted_class_index': int(predicted_class_idx),
                'confidence': confidence,
                'probabilities': probabilities,
                'probability_details': {}
            }
            
            # 添加每个类别的详细概率
            for i, label in enumerate(self.class_labels):
                result['probability_details'][label] = {
                    'probability': float(probabilities[i]),
                    'percentage': f"{probabilities[i]*100:.2f}%"
                }
            
            return result, None
            
        except Exception as e:
            return None, str(e)
    
    def analyze_image_complete(self, image_path):
        """完整的图像分析流程"""
        analysis_start = datetime.now()
        
        self.log_result(f"\n{'='*80}")
        self.log_result(f"🔍 开始分析图像: {os.path.basename(image_path)}")
        self.log_result(f"📁 完整路径: {image_path}")
        self.log_result(f"⏰ 开始时间: {analysis_start.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 步骤1: 图像预处理
        self.progress['value'] = 10
        self.root.update()
        self.log_result(f"\n📊 步骤1: 图像预处理...")
        
        image_array = self.preprocess_image(image_path)
        if image_array is None:
            self.log_result("❌ 分析终止: 图像预处理失败")
            return
        
        self.log_result("✅ 图像预处理完成")
        
        # 步骤2: CT图像检测
        self.progress['value'] = 30
        self.root.update()
        self.log_result(f"\n🔍 步骤2: CT图像检测...")
        
        ct_result, ct_error = self.detect_ct_image(image_array)
        if ct_error:
            self.log_result(f"❌ CT检测失败: {ct_error}")
            return
        
        is_ct = ct_result['is_ct']
        ct_confidence = ct_result['confidence']
        
        self.log_result(f"🎯 CT检测结果: {'✅ 是CT图像' if is_ct else '❌ 不是CT图像'}")
        self.log_result(f"📊 CT检测置信度: {ct_confidence:.6f} ({ct_confidence*100:.2f}%)")
        
        # 置信度等级评估
        if ct_confidence > 0.9:
            confidence_level = "非常高"
        elif ct_confidence > 0.7:
            confidence_level = "高"
        elif ct_confidence > 0.5:
            confidence_level = "中等"
        else:
            confidence_level = "低"
        
        self.log_result(f"📈 置信度等级: {confidence_level}")
        
        if not is_ct:
            self.progress['value'] = 100
            self.root.update()
            self.log_result(f"\n⚠️ 分析终止: 输入图像不是CT图像")
            self.log_result(f"💡 建议: 请上传CT扫描图像进行痴呆症分析")
            
            # 记录结果
            self.test_results.append({
                'file': os.path.basename(image_path),
                'is_ct': False,
                'ct_confidence': ct_confidence,
                'analysis_result': None,
                'timestamp': analysis_start.isoformat()
            })
            self.update_statistics()
            return
        
        # 步骤3: 症状分析
        self.progress['value'] = 60
        self.root.update()
        self.log_result(f"\n🧠 步骤3: 痴呆症症状分析...")
        
        symptom_result, symptom_error = self.classify_symptoms(image_array)
        if symptom_error:
            self.log_result(f"❌ 症状分析失败: {symptom_error}")
            return
        
        # 显示详细分析结果
        self.progress['value'] = 90
        self.root.update()
        
        predicted_class = symptom_result['predicted_class']
        symptom_confidence = symptom_result['confidence']
        
        self.log_result(f"\n🎯 症状分析结果:")
        self.log_result(f"   预测类别: {predicted_class}")
        self.log_result(f"   置信度: {symptom_confidence:.6f} ({symptom_confidence*100:.2f}%)")
        
        self.log_result(f"\n📊 详细概率分布:")
        for label, details in symptom_result['probability_details'].items():
            self.log_result(f"   {label}: {details['percentage']}")
        
        # 生成建议
        self.log_result(f"\n💡 分析建议:")
        if symptom_confidence > 0.8:
            self.log_result("   • 预测置信度较高，结果可信度良好")
        elif symptom_confidence > 0.6:
            self.log_result("   • 预测置信度中等，建议结合其他检查结果")
        else:
            self.log_result("   • 预测置信度较低，建议进行进一步检查")
        
        if 'NonDemented' in predicted_class:
            self.log_result("   • 建议定期进行认知功能检查")
        else:
            self.log_result("   • 建议咨询专业医生进行详细诊断")
        
        # 完成分析
        self.progress['value'] = 100
        self.root.update()
        
        analysis_end = datetime.now()
        processing_time = (analysis_end - analysis_start).total_seconds()
        
        self.log_result(f"\n✅ 分析完成!")
        self.log_result(f"⏱️ 总耗时: {processing_time:.2f} 秒")
        self.log_result(f"🏁 结束时间: {analysis_end.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 记录完整结果
        self.test_results.append({
            'file': os.path.basename(image_path),
            'is_ct': True,
            'ct_confidence': ct_confidence,
            'analysis_result': symptom_result,
            'processing_time': processing_time,
            'timestamp': analysis_start.isoformat()
        })
        
        self.update_statistics()
    
    def analyze_single_image(self):
        """分析单张图像"""
        if not (self.ct_detection_model and self.ct_classification_model):
            messagebox.showerror("错误", "模型未完全加载")
            return
        
        # 选择图像文件
        file_path = filedialog.askopenfilename(
            title="选择要分析的图像",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        # 在后台线程中执行分析
        def analyze_thread():
            self.analyze_image_complete(file_path)
            self.root.after(0, lambda: self.progress.configure(value=0))
        
        threading.Thread(target=analyze_thread, daemon=True).start()
    
    def analyze_batch_images(self):
        """批量分析图像"""
        if not (self.ct_detection_model and self.ct_classification_model):
            messagebox.showerror("错误", "模型未完全加载")
            return
        
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择包含图像的文件夹")
        if not folder_path:
            return
        
        # 获取所有图像文件
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        image_files = [f for f in os.listdir(folder_path) 
                      if f.lower().endswith(image_extensions)]
        
        if not image_files:
            messagebox.showinfo("提示", "文件夹中没有找到图像文件")
            return
        
        self.log_result(f"\n{'='*80}")
        self.log_result(f"📂 开始批量分析")
        self.log_result(f"📁 文件夹: {folder_path}")
        self.log_result(f"📊 找到 {len(image_files)} 张图像")
        
        # 在后台线程中执行批量分析
        def batch_analyze_thread():
            for i, filename in enumerate(image_files, 1):
                file_path = os.path.join(folder_path, filename)
                self.log_result(f"\n[{i}/{len(image_files)}] 分析: {filename}")
                self.analyze_image_complete(file_path)
            
            self.log_result(f"\n🎉 批量分析完成! 共处理 {len(image_files)} 张图像")
            self.root.after(0, lambda: self.progress.configure(value=0))
        
        threading.Thread(target=batch_analyze_thread, daemon=True).start()
    
    def show_model_info(self):
        """显示模型信息"""
        self.log_result(f"\n{'='*80}")
        self.log_result("🤖 双模型系统信息:")
        
        # CT识别模型信息
        if self.ct_detection_model:
            self.log_result(f"\n🔍 CT识别模型:")
            self.log_result(f"   路径: {self.ct_detection_path}")
            self.log_result(f"   输入形状: {self.ct_detection_model.input_shape}")
            self.log_result(f"   输出形状: {self.ct_detection_model.output_shape}")
            self.log_result(f"   参数数量: {self.ct_detection_model.count_params():,}")
        else:
            self.log_result(f"\n❌ CT识别模型未加载")
        
        # 症状分析模型信息
        if self.ct_classification_model:
            self.log_result(f"\n🧠 症状分析模型:")
            self.log_result(f"   路径: {self.ct_classification_path}")
            self.log_result(f"   输入形状: {self.ct_classification_model.input_shape}")
            self.log_result(f"   输出形状: {self.ct_classification_model.output_shape}")
            self.log_result(f"   参数数量: {self.ct_classification_model.count_params():,}")
            self.log_result(f"   分类标签: {self.class_labels}")
        else:
            self.log_result(f"\n❌ 症状分析模型未加载")
    
    def update_statistics(self):
        """更新统计信息"""
        if not self.test_results:
            self.stats_label.config(text="统计信息: 暂无分析")
            return
        
        total_tests = len(self.test_results)
        ct_images = sum(1 for r in self.test_results if r['is_ct'])
        non_ct_images = total_tests - ct_images
        
        # 症状分析统计
        analyzed_images = sum(1 for r in self.test_results if r['analysis_result'] is not None)
        
        stats_text = (f"统计信息: 总分析 {total_tests} 张 | "
                     f"CT图像 {ct_images} 张 | "
                     f"非CT图像 {non_ct_images} 张 | "
                     f"完成症状分析 {analyzed_images} 张")
        
        self.stats_label.config(text=stats_text)
    
    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.progress['value'] = 0
        self.update_statistics()
    
    def log_result(self, message):
        """记录结果到文本框"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run(self):
        """运行系统"""
        self.root.mainloop()


def main():
    """主函数"""
    print("🚀 启动双模型联用系统...")
    system = DualModelSystem()
    system.run()


if __name__ == "__main__":
    main()
