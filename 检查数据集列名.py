"""
检查数据集的实际列名
"""

import pandas as pd
import os

audio_path = r"D:\模型开发\audio"

csv_files = {
    'acoustic': 'acoustic_features_dataset.csv',
    'cookie_theft': 'cookie_theft_dataset.csv', 
    'semantic_fluency': 'semantic_fluency_dataset.csv'
}

for name, filename in csv_files.items():
    file_path = os.path.join(audio_path, filename)
    if os.path.exists(file_path):
        df = pd.read_csv(file_path)
        print(f"\n📊 {name} 数据集:")
        print(f"   形状: {df.shape}")
        print(f"   列名: {list(df.columns)}")
        print(f"   前3行:")
        print(df.head(3))
    else:
        print(f"❌ 未找到文件: {filename}")
