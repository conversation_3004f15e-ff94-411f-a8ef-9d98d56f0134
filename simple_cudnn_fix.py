"""
🔧 简单cuDNN修复方案
适用于AutoDL环境，避免权限问题
"""

import os
import subprocess
import sys

def method1_conda_install():
    """方法1: 使用conda安装cuDNN"""
    print("🔧 方法1: 使用conda安装cuDNN")
    print("=" * 40)
    
    try:
        # 卸载可能冲突的版本
        print("📦 卸载旧版本...")
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'tensorflow', '-y'], 
                      capture_output=True)
        
        # 使用conda安装cudnn
        print("📦 安装cuDNN...")
        result = subprocess.run(['conda', 'install', 'cudnn=8.2.1', '-y'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ conda安装cuDNN成功")
        else:
            print(f"❌ conda安装失败: {result.stderr}")
            return False
        
        # 重新安装TensorFlow
        print("📦 重新安装TensorFlow...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'tensorflow==2.8.0'], 
                      capture_output=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
        return False

def method2_pip_install():
    """方法2: 使用pip安装cuDNN"""
    print("\n🔧 方法2: 使用pip安装cuDNN")
    print("=" * 40)
    
    try:
        # 安装nvidia-cudnn
        print("📦 安装nvidia-cudnn...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'nvidia-cudnn-cu11==*********', '--upgrade'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ pip安装cuDNN成功")
        else:
            print(f"❌ pip安装失败: {result.stderr}")
            return False
        
        # 设置环境变量
        print("🔧 设置环境变量...")
        cudnn_path = subprocess.run([
            sys.executable, '-c', 
            'import nvidia.cudnn; print(nvidia.cudnn.__file__)'
        ], capture_output=True, text=True)
        
        if cudnn_path.returncode == 0:
            cudnn_dir = os.path.dirname(cudnn_path.stdout.strip())
            os.environ['LD_LIBRARY_PATH'] = f"{cudnn_dir}/lib:{os.environ.get('LD_LIBRARY_PATH', '')}"
            print(f"✅ 设置LD_LIBRARY_PATH: {cudnn_dir}/lib")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法2失败: {e}")
        return False

def method3_downgrade_tensorflow():
    """方法3: 降级TensorFlow"""
    print("\n🔧 方法3: 降级TensorFlow")
    print("=" * 40)
    
    try:
        # 卸载当前TensorFlow
        print("📦 卸载当前TensorFlow...")
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'tensorflow', '-y'], 
                      capture_output=True)
        
        # 安装兼容版本
        print("📦 安装TensorFlow 2.6.0...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'tensorflow==2.6.0'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ TensorFlow降级成功")
            return True
        else:
            print(f"❌ TensorFlow降级失败: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ 方法3失败: {e}")
        return False

def test_cudnn():
    """测试cuDNN是否工作"""
    print("\n🧪 测试cuDNN")
    print("=" * 40)

    try:
        # 重新导入TensorFlow以重置状态
        import importlib
        import sys
        if 'tensorflow' in sys.modules:
            importlib.reload(sys.modules['tensorflow'])

        import tensorflow as tf
        print(f"📊 TensorFlow版本: {tf.__version__}")

        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 GPU数量: {len(gpus)}")

        if gpus:
            try:
                # 尝试设置GPU内存增长（如果还没初始化）
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print("✅ GPU内存增长设置成功")
            except RuntimeError as e:
                if "Physical devices cannot be modified" in str(e):
                    print("⚠️ GPU已初始化，跳过内存设置")
                else:
                    raise e

            # 测试卷积操作
            print("🧪 测试卷积操作...")
            try:
                with tf.device('/GPU:0'):
                    x = tf.random.normal((1, 19, 128, 1))
                    conv = tf.keras.layers.Conv2D(32, (1, 7), padding='same')
                    y = conv(x)
                    print(f"✅ 卷积测试成功: {x.shape} -> {y.shape}")
                return True
            except Exception as conv_error:
                print(f"❌ 卷积操作失败: {conv_error}")
                return False
        else:
            print("❌ 未检测到GPU")
            return False

    except Exception as e:
        print(f"❌ cuDNN测试失败: {e}")
        return False

def create_cpu_fallback():
    """创建CPU备用方案"""
    print("\n💻 创建CPU备用方案")
    print("=" * 40)
    
    cpu_script = '''
# 强制CPU模式脚本
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
tf.config.set_visible_devices([], 'GPU')

print("🔧 已强制切换到CPU模式")
print("💻 现在可以安全使用CPU训练")
'''
    
    with open('force_cpu.py', 'w', encoding='utf-8') as f:
        f.write(cpu_script)
    
    print("✅ CPU备用脚本已创建: force_cpu.py")
    print("💡 使用方法: exec(open('force_cpu.py').read())")

def main():
    """主修复流程"""
    print("🔧 AutoDL cuDNN问题修复工具")
    print("=" * 50)
    
    # 尝试方法1: conda安装
    if method1_conda_install():
        if test_cudnn():
            print("\n🎉 方法1成功! cuDNN已修复")
            print("🚀 现在可以运行: python complete_deep_eeg_trainer.py")
            return
    
    # 尝试方法2: pip安装
    if method2_pip_install():
        if test_cudnn():
            print("\n🎉 方法2成功! cuDNN已修复")
            print("🚀 现在可以运行: python complete_deep_eeg_trainer.py")
            return
    
    # 尝试方法3: 降级TensorFlow
    if method3_downgrade_tensorflow():
        if test_cudnn():
            print("\n🎉 方法3成功! cuDNN已修复")
            print("🚀 现在可以运行: python complete_deep_eeg_trainer.py")
            return
    
    # 所有方法都失败，提供CPU备用方案
    print("\n⚠️ 所有GPU修复方法都失败")
    print("💡 推荐使用CPU训练器")
    
    create_cpu_fallback()
    
    print("\n📋 推荐方案:")
    print("1. 运行CPU训练器: python cpu_only_eeg_trainer.py")
    print("2. 或者强制CPU模式后运行原训练器:")
    print("   exec(open('force_cpu.py').read())")
    print("   然后运行: python complete_deep_eeg_trainer.py")

if __name__ == "__main__":
    main()
