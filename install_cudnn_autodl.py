"""
🔧 AutoDL cuDNN安装脚本
详细指导在AutoDL Jupyter中安装cuDNN
"""

import os
import subprocess
import shutil
import tarfile

def check_current_status():
    """检查当前状态"""
    print("🔍 检查当前环境状态")
    print("=" * 50)
    
    # 检查CUDA版本
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA版本:")
            for line in result.stdout.split('\n'):
                if 'release' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ CUDA不可用")
    except:
        print("❌ nvcc命令未找到")
    
    # 检查当前cuDNN
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 GPU数量: {len(gpus)}")
        
        # 测试cuDNN
        try:
            with tf.device('/GPU:0' if gpus else '/CPU:0'):
                x = tf.random.normal((1, 10, 10, 1))
                conv = tf.keras.layers.Conv2D(1, 3)
                y = conv(x)
            print("✅ 当前cuDNN可用")
            return True
        except Exception as e:
            print(f"❌ 当前cuDNN不可用: {e}")
            return False
    except:
        print("❌ TensorFlow未安装")
        return False

def install_cudnn_from_archive():
    """从压缩包安装cuDNN"""
    print("\n🚀 开始安装cuDNN")
    print("=" * 50)
    
    # cuDNN压缩包路径
    cudnn_archive = "cudnn-linux-x86_64-9.10.2.21_cuda11-archive.tar.xz"
    
    # 检查文件是否存在
    if not os.path.exists(cudnn_archive):
        print(f"❌ cuDNN压缩包不存在: {cudnn_archive}")
        print("💡 请确保文件在当前目录中")
        return False
    
    print(f"✅ 找到cuDNN压缩包: {cudnn_archive}")
    
    try:
        # 1. 解压cuDNN
        print("📦 解压cuDNN压缩包...")
        with tarfile.open(cudnn_archive, 'r:xz') as tar:
            tar.extractall('.')
        
        # 查找解压后的目录
        extracted_dirs = [d for d in os.listdir('.') if d.startswith('cudnn-linux')]
        if not extracted_dirs:
            print("❌ 解压失败，未找到cuDNN目录")
            return False
        
        cudnn_dir = extracted_dirs[0]
        print(f"✅ 解压完成: {cudnn_dir}")
        
        # 2. 获取CUDA安装路径
        cuda_path = "/usr/local/cuda"
        if not os.path.exists(cuda_path):
            # 尝试其他可能的路径
            possible_paths = ["/usr/local/cuda-11", "/usr/local/cuda-11.2", "/usr/local/cuda-11.3"]
            for path in possible_paths:
                if os.path.exists(path):
                    cuda_path = path
                    break
        
        print(f"📍 CUDA路径: {cuda_path}")
        
        # 3. 复制cuDNN文件
        print("📋 复制cuDNN文件...")
        
        # 复制头文件
        include_src = os.path.join(cudnn_dir, "include")
        include_dst = os.path.join(cuda_path, "include")
        if os.path.exists(include_src):
            subprocess.run(['sudo', 'cp', '-r', f"{include_src}/*", include_dst], shell=True)
            print("✅ 头文件复制完成")
        
        # 复制库文件
        lib_src = os.path.join(cudnn_dir, "lib")
        lib_dst = os.path.join(cuda_path, "lib64")
        if os.path.exists(lib_src):
            subprocess.run(['sudo', 'cp', '-r', f"{lib_src}/*", lib_dst], shell=True)
            print("✅ 库文件复制完成")
        
        # 4. 设置权限
        print("🔐 设置文件权限...")
        subprocess.run(['sudo', 'chmod', 'a+r', f"{cuda_path}/include/cudnn*"])
        subprocess.run(['sudo', 'chmod', 'a+r', f"{cuda_path}/lib64/libcudnn*"])
        
        # 5. 更新链接库缓存
        print("🔄 更新链接库缓存...")
        subprocess.run(['sudo', 'ldconfig'])
        
        print("✅ cuDNN安装完成!")
        return True
        
    except Exception as e:
        print(f"❌ 安装失败: {e}")
        return False

def verify_installation():
    """验证安装"""
    print("\n🔍 验证cuDNN安装")
    print("=" * 50)
    
    try:
        # 重新导入TensorFlow
        import importlib
        import tensorflow as tf
        importlib.reload(tf)
        
        print(f"📊 TensorFlow版本: {tf.__version__}")
        
        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 GPU数量: {len(gpus)}")
        
        if gpus:
            # 设置GPU内存增长
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            
            # 测试cuDNN
            print("🧪 测试cuDNN功能...")
            with tf.device('/GPU:0'):
                # 创建测试数据
                x = tf.random.normal((32, 19, 128, 1))
                
                # 创建卷积层
                conv1 = tf.keras.layers.Conv2D(32, (1, 7), padding='same', activation='relu')
                conv2 = tf.keras.layers.Conv2D(64, (19, 1), activation='relu')
                
                # 执行卷积操作
                y1 = conv1(x)
                y2 = conv2(y1)
                
                print(f"✅ 卷积测试通过: {x.shape} -> {y1.shape} -> {y2.shape}")
            
            print("🎉 cuDNN安装验证成功!")
            return True
        else:
            print("⚠️ 未检测到GPU")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def cleanup():
    """清理临时文件"""
    print("\n🧹 清理临时文件")
    print("=" * 50)
    
    try:
        # 删除解压的目录
        extracted_dirs = [d for d in os.listdir('.') if d.startswith('cudnn-linux')]
        for dir_name in extracted_dirs:
            if os.path.isdir(dir_name):
                shutil.rmtree(dir_name)
                print(f"🗑️ 删除临时目录: {dir_name}")
        
        print("✅ 清理完成")
    except Exception as e:
        print(f"⚠️ 清理失败: {e}")

def create_test_script():
    """创建测试脚本"""
    print("\n📝 创建测试脚本")
    print("=" * 50)
    
    test_script = '''
"""
🧪 cuDNN测试脚本
验证cuDNN是否正常工作
"""

import tensorflow as tf
import numpy as np

def test_cudnn():
    print("🧪 cuDNN功能测试")
    print("=" * 40)
    
    # 检查GPU
    gpus = tf.config.list_physical_devices('GPU')
    print(f"📊 GPU数量: {len(gpus)}")
    
    if not gpus:
        print("❌ 未检测到GPU")
        return False
    
    try:
        # 设置GPU内存增长
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        
        # 创建测试模型
        model = tf.keras.Sequential([
            tf.keras.layers.Conv2D(32, (1, 7), padding='same', activation='relu', input_shape=(19, 128, 1)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv2D(32, (19, 1), activation='relu'),
            tf.keras.layers.GlobalAveragePooling2D(),
            tf.keras.layers.Dense(3, activation='softmax')
        ])
        
        # 编译模型
        model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        
        # 创建测试数据
        X = np.random.randn(10, 19, 128, 1).astype(np.float32)
        y = tf.keras.utils.to_categorical(np.random.randint(0, 3, 10), 3)
        
        # 测试训练
        print("🏋️ 测试模型训练...")
        history = model.fit(X, y, epochs=2, verbose=0)
        
        # 测试预测
        print("🔮 测试模型预测...")
        predictions = model.predict(X, verbose=0)
        
        print("✅ cuDNN测试通过!")
        print(f"📊 模型参数: {model.count_params():,}")
        print(f"📈 最终损失: {history.history['loss'][-1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ cuDNN测试失败: {e}")
        return False

if __name__ == "__main__":
    test_cudnn()
'''
    
    with open('test_cudnn.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print("✅ 测试脚本已创建: test_cudnn.py")

def main():
    """主安装流程"""
    print("🔧 AutoDL cuDNN安装向导")
    print("=" * 60)
    
    # 1. 检查当前状态
    current_ok = check_current_status()
    
    if current_ok:
        print("\n🎉 cuDNN已经正常工作，无需重新安装!")
        return
    
    # 2. 安装cuDNN
    install_success = install_cudnn_from_archive()
    
    if not install_success:
        print("\n❌ cuDNN安装失败")
        return
    
    # 3. 验证安装
    verify_success = verify_installation()
    
    # 4. 清理临时文件
    cleanup()
    
    # 5. 创建测试脚本
    create_test_script()
    
    if verify_success:
        print("\n🎉 cuDNN安装和验证成功!")
        print("📝 运行测试: python test_cudnn.py")
        print("🚀 现在可以使用GPU训练: python complete_deep_eeg_trainer.py")
    else:
        print("\n⚠️ cuDNN安装完成但验证失败")
        print("💡 建议使用CPU训练器: python cpu_only_eeg_trainer.py")

if __name__ == "__main__":
    main()
