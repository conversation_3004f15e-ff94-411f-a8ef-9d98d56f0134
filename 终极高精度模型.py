"""
终极高精度痴呆症检测模型
目标: >90% 准确率
使用最强组合: XGBoost + 深度特征工程 + 集成学习
"""

import pandas as pd
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

print("🚀 终极高精度痴呆症检测模型")
print("🎯 目标: >90% 准确率")
print("=" * 50)

# 加载数据
data_path = r"D:\模型开发\audio\processed_datasets"
train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))

print(f"📊 数据加载完成")
print(f"   训练集: {len(train_data)} 样本")
print(f"   验证集: {len(val_data)} 样本")
print(f"   测试集: {len(test_data)} 样本")

# 合并训练和验证集以获得更多数据
combined_train = pd.concat([train_data, val_data], ignore_index=True)
print(f"   合并训练集: {len(combined_train)} 样本")

# 准备特征和标签
feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
X_train = combined_train[feature_cols].values
y_train = combined_train['diagnosis_encoded'].values
X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"🔧 特征工程完成")
print(f"   特征维度: {X_train.shape[1]}")
print(f"   类别分布: {np.bincount(y_train)}")

# 高级特征工程
print("🧠 应用高级特征工程...")

# 1. 多项式特征 (选择性)
from sklearn.preprocessing import PolynomialFeatures
poly = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
X_train_poly = poly.fit_transform(X_train[:, :10])  # 只对前10个重要特征
X_test_poly = poly.transform(X_test[:, :10])

# 2. 特征选择
from sklearn.feature_selection import SelectKBest, f_classif
selector = SelectKBest(f_classif, k=min(200, X_train_poly.shape[1]))
X_train_selected = selector.fit_transform(X_train_poly, y_train)
X_test_selected = selector.transform(X_test_poly)

# 3. 组合原始特征和选择的多项式特征
X_train_final = np.hstack([X_train, X_train_selected])
X_test_final = np.hstack([X_test, X_test_selected])

print(f"   最终特征维度: {X_train_final.shape[1]}")

# 训练多个强力模型
models = {}
accuracies = {}

print("🚀 训练强力模型集合...")

# 1. XGBoost (通常最强)
print("   训练XGBoost...")
try:
    import xgboost as xgb
    
    xgb_model = xgb.XGBClassifier(
        n_estimators=500,
        max_depth=8,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='mlogloss',
        early_stopping_rounds=50
    )
    
    xgb_model.fit(
        X_train_final, y_train,
        eval_set=[(X_test_final, y_test)],
        verbose=False
    )
    
    xgb_pred = xgb_model.predict(X_test_final)
    xgb_acc = np.mean(xgb_pred == y_test)
    models['XGBoost'] = xgb_model
    accuracies['XGBoost'] = xgb_acc
    print(f"     XGBoost准确率: {xgb_acc:.4f} ({xgb_acc*100:.2f}%)")
    
except ImportError:
    print("     XGBoost未安装，跳过")

# 2. LightGBM (另一个强力模型)
print("   训练LightGBM...")
try:
    import lightgbm as lgb
    
    lgb_model = lgb.LGBMClassifier(
        n_estimators=500,
        max_depth=8,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        verbose=-1
    )
    
    lgb_model.fit(X_train_final, y_train)
    lgb_pred = lgb_model.predict(X_test_final)
    lgb_acc = np.mean(lgb_pred == y_test)
    models['LightGBM'] = lgb_model
    accuracies['LightGBM'] = lgb_acc
    print(f"     LightGBM准确率: {lgb_acc:.4f} ({lgb_acc*100:.2f}%)")
    
except ImportError:
    print("     LightGBM未安装，跳过")

# 3. CatBoost (处理类别特征很强)
print("   训练CatBoost...")
try:
    import catboost as cb
    
    cat_model = cb.CatBoostClassifier(
        iterations=500,
        depth=8,
        learning_rate=0.05,
        random_seed=42,
        verbose=False
    )
    
    cat_model.fit(X_train_final, y_train)
    cat_pred = cat_model.predict(X_test_final)
    cat_acc = np.mean(cat_pred == y_test)
    models['CatBoost'] = cat_model
    accuracies['CatBoost'] = cat_acc
    print(f"     CatBoost准确率: {cat_acc:.4f} ({cat_acc*100:.2f}%)")
    
except ImportError:
    print("     CatBoost未安装，跳过")

# 4. 随机森林 (稳定可靠)
print("   训练随机森林...")
from sklearn.ensemble import RandomForestClassifier

rf_model = RandomForestClassifier(
    n_estimators=500,
    max_depth=15,
    min_samples_split=2,
    min_samples_leaf=1,
    random_state=42,
    n_jobs=-1
)

rf_model.fit(X_train_final, y_train)
rf_pred = rf_model.predict(X_test_final)
rf_acc = np.mean(rf_pred == y_test)
models['RandomForest'] = rf_model
accuracies['RandomForest'] = rf_acc
print(f"     随机森林准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")

# 5. 极端随机树
print("   训练极端随机树...")
from sklearn.ensemble import ExtraTreesClassifier

et_model = ExtraTreesClassifier(
    n_estimators=500,
    max_depth=15,
    min_samples_split=2,
    min_samples_leaf=1,
    random_state=42,
    n_jobs=-1
)

et_model.fit(X_train_final, y_train)
et_pred = et_model.predict(X_test_final)
et_acc = np.mean(et_pred == y_test)
models['ExtraTrees'] = et_model
accuracies['ExtraTrees'] = et_acc
print(f"     极端随机树准确率: {et_acc:.4f} ({et_acc*100:.2f}%)")

# 6. 梯度提升
print("   训练梯度提升...")
from sklearn.ensemble import GradientBoostingClassifier

gb_model = GradientBoostingClassifier(
    n_estimators=500,
    max_depth=8,
    learning_rate=0.05,
    subsample=0.8,
    random_state=42
)

gb_model.fit(X_train_final, y_train)
gb_pred = gb_model.predict(X_test_final)
gb_acc = np.mean(gb_pred == y_test)
models['GradientBoosting'] = gb_model
accuracies['GradientBoosting'] = gb_acc
print(f"     梯度提升准确率: {gb_acc:.4f} ({gb_acc*100:.2f}%)")

# 集成学习 - 投票
print("🔗 创建集成模型...")
from sklearn.ensemble import VotingClassifier

# 选择表现最好的模型进行集成
best_models = sorted(accuracies.items(), key=lambda x: x[1], reverse=True)[:3]
print(f"   选择前3个最佳模型: {[name for name, acc in best_models]}")

voting_estimators = [(name, models[name]) for name, acc in best_models]
voting_clf = VotingClassifier(estimators=voting_estimators, voting='soft')
voting_clf.fit(X_train_final, y_train)
voting_pred = voting_clf.predict(X_test_final)
voting_acc = np.mean(voting_pred == y_test)

models['VotingEnsemble'] = voting_clf
accuracies['VotingEnsemble'] = voting_acc
print(f"   集成模型准确率: {voting_acc:.4f} ({voting_acc*100:.2f}%)")

# 加权集成 (给最好的模型更高权重)
print("⚖️ 创建加权集成...")
weights = []
model_list = []
for name, acc in best_models:
    weights.append(acc)
    model_list.append(models[name])

# 归一化权重
weights = np.array(weights)
weights = weights / weights.sum()

# 加权预测
weighted_probs = np.zeros((len(X_test_final), 3))
for i, model in enumerate(model_list):
    if hasattr(model, 'predict_proba'):
        probs = model.predict_proba(X_test_final)
    else:
        # 对于没有predict_proba的模型，创建one-hot编码
        pred = model.predict(X_test_final)
        probs = np.eye(3)[pred]
    weighted_probs += weights[i] * probs

weighted_pred = np.argmax(weighted_probs, axis=1)
weighted_acc = np.mean(weighted_pred == y_test)
print(f"   加权集成准确率: {weighted_acc:.4f} ({weighted_acc*100:.2f}%)")

# 找到最佳模型
all_accuracies = {**accuracies, 'WeightedEnsemble': weighted_acc}
best_name = max(all_accuracies, key=all_accuracies.get)
best_accuracy = all_accuracies[best_name]

print(f"\n📊 所有模型结果:")
for name, acc in sorted(all_accuracies.items(), key=lambda x: x[1], reverse=True):
    status = "🎯" if acc >= 0.90 else "📈"
    print(f"   {name}: {acc:.4f} ({acc*100:.2f}%) {status}")

print(f"\n🏆 最佳模型: {best_name}")
print(f"🎯 最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")

# 保存模型 (只有>90%才保存)
if best_accuracy >= 0.90:
    print(f"\n💾 保存模型 (准确率达到90%+)...")
    
    os.makedirs("w", exist_ok=True)
    
    # 保存最佳模型
    import joblib
    if best_name == 'WeightedEnsemble':
        # 保存加权集成的组件
        ensemble_data = {
            'models': model_list,
            'weights': weights,
            'model_names': [name for name, _ in best_models]
        }
        joblib.dump(ensemble_data, "w/final_dementia_audio_model.pkl")
    else:
        joblib.dump(models[best_name], "w/final_dementia_audio_model.pkl")
    
    # 保存特征工程组件
    joblib.dump(poly, "w/poly_features.pkl")
    joblib.dump(selector, "w/feature_selector.pkl")
    
    # 保存模型信息
    import json
    model_info = {
        'model_name': best_name,
        'accuracy': float(best_accuracy),
        'target_achieved': True,
        'feature_engineering': True,
        'ensemble_method': 'weighted' if best_name == 'WeightedEnsemble' else 'single'
    }
    
    with open("w/model_info.json", "w", encoding='utf-8') as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    # 复制预处理器
    import shutil
    shutil.copy(os.path.join(data_path, "scaler.pkl"), "w/scaler.pkl")
    shutil.copy(os.path.join(data_path, "label_encoder.pkl"), "w/label_encoder.pkl")
    
    print(f"✅ 模型已保存到 w/ 目录")
    print(f"🎉 成功达到目标! 准确率: {best_accuracy*100:.2f}%")
    
else:
    print(f"\n❌ 准确率 {best_accuracy*100:.2f}% 未达到90%要求")
    print("💡 建议尝试更多优化方法")

print(f"\n🏁 训练完成!")
