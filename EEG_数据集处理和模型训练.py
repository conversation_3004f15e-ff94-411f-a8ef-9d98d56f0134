"""
🧠 EEG数据集处理和模型训练完整流程
基于OpenNeuro阿尔茨海默病和额颞叶痴呆EEG数据集
版本: v1.0
作者: AI Assistant
"""

import os
import sys
import zipfile
import json
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 深度学习相关
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.utils import plot_model
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import mne

# 抑制TensorFlow警告
tf.get_logger().setLevel('ERROR')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

print("🧠 EEG痴呆症检测模型训练系统")
print("=" * 50)

class EEGDatasetProcessor:
    """EEG数据集处理器"""
    
    def __init__(self, base_path="/root/autodl-tmp"):
        self.base_path = base_path
        self.dataset_path = None
        self.subjects_info = []
        
    def extract_dataset(self, zip_filename="open-nuro-dataset.zip"):
        """解压数据集"""
        zip_path = os.path.join(self.base_path, zip_filename)
        
        print(f"🔍 查找数据集文件: {zip_path}")
        
        if not os.path.exists(zip_path):
            print(f"❌ 未找到数据集文件: {zip_path}")
            print("请确保已下载数据集到AutoDL环境")
            return False
        
        # 解压到指定目录
        extract_path = os.path.join(self.base_path, "eeg_dataset")
        
        print(f"📦 开始解压数据集到: {extract_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            
            self.dataset_path = extract_path
            print(f"✅ 数据集解压完成!")
            
            # 查看数据集结构
            self.explore_dataset_structure()
            return True
            
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            return False
    
    def explore_dataset_structure(self):
        """探索数据集结构"""
        print(f"\n🔍 探索数据集结构...")
        
        if not self.dataset_path:
            print("❌ 数据集路径未设置")
            return
        
        # 遍历数据集目录
        for root, dirs, files in os.walk(self.dataset_path):
            level = root.replace(self.dataset_path, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            
            # 只显示前几层目录结构
            if level < 3:
                subindent = ' ' * 2 * (level + 1)
                for file in files[:5]:  # 只显示前5个文件
                    print(f"{subindent}{file}")
                if len(files) > 5:
                    print(f"{subindent}... 还有 {len(files)-5} 个文件")
    
    def scan_subjects(self):
        """扫描所有被试者"""
        print(f"\n📊 扫描被试者信息...")
        
        subjects = []
        
        # 查找所有sub-开头的文件夹
        for item in os.listdir(self.dataset_path):
            if item.startswith('sub-') and os.path.isdir(os.path.join(self.dataset_path, item)):
                subject_path = os.path.join(self.dataset_path, item)
                
                # 查找EEG文件
                eeg_path = os.path.join(subject_path, 'eeg')
                if os.path.exists(eeg_path):
                    eeg_files = [f for f in os.listdir(eeg_path) if f.endswith('.set')]
                    
                    if eeg_files:
                        subject_info = {
                            'subject_id': item,
                            'subject_path': subject_path,
                            'eeg_files': eeg_files,
                            'label': self.determine_label(item)
                        }
                        subjects.append(subject_info)
        
        self.subjects_info = subjects
        
        # 统计信息
        labels = [s['label'] for s in subjects]
        label_counts = pd.Series(labels).value_counts()
        
        print(f"📈 被试者统计:")
        print(f"   总数: {len(subjects)}")
        print(f"   健康对照 (CN): {label_counts.get(0, 0)}")
        print(f"   阿尔茨海默病 (AD): {label_counts.get(1, 0)}")
        print(f"   额颞叶痴呆 (FTD): {label_counts.get(2, 0)}")
        
        return subjects
    
    def determine_label(self, subject_id):
        """确定被试者标签"""
        # 根据数据集文档，这里需要根据实际情况调整
        # 暂时使用简单的编号规则
        subject_num = int(subject_id.split('-')[1])
        
        # 假设前1/3是健康，中间1/3是AD，后1/3是FTD
        if subject_num <= 29:
            return 0  # 健康
        elif subject_num <= 65:
            return 1  # AD
        else:
            return 2  # FTD


class EEGFeatureExtractor:
    """EEG特征提取器"""
    
    def __init__(self, sfreq=128):
        self.sfreq = sfreq
        self.freq_bands = {
            'delta': (0.5, 4),
            'theta': (4, 8),
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 50)
        }
        self.channel_names = [
            'Fp1', 'Fp2', 'F3', 'F4', 'F7', 'F8', 'Fz',
            'C3', 'C4', 'Cz', 'P3', 'P4', 'Pz',
            'O1', 'O2', 'T3', 'T4', 'T5', 'T6'
        ]
    
    def load_eeg_file(self, file_path):
        """加载单个EEG文件"""
        try:
            # 使用MNE加载EEGLAB格式
            raw = mne.io.read_raw_eeglab(file_path, preload=True, verbose=False)
            
            # 获取数据
            data = raw.get_data()  # (n_channels, n_samples)
            sfreq = raw.info['sfreq']
            
            print(f"✅ 加载EEG文件: {os.path.basename(file_path)}")
            print(f"   通道数: {data.shape[0]}, 采样点: {data.shape[1]}, 采样率: {sfreq}Hz")
            
            return data, sfreq, raw.info
            
        except Exception as e:
            print(f"❌ 加载EEG文件失败: {e}")
            return None, None, None
    
    def extract_features(self, data):
        """提取EEG特征"""
        if data is None:
            return None
        
        features = []
        
        # 1. 频域特征
        freq_features = self._extract_frequency_features(data)
        features.extend(freq_features)
        
        # 2. 时域特征
        time_features = self._extract_time_features(data)
        features.extend(time_features)
        
        # 3. 连接性特征
        connectivity_features = self._extract_connectivity_features(data)
        features.extend(connectivity_features)
        
        return np.array(features)
    
    def _extract_frequency_features(self, data):
        """提取频域特征"""
        features = []
        
        for band_name, (low, high) in self.freq_bands.items():
            band_powers = []
            
            for ch in range(min(data.shape[0], 19)):  # 最多19个通道
                # FFT计算功率谱
                fft = np.fft.fft(data[ch, :])
                freqs = np.fft.fftfreq(len(fft), 1/self.sfreq)
                
                # 频段功率
                band_mask = (freqs >= low) & (freqs <= high)
                if np.any(band_mask):
                    band_power = np.mean(np.abs(fft[band_mask])**2)
                else:
                    band_power = 0
                
                band_powers.append(band_power)
            
            # 补齐到19个通道
            while len(band_powers) < 19:
                band_powers.append(0)
            
            features.extend(band_powers[:19])
            
            # 相对功率
            total_power = sum(band_powers) + 1e-8
            relative_powers = [p / total_power for p in band_powers[:19]]
            features.extend(relative_powers)
        
        return features
    
    def _extract_time_features(self, data):
        """提取时域特征"""
        features = []
        
        n_channels = min(data.shape[0], 19)
        
        for ch in range(n_channels):
            ch_data = data[ch, :]
            
            features.extend([
                np.mean(ch_data),           # 均值
                np.std(ch_data),            # 标准差
                np.var(ch_data),            # 方差
                np.max(ch_data),            # 最大值
                np.min(ch_data),            # 最小值
                np.median(ch_data),         # 中位数
                self._skewness(ch_data),    # 偏度
                self._kurtosis(ch_data),    # 峰度
            ])
        
        # 补齐到19个通道
        while len(features) < 19 * 8:
            features.extend([0] * 8)
        
        return features[:19 * 8]
    
    def _extract_connectivity_features(self, data):
        """提取连接性特征"""
        n_channels = min(data.shape[0], 19)
        
        # 计算相关性矩阵
        if n_channels > 1:
            correlation_matrix = np.corrcoef(data[:n_channels, :])
            
            # 提取上三角矩阵
            triu_indices = np.triu_indices(n_channels, k=1)
            connectivity_features = correlation_matrix[triu_indices]
        else:
            connectivity_features = [0]
        
        # 补齐到固定长度 (19*18/2 = 171)
        target_length = 171
        if len(connectivity_features) < target_length:
            connectivity_features = list(connectivity_features) + [0] * (target_length - len(connectivity_features))
        
        return connectivity_features[:target_length]
    
    def _skewness(self, x):
        """计算偏度"""
        if len(x) == 0:
            return 0
        mean_x = np.mean(x)
        std_x = np.std(x)
        if std_x == 0:
            return 0
        return np.mean(((x - mean_x) / std_x) ** 3)
    
    def _kurtosis(self, x):
        """计算峰度"""
        if len(x) == 0:
            return 0
        mean_x = np.mean(x)
        std_x = np.std(x)
        if std_x == 0:
            return 0
        return np.mean(((x - mean_x) / std_x) ** 4) - 3


class CompleteEEGClassifier:
    """完整的EEG分类器"""
    
    def __init__(self):
        self.feature_extractor = EEGFeatureExtractor()
        self.scaler = StandardScaler()
        self.model = None
        self.is_trained = False
        self.history = None
        
        # 类别信息
        self.n_classes = 3
        self.class_names = ['健康', '阿尔茨海默病', '额颞叶痴呆']
    
    def load_and_process_dataset(self, dataset_processor):
        """加载和处理数据集"""
        print(f"\n🔄 开始处理EEG数据集...")
        
        subjects = dataset_processor.subjects_info
        
        all_features = []
        all_labels = []
        
        for i, subject in enumerate(subjects):
            print(f"处理被试 {i+1}/{len(subjects)}: {subject['subject_id']}")
            
            # 加载EEG文件
            eeg_file = os.path.join(subject['subject_path'], 'eeg', subject['eeg_files'][0])
            data, sfreq, info = self.feature_extractor.load_eeg_file(eeg_file)
            
            if data is not None:
                # 提取特征
                features = self.feature_extractor.extract_features(data)
                
                if features is not None:
                    all_features.append(features)
                    all_labels.append(subject['label'])
        
        print(f"\n📊 数据处理完成:")
        print(f"   有效样本数: {len(all_features)}")
        print(f"   特征维度: {len(all_features[0]) if all_features else 0}")
        
        return np.array(all_features), np.array(all_labels)
    
    def split_dataset(self, X, y, test_size=0.2, val_size=0.2):
        """按8:2比例分割数据集"""
        print(f"\n📊 分割数据集 (训练:验证:测试 = 6:2:2)...")
        
        # 首先分出测试集 (20%)
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42, stratify=y
        )
        
        # 再从剩余80%中分出验证集 (实际占总数的20%)
        val_ratio = val_size / (1 - test_size)
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=val_ratio, random_state=42, stratify=y_temp
        )
        
        print(f"   训练集: {len(X_train)} 样本")
        print(f"   验证集: {len(X_val)} 样本")
        print(f"   测试集: {len(X_test)} 样本")
        
        # 显示类别分布
        for split_name, labels in [("训练集", y_train), ("验证集", y_val), ("测试集", y_test)]:
            counts = np.bincount(labels)
            print(f"   {split_name}类别分布: {dict(zip(self.class_names, counts))}")
        
        return X_train, X_val, X_test, y_train, y_val, y_test

    def build_optimal_model(self, input_dim):
        """构建最优EEG分类模型"""
        print(f"\n🏗️ 构建最优EEG分类模型...")
        print(f"   输入维度: {input_dim}")

        # 使用函数式API构建更复杂的模型
        inputs = layers.Input(shape=(input_dim,), name='eeg_features')

        # 第一个分支 - 深度特征提取
        branch1 = layers.Dense(512, activation='relu', name='dense1_1')(inputs)
        branch1 = layers.BatchNormalization(name='bn1_1')(branch1)
        branch1 = layers.Dropout(0.5, name='dropout1_1')(branch1)

        branch1 = layers.Dense(256, activation='relu', name='dense1_2')(branch1)
        branch1 = layers.BatchNormalization(name='bn1_2')(branch1)
        branch1 = layers.Dropout(0.4, name='dropout1_2')(branch1)

        # 第二个分支 - 浅层特征提取
        branch2 = layers.Dense(256, activation='relu', name='dense2_1')(inputs)
        branch2 = layers.BatchNormalization(name='bn2_1')(branch2)
        branch2 = layers.Dropout(0.3, name='dropout2_1')(branch2)

        # 特征融合
        merged = layers.Concatenate(name='feature_fusion')([branch1, branch2])

        # 融合后的处理
        x = layers.Dense(128, activation='relu', name='fusion_dense1')(merged)
        x = layers.BatchNormalization(name='fusion_bn1')(x)
        x = layers.Dropout(0.3, name='fusion_dropout1')(x)

        x = layers.Dense(64, activation='relu', name='fusion_dense2')(x)
        x = layers.BatchNormalization(name='fusion_bn2')(x)
        x = layers.Dropout(0.2, name='fusion_dropout2')(x)

        # 输出层
        outputs = layers.Dense(self.n_classes, activation='softmax', name='predictions')(x)

        # 创建模型
        model = models.Model(inputs=inputs, outputs=outputs, name='EEG_Dementia_Classifier')

        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )

        self.model = model

        # 显示模型结构
        print(f"\n📋 模型结构:")
        model.summary()

        return model

    def visualize_model(self, save_path="model_architecture.png"):
        """可视化模型结构"""
        if self.model is None:
            print("❌ 模型未构建")
            return

        print(f"\n🎨 生成模型结构图...")

        try:
            plot_model(
                self.model,
                to_file=save_path,
                show_shapes=True,
                show_layer_names=True,
                rankdir='TB',
                expand_nested=False,
                dpi=150
            )
            print(f"✅ 模型结构图已保存: {save_path}")
        except Exception as e:
            print(f"⚠️ 模型结构图生成失败: {e}")

    def train_model(self, X_train, y_train, X_val, y_val, epochs=150):
        """训练模型"""
        print(f"\n🚀 开始训练EEG分类模型...")
        print(f"   训练轮数: {epochs}")
        print(f"   训练样本: {len(X_train)}")
        print(f"   验证样本: {len(X_val)}")

        # 数据标准化
        print(f"🔄 标准化特征...")
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)

        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=25,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=15,
                min_lr=1e-7,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                'best_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]

        # 开始训练
        print(f"🔥 开始训练...")
        start_time = datetime.now()

        self.history = self.model.fit(
            X_train_scaled, y_train,
            validation_data=(X_val_scaled, y_val),
            epochs=epochs,
            batch_size=16,
            callbacks=callbacks_list,
            verbose=1
        )

        end_time = datetime.now()
        training_time = end_time - start_time

        print(f"✅ 训练完成!")
        print(f"   训练时间: {training_time}")

        self.is_trained = True
        return self.history

    def visualize_training(self, save_path="training_history.png"):
        """可视化训练过程"""
        if self.history is None:
            print("❌ 没有训练历史")
            return

        print(f"\n📈 生成训练过程可视化...")

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('EEG模型训练过程', fontsize=16, fontweight='bold')

        # 损失函数
        axes[0, 0].plot(self.history.history['loss'], label='训练损失', color='blue')
        axes[0, 0].plot(self.history.history['val_loss'], label='验证损失', color='red')
        axes[0, 0].set_title('模型损失')
        axes[0, 0].set_xlabel('轮次')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # 准确率
        axes[0, 1].plot(self.history.history['accuracy'], label='训练准确率', color='blue')
        axes[0, 1].plot(self.history.history['val_accuracy'], label='验证准确率', color='red')
        axes[0, 1].set_title('模型准确率')
        axes[0, 1].set_xlabel('轮次')
        axes[0, 1].set_ylabel('准确率')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # 精确率
        if 'precision' in self.history.history:
            axes[1, 0].plot(self.history.history['precision'], label='训练精确率', color='blue')
            axes[1, 0].plot(self.history.history['val_precision'], label='验证精确率', color='red')
            axes[1, 0].set_title('模型精确率')
            axes[1, 0].set_xlabel('轮次')
            axes[1, 0].set_ylabel('精确率')
            axes[1, 0].legend()
            axes[1, 0].grid(True)

        # 召回率
        if 'recall' in self.history.history:
            axes[1, 1].plot(self.history.history['recall'], label='训练召回率', color='blue')
            axes[1, 1].plot(self.history.history['val_recall'], label='验证召回率', color='red')
            axes[1, 1].set_title('模型召回率')
            axes[1, 1].set_xlabel('轮次')
            axes[1, 1].set_ylabel('召回率')
            axes[1, 1].legend()
            axes[1, 1].grid(True)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 训练过程图已保存: {save_path}")

    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        if not self.is_trained:
            print("❌ 模型未训练")
            return

        print(f"\n📊 评估模型性能...")

        # 标准化测试数据
        X_test_scaled = self.scaler.transform(X_test)

        # 预测
        y_pred_proba = self.model.predict(X_test_scaled, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)

        # 计算指标
        accuracy = accuracy_score(y_test, y_pred)

        print(f"\n🎯 测试集结果:")
        print(f"   准确率: {accuracy:.4f}")

        # 详细分类报告
        print(f"\n📋 详细分类报告:")
        print(classification_report(y_test, y_pred, target_names=self.class_names))

        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)

        # 可视化混淆矩阵
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

        return accuracy, y_pred, y_pred_proba

    def save_complete_model(self, filepath="EEG_complete_classifier.pkl"):
        """保存完整模型"""
        if not self.is_trained:
            print("❌ 模型未训练，无法保存")
            return False

        print(f"\n💾 保存完整EEG分类器...")

        try:
            with open(filepath, 'wb') as f:
                pickle.dump(self, f)

            print(f"✅ 模型已保存: {filepath}")

            # 显示文件大小
            file_size = os.path.getsize(filepath) / (1024 * 1024)
            print(f"   文件大小: {file_size:.2f} MB")

            return True

        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
            return False

    @classmethod
    def load_complete_model(cls, filepath="EEG_complete_classifier.pkl"):
        """加载完整模型"""
        print(f"📂 加载EEG分类器: {filepath}")

        try:
            with open(filepath, 'rb') as f:
                model = pickle.load(f)

            print(f"✅ 模型加载成功")
            return model

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return None

    def predict_single(self, eeg_data):
        """预测单个EEG样本"""
        if not self.is_trained:
            print("❌ 模型未训练")
            return None

        # 提取特征
        features = self.feature_extractor.extract_features(eeg_data)
        if features is None:
            return None

        # 标准化
        features_scaled = self.scaler.transform([features])

        # 预测
        pred_proba = self.model.predict(features_scaled, verbose=0)[0]
        pred_class = np.argmax(pred_proba)

        result = {
            'predicted_class': pred_class,
            'class_name': self.class_names[pred_class],
            'confidence': pred_proba[pred_class],
            'probabilities': {
                self.class_names[i]: prob for i, prob in enumerate(pred_proba)
            }
        }

        return result


def main():
    """主函数 - 完整的EEG数据集处理和模型训练流程"""

    print("🧠 EEG痴呆症检测模型训练系统启动")
    print("=" * 60)

    # 1. 数据集处理
    print("\n📦 第一步: 数据集处理")
    processor = EEGDatasetProcessor()

    # 解压数据集
    if not processor.extract_dataset():
        print("❌ 数据集解压失败，程序退出")
        return

    # 扫描被试者
    subjects = processor.scan_subjects()
    if len(subjects) == 0:
        print("❌ 未找到有效的被试者数据")
        return

    # 2. 特征提取和数据准备
    print("\n🔧 第二步: 特征提取和数据准备")
    classifier = CompleteEEGClassifier()

    # 加载和处理数据集
    X, y = classifier.load_and_process_dataset(processor)

    if len(X) == 0:
        print("❌ 特征提取失败，程序退出")
        return

    # 分割数据集
    X_train, X_val, X_test, y_train, y_val, y_test = classifier.split_dataset(X, y)

    # 3. 模型构建
    print("\n🏗️ 第三步: 模型构建")
    model = classifier.build_optimal_model(X.shape[1])

    # 可视化模型结构
    classifier.visualize_model()

    # 4. 模型训练
    print("\n🚀 第四步: 模型训练")
    history = classifier.train_model(X_train, y_train, X_val, y_val, epochs=150)

    # 可视化训练过程
    classifier.visualize_training()

    # 5. 模型评估
    print("\n📊 第五步: 模型评估")
    accuracy, y_pred, y_pred_proba = classifier.evaluate_model(X_test, y_test)

    # 6. 保存模型
    print("\n💾 第六步: 保存模型")
    success = classifier.save_complete_model()

    if success:
        print("\n🎉 EEG模型训练完成!")
        print("=" * 60)
        print(f"📊 最终结果:")
        print(f"   测试准确率: {accuracy:.4f}")
        print(f"   模型文件: EEG_complete_classifier.pkl")
        print(f"   结构图: model_architecture.png")
        print(f"   训练图: training_history.png")
        print(f"   混淆矩阵: confusion_matrix.png")
        print("=" * 60)

        # 测试加载模型
        print("\n🔍 测试模型加载...")
        loaded_model = CompleteEEGClassifier.load_complete_model()
        if loaded_model:
            print("✅ 模型加载测试成功!")

    else:
        print("❌ 模型保存失败")


if __name__ == "__main__":
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 运行主程序
    main()
