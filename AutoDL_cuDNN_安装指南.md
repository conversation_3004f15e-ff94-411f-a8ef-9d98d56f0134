# 🔧 AutoDL cuDNN安装详细指南

## 📋 准备工作

您已经有了cuDNN压缩包：`cudnn-linux-x86_64-9.10.2.21_cuda11-archive.tar.xz`

## 🚀 在AutoDL Jupyter中的操作步骤

### 第1步：检查当前环境

在Jupyter新建一个cell，运行：

```python
# 检查CUDA版本
!nvcc --version

# 检查GPU
!nvidia-smi

# 检查TensorFlow
import tensorflow as tf
print(f"TensorFlow版本: {tf.__version__}")
print(f"GPU数量: {len(tf.config.list_physical_devices('GPU'))}")

# 测试当前cuDNN
try:
    x = tf.random.normal((1, 10, 10, 1))
    conv = tf.keras.layers.Conv2D(1, 3)
    y = conv(x)
    print("✅ 当前cuDNN可用")
except Exception as e:
    print(f"❌ 当前cuDNN不可用: {e}")
```

### 第2步：解压cuDNN

```python
import tarfile
import os

# 检查文件是否存在
cudnn_file = "cudnn-linux-x86_64-9.10.2.21_cuda11-archive.tar.xz"
if os.path.exists(cudnn_file):
    print(f"✅ 找到cuDNN文件: {cudnn_file}")
    
    # 解压文件
    print("📦 正在解压...")
    with tarfile.open(cudnn_file, 'r:xz') as tar:
        tar.extractall('.')
    
    # 查看解压结果
    extracted_dirs = [d for d in os.listdir('.') if d.startswith('cudnn-linux')]
    print(f"✅ 解压完成: {extracted_dirs}")
else:
    print(f"❌ 文件不存在: {cudnn_file}")
    print("请确保文件在当前目录中")
```

### 第3步：安装cuDNN

```python
import subprocess
import shutil

# 找到解压的cuDNN目录
cudnn_dirs = [d for d in os.listdir('.') if d.startswith('cudnn-linux')]
if not cudnn_dirs:
    print("❌ 未找到cuDNN目录")
else:
    cudnn_dir = cudnn_dirs[0]
    print(f"📁 cuDNN目录: {cudnn_dir}")
    
    # 查找CUDA安装路径
    cuda_paths = ["/usr/local/cuda", "/usr/local/cuda-11", "/usr/local/cuda-11.2", "/usr/local/cuda-11.3"]
    cuda_path = None
    
    for path in cuda_paths:
        if os.path.exists(path):
            cuda_path = path
            break
    
    if cuda_path:
        print(f"📍 CUDA路径: {cuda_path}")
        
        # 复制头文件
        print("📋 复制头文件...")
        include_src = os.path.join(cudnn_dir, "include", "*")
        include_dst = os.path.join(cuda_path, "include")
        !sudo cp -r {include_src} {include_dst}
        
        # 复制库文件
        print("📋 复制库文件...")
        lib_src = os.path.join(cudnn_dir, "lib", "*")
        lib_dst = os.path.join(cuda_path, "lib64")
        !sudo cp -r {lib_src} {lib_dst}
        
        # 设置权限
        print("🔐 设置权限...")
        !sudo chmod a+r {cuda_path}/include/cudnn*
        !sudo chmod a+r {cuda_path}/lib64/libcudnn*
        
        # 更新链接库
        print("🔄 更新链接库...")
        !sudo ldconfig
        
        print("✅ cuDNN安装完成!")
    else:
        print("❌ 未找到CUDA安装路径")
```

### 第4步：重启Python内核

**重要：** 在Jupyter中，点击菜单栏 `Kernel` -> `Restart Kernel`

### 第5步：验证安装

重启内核后，在新的cell中运行：

```python
import tensorflow as tf
import numpy as np

print("🧪 验证cuDNN安装")
print("=" * 40)

# 检查GPU
gpus = tf.config.list_physical_devices('GPU')
print(f"📊 GPU数量: {len(gpus)}")

if gpus:
    # 设置GPU内存增长
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)
    
    try:
        # 创建测试模型
        print("🏗️ 创建测试模型...")
        model = tf.keras.Sequential([
            tf.keras.layers.Conv2D(32, (1, 7), padding='same', activation='relu', 
                                 input_shape=(19, 128, 1)),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.Conv2D(32, (19, 1), activation='relu'),
            tf.keras.layers.GlobalAveragePooling2D(),
            tf.keras.layers.Dense(3, activation='softmax')
        ])
        
        # 编译模型
        model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
        
        # 创建测试数据
        print("📊 创建测试数据...")
        X = np.random.randn(10, 19, 128, 1).astype(np.float32)
        y = tf.keras.utils.to_categorical(np.random.randint(0, 3, 10), 3)
        
        # 测试训练
        print("🏋️ 测试训练...")
        history = model.fit(X, y, epochs=2, verbose=1)
        
        # 测试预测
        print("🔮 测试预测...")
        predictions = model.predict(X, verbose=0)
        
        print("🎉 cuDNN验证成功!")
        print(f"📊 模型参数: {model.count_params():,}")
        
    except Exception as e:
        print(f"❌ cuDNN验证失败: {e}")
        print("💡 建议使用CPU训练器")
else:
    print("❌ 未检测到GPU")
```

### 第6步：清理临时文件

```python
import shutil

# 删除解压的临时目录
cudnn_dirs = [d for d in os.listdir('.') if d.startswith('cudnn-linux')]
for dir_name in cudnn_dirs:
    if os.path.isdir(dir_name):
        shutil.rmtree(dir_name)
        print(f"🗑️ 删除临时目录: {dir_name}")

print("✅ 清理完成")
```

## 🎯 验证成功后的下一步

如果验证成功，您可以：

1. **运行GPU训练器**：
```python
# 在新的cell中运行
exec(open('complete_deep_eeg_trainer.py').read())
```

2. **或者直接运行**：
```bash
!python complete_deep_eeg_trainer.py
```

## ⚠️ 如果安装失败

如果cuDNN安装失败，请使用CPU训练器：

```python
# 运行CPU训练器
exec(open('cpu_only_eeg_trainer.py').read())
```

## 🔧 故障排除

### 常见问题：

1. **权限不足**：确保使用 `sudo` 命令
2. **路径错误**：检查CUDA安装路径
3. **版本不兼容**：cuDNN 9.x 可能需要更新的TensorFlow

### 备用方案：

```python
# 如果手动安装失败，尝试conda安装
!conda install cudnn=8.2.1 -y

# 或pip安装
!pip install nvidia-cudnn-cu11==*********
```

---

**按照这个指南逐步操作，应该能成功安装cuDNN并解决GPU训练问题！** 🚀
