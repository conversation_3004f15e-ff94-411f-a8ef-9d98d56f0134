"""
简化音频模型训练器 - 测试版本
"""

import pandas as pd
import numpy as np
import os
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

# 抑制TensorFlow警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    from sklearn.metrics import classification_report, accuracy_score
    print("✅ 成功导入所有依赖库")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def load_data():
    """加载数据"""
    print("📊 加载训练数据...")
    
    # 查找数据路径
    data_path = r"D:\模型开发\audio\processed_datasets"
    
    try:
        # 加载数据
        train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
        val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
        test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))
        
        print(f"   训练集: {len(train_data)} 样本")
        print(f"   验证集: {len(val_data)} 样本")
        print(f"   测试集: {len(test_data)} 样本")
        
        # 加载配置
        with open(os.path.join(data_path, "feature_info.json"), 'r', encoding='utf-8') as f:
            feature_info = json.load(f)
        
        with open(os.path.join(data_path, "label_encoder.pkl"), 'rb') as f:
            label_encoder = pickle.load(f)
        
        return train_data, val_data, test_data, feature_info, label_encoder
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None, None, None, None

def prepare_data(train_data, val_data, test_data, feature_info):
    """准备训练数据"""
    print("🔧 准备训练数据...")
    
    # 获取特征列
    feature_cols = []
    for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
        if category in feature_info:
            feature_cols.extend(feature_info[category])
    
    # 分离特征和标签
    X_train = train_data[feature_cols].values
    y_train = train_data['diagnosis_encoded'].values
    
    X_val = val_data[feature_cols].values
    y_val = val_data['diagnosis_encoded'].values
    
    X_test = test_data[feature_cols].values
    y_test = test_data['diagnosis_encoded'].values
    
    # 转换为分类标签
    y_train_cat = tf.keras.utils.to_categorical(y_train, num_classes=3)
    y_val_cat = tf.keras.utils.to_categorical(y_val, num_classes=3)
    y_test_cat = tf.keras.utils.to_categorical(y_test, num_classes=3)
    
    print(f"   特征矩阵形状: {X_train.shape}")
    print(f"   标签矩阵形状: {y_train_cat.shape}")
    
    return X_train, X_val, X_test, y_train_cat, y_val_cat, y_test_cat, y_test

def build_simple_model(input_dim):
    """构建简单模型"""
    print("🏗️ 构建简单模型...")
    
    model = Sequential([
        Dense(128, activation='relu', input_shape=(input_dim,)),
        BatchNormalization(),
        Dropout(0.5),
        
        Dense(64, activation='relu'),
        BatchNormalization(),
        Dropout(0.4),
        
        Dense(32, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(3, activation='softmax')
    ])
    
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print(f"   模型参数: {model.count_params():,}")
    return model

def train_model(model, X_train, y_train_cat, X_val, y_val_cat):
    """训练模型"""
    print("🚀 开始训练...")
    
    # 创建输出目录
    os.makedirs("trained_audio_models", exist_ok=True)
    
    # 设置回调
    callbacks = [
        EarlyStopping(
            monitor='val_accuracy',
            patience=10,
            restore_best_weights=True,
            verbose=1
        )
    ]
    
    # 训练
    history = model.fit(
        X_train, y_train_cat,
        validation_data=(X_val, y_val_cat),
        epochs=50,
        batch_size=32,
        callbacks=callbacks,
        verbose=1
    )
    
    return history

def evaluate_model(model, X_test, y_test_cat, y_test, label_encoder):
    """评估模型"""
    print("📊 评估模型...")
    
    # 预测
    y_pred_proba = model.predict(X_test)
    y_pred = np.argmax(y_pred_proba, axis=1)
    
    # 计算准确率
    accuracy = accuracy_score(y_test, y_pred)
    print(f"   测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # 分类报告
    class_names = label_encoder.classes_
    report = classification_report(y_test, y_pred, target_names=class_names)
    print("\n📋 分类报告:")
    print(report)
    
    return accuracy, report

def main():
    """主函数"""
    print("🎵 简化音频模型训练器")
    print("=" * 50)
    
    # 1. 加载数据
    train_data, val_data, test_data, feature_info, label_encoder = load_data()
    if train_data is None:
        return
    
    # 2. 准备数据
    X_train, X_val, X_test, y_train_cat, y_val_cat, y_test_cat, y_test = prepare_data(
        train_data, val_data, test_data, feature_info
    )
    
    # 3. 构建模型
    model = build_simple_model(X_train.shape[1])
    
    # 4. 训练模型
    history = train_model(model, X_train, y_train_cat, X_val, y_val_cat)
    
    # 5. 评估模型
    accuracy, report = evaluate_model(model, X_test, y_test_cat, y_test, label_encoder)
    
    # 6. 保存模型
    model.save("trained_audio_models/simple_dementia_model.h5")
    
    print("\n" + "=" * 50)
    print("✅ 训练完成!")
    print(f"🎯 最终准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print("📁 模型已保存: trained_audio_models/simple_dementia_model.h5")

if __name__ == "__main__":
    main()
