# -*- coding: utf-8 -*-
"""
测试症状分析结果显示
"""

def test_symptoms_display():
    """测试症状分析结果显示逻辑"""
    print("🧪 测试症状分析结果显示")
    print("=" * 40)
    
    # 模拟症状分析结果
    class_labels = [
        'MildDemented(轻度痴呆)',
        'ModerateDemented(中度痴呆)',
        'NonDemented(无痴呆)',
        'VeryMildDemented(非常轻度痴呆)'
    ]
    
    # 模拟分析结果
    test_result = {
        'predicted_class': 2,
        'predicted_class_name': 'NonDemented(无痴呆)',
        'confidence': 0.85,
        'probabilities': [0.05, 0.08, 0.85, 0.02],
        'image_path': 'test_image.jpg',
        'timestamp': '2024-06-21 15:30:00',
        'ct_detection': {
            'is_ct': True,
            'confidence': 0.92,
            'class_name': 'CT图像',
            'probabilities': [0.92, 0.08]
        }
    }
    
    print("📊 模拟分析结果:")
    print(f"预测类别: {test_result['predicted_class_name']}")
    print(f"置信度: {test_result['confidence']:.2%}")
    
    print(f"\n🔍 CT检测结果:")
    ct_result = test_result['ct_detection']
    print(f"检测结果: {ct_result['class_name']}")
    print(f"是否CT图像: {ct_result['is_ct']}")
    print(f"检测置信度: {ct_result['confidence']:.2%}")
    
    print(f"\n📈 详细概率分布:")
    for i, prob in enumerate(test_result['probabilities']):
        label_text = class_labels[i].split('(')[0]
        print(f"  {label_text}: {prob:.4f} ({prob:.2%})")
    
    print(f"\n✅ 症状分析结果显示测试完成")
    print("如果在应用中看不到症状分析详情，可能是界面布局问题")

if __name__ == "__main__":
    test_symptoms_display()
