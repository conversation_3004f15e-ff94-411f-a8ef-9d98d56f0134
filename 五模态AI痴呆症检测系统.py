"""
🧠 五模态AI痴呆症检测系统
=======================

集成五种数据模态的综合痴呆症诊断系统:
1. MRI症状分析模型
2. CT图像检测模型  
3. EEG脑电分析模型
4. MMSE认知评估模型
5. 血液标志物分析模型

作者: AI医学团队
版本: v3.0 五模态版
更新日期: 2024-06-23
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras.models import load_model
import joblib
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns
from datetime import datetime
import json

# 设置主题
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class FiveModalDementiaDetector:
    """五模态痴呆症检测系统"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🧠 五模态AI痴呆症检测系统 v3.0")
        self.root.geometry("1400x900")
        self.root.resizable(True, True)
        
        # 模型路径配置
        self.model_paths = {
            'mri_model': "D:/模型开发/MRI_class.h5",
            'ct_model': "D:/模型开发/picture_class.h5", 
            'eeg_model': "EEG_complete_classifier.pkl",
            'mmse_model': "MMSE_classifier.pkl",
            'blood_model': "Blood_biomarker_classifier.pkl"
        }
        
        # 模型实例
        self.models = {
            'mri': None,
            'ct': None,
            'eeg': None,
            'mmse': None,
            'blood': None
        }
        
        # 数据存储
        self.current_data = {
            'image_path': None,
            'eeg_data': None,
            'mmse_scores': {},
            'blood_biomarkers': {},
            'results': {}
        }
        
        # 类别标签
        self.class_labels = {
            'dementia': ['轻度痴呆', '中度痴呆', '无痴呆', '非常轻度痴呆'],
            'image_type': ['CT图像', '非CT图像'],
            'eeg': ['健康', '阿尔茨海默病', '额颞叶痴呆'],
            'cognitive': ['正常', '轻度认知障碍', '痴呆'],
            'blood_risk': ['低风险', '中风险', '高风险']
        }
        
        # 创建界面
        self.create_interface()
        self.load_models()
    
    def create_interface(self):
        """创建用户界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 标题
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🧠 五模态AI痴呆症检测系统",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)
        
        # 创建选项卡
        self.notebook = ctk.CTkTabview(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 添加选项卡
        self.create_image_analysis_tab()
        self.create_eeg_analysis_tab()
        self.create_mmse_assessment_tab()
        self.create_blood_biomarker_tab()
        self.create_comprehensive_analysis_tab()
        self.create_results_tab()
    
    def create_image_analysis_tab(self):
        """创建影像分析选项卡"""
        tab = self.notebook.add("🖼️ 影像分析")
        
        # 图像上传区域
        upload_frame = ctk.CTkFrame(tab)
        upload_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(upload_frame, text="📁 影像数据上传", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
        
        upload_btn = ctk.CTkButton(
            upload_frame,
            text="🔍 选择MRI/CT图像",
            command=self.upload_image,
            height=40
        )
        upload_btn.pack(pady=10)
        
        # 图像显示区域
        self.image_display_frame = ctk.CTkFrame(tab)
        self.image_display_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 分析按钮
        analyze_btn = ctk.CTkButton(
            tab,
            text="🔬 开始影像分析",
            command=self.analyze_image,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        analyze_btn.pack(pady=10)
        
        # 结果显示
        self.image_results_frame = ctk.CTkFrame(tab)
        self.image_results_frame.pack(fill="x", padx=10, pady=10)
    
    def create_eeg_analysis_tab(self):
        """创建EEG分析选项卡"""
        tab = self.notebook.add("🧠 EEG分析")
        
        # EEG数据输入
        input_frame = ctk.CTkFrame(tab)
        input_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(input_frame, text="📊 EEG数据输入", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
        
        # EEG文件上传
        eeg_upload_btn = ctk.CTkButton(
            input_frame,
            text="📁 上传EEG数据文件",
            command=self.upload_eeg_data,
            height=40
        )
        eeg_upload_btn.pack(pady=10)
        
        # EEG参数显示
        self.eeg_params_frame = ctk.CTkFrame(tab)
        self.eeg_params_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 分析按钮
        eeg_analyze_btn = ctk.CTkButton(
            tab,
            text="🔬 开始EEG分析",
            command=self.analyze_eeg,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        eeg_analyze_btn.pack(pady=10)
    
    def create_mmse_assessment_tab(self):
        """创建MMSE评估选项卡"""
        tab = self.notebook.add("📝 MMSE评估")
        
        # MMSE评分输入
        input_frame = ctk.CTkFrame(tab)
        input_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        ctk.CTkLabel(input_frame, text="📋 MMSE认知评估", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        
        # 创建MMSE评分输入界面
        self.create_mmse_inputs(input_frame)
        
        # 计算按钮
        calculate_btn = ctk.CTkButton(
            tab,
            text="🧮 计算MMSE评分",
            command=self.calculate_mmse,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        calculate_btn.pack(pady=10)
        
        # 结果显示
        self.mmse_results_frame = ctk.CTkFrame(tab)
        self.mmse_results_frame.pack(fill="x", padx=10, pady=10)
    
    def create_mmse_inputs(self, parent):
        """创建MMSE输入控件"""
        # MMSE各项评分
        mmse_items = {
            'orientation_time': ('时间定向力', 5),
            'orientation_place': ('地点定向力', 5),
            'registration': ('即时记忆', 3),
            'attention': ('注意力和计算', 5),
            'recall': ('延迟回忆', 3),
            'language': ('语言能力', 8),
            'visuospatial': ('视空间能力', 1)
        }
        
        self.mmse_vars = {}
        
        # 创建输入框
        for key, (label, max_score) in mmse_items.items():
            frame = ctk.CTkFrame(parent)
            frame.pack(fill="x", padx=10, pady=5)
            
            ctk.CTkLabel(frame, text=f"{label} (0-{max_score}分):", 
                        width=150).pack(side="left", padx=10)
            
            var = tk.StringVar()
            entry = ctk.CTkEntry(frame, textvariable=var, width=100)
            entry.pack(side="left", padx=10)
            
            self.mmse_vars[key] = var
    
    def create_blood_biomarker_tab(self):
        """创建血液标志物选项卡"""
        tab = self.notebook.add("🩸 血液标志物")
        
        # 血液标志物输入
        input_frame = ctk.CTkFrame(tab)
        input_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        ctk.CTkLabel(input_frame, text="🧪 血液生物标志物检测", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        
        # 创建血液标志物输入界面
        self.create_blood_inputs(input_frame)
        
        # 分析按钮
        blood_analyze_btn = ctk.CTkButton(
            tab,
            text="🔬 分析血液标志物",
            command=self.analyze_blood_biomarkers,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        blood_analyze_btn.pack(pady=10)
        
        # 结果显示
        self.blood_results_frame = ctk.CTkFrame(tab)
        self.blood_results_frame.pack(fill="x", padx=10, pady=10)
    
    def create_blood_inputs(self, parent):
        """创建血液标志物输入控件"""
        # 血液标志物项目
        biomarkers = {
            'amyloid_beta_42': ('Aβ42 (pg/mL)', '正常: >1000'),
            'amyloid_beta_40': ('Aβ40 (pg/mL)', '正常: 10000-20000'),
            'p_tau_181': ('p-Tau181 (pg/mL)', '正常: <20'),
            'total_tau': ('总Tau (pg/mL)', '正常: <300'),
            'neurofilament_light': ('NfL (pg/mL)', '正常: <40'),
            'gfap': ('GFAP (pg/mL)', '正常: <150'),
            'apoe_genotype': ('APOE基因型', 'E2/E3/E4')
        }
        
        self.blood_vars = {}
        
        # 创建两列布局
        left_frame = ctk.CTkFrame(parent)
        left_frame.pack(side="left", fill="both", expand=True, padx=5)
        
        right_frame = ctk.CTkFrame(parent)
        right_frame.pack(side="right", fill="both", expand=True, padx=5)
        
        items = list(biomarkers.items())
        mid_point = len(items) // 2
        
        # 左列
        for key, (label, reference) in items[:mid_point]:
            frame = ctk.CTkFrame(left_frame)
            frame.pack(fill="x", padx=10, pady=5)
            
            ctk.CTkLabel(frame, text=f"{label}:", width=120).pack(anchor="w", padx=5)
            ctk.CTkLabel(frame, text=reference, font=ctk.CTkFont(size=10), 
                        text_color="gray").pack(anchor="w", padx=5)
            
            var = tk.StringVar()
            entry = ctk.CTkEntry(frame, textvariable=var, width=150)
            entry.pack(anchor="w", padx=5, pady=2)
            
            self.blood_vars[key] = var
        
        # 右列
        for key, (label, reference) in items[mid_point:]:
            frame = ctk.CTkFrame(right_frame)
            frame.pack(fill="x", padx=10, pady=5)
            
            ctk.CTkLabel(frame, text=f"{label}:", width=120).pack(anchor="w", padx=5)
            ctk.CTkLabel(frame, text=reference, font=ctk.CTkFont(size=10), 
                        text_color="gray").pack(anchor="w", padx=5)
            
            if key == 'apoe_genotype':
                var = tk.StringVar()
                combo = ctk.CTkComboBox(frame, variable=var, 
                                       values=['E2/E2', 'E2/E3', 'E2/E4', 'E3/E3', 'E3/E4', 'E4/E4'],
                                       width=150)
                combo.pack(anchor="w", padx=5, pady=2)
            else:
                var = tk.StringVar()
                entry = ctk.CTkEntry(frame, textvariable=var, width=150)
                entry.pack(anchor="w", padx=5, pady=2)
            
            self.blood_vars[key] = var
    
    def create_comprehensive_analysis_tab(self):
        """创建综合分析选项卡"""
        tab = self.notebook.add("🔬 综合分析")
        
        # 综合分析控制面板
        control_frame = ctk.CTkFrame(tab)
        control_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(control_frame, text="🎯 五模态综合分析", 
                    font=ctk.CTkFont(size=18, weight="bold")).pack(pady=10)
        
        # 模态选择
        modality_frame = ctk.CTkFrame(control_frame)
        modality_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(modality_frame, text="选择分析模态:", 
                    font=ctk.CTkFont(size=14)).pack(anchor="w", padx=10)
        
        self.modality_vars = {}
        modalities = ['MRI影像', 'EEG脑电', 'MMSE认知', '血液标志物']
        
        for modality in modalities:
            var = tk.BooleanVar(value=True)
            checkbox = ctk.CTkCheckBox(modality_frame, text=modality, variable=var)
            checkbox.pack(anchor="w", padx=20, pady=2)
            self.modality_vars[modality] = var
        
        # 融合策略选择
        strategy_frame = ctk.CTkFrame(control_frame)
        strategy_frame.pack(fill="x", padx=10, pady=10)
        
        ctk.CTkLabel(strategy_frame, text="融合策略:", 
                    font=ctk.CTkFont(size=14)).pack(anchor="w", padx=10)
        
        self.fusion_strategy = tk.StringVar(value="加权平均")
        strategies = ["加权平均", "投票机制", "深度融合", "贝叶斯融合"]
        
        strategy_combo = ctk.CTkComboBox(strategy_frame, variable=self.fusion_strategy,
                                        values=strategies, width=200)
        strategy_combo.pack(anchor="w", padx=20, pady=5)
        
        # 综合分析按钮
        comprehensive_btn = ctk.CTkButton(
            tab,
            text="🚀 开始五模态综合分析",
            command=self.comprehensive_analysis,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        comprehensive_btn.pack(pady=20)
        
        # 综合结果显示
        self.comprehensive_results_frame = ctk.CTkFrame(tab)
        self.comprehensive_results_frame.pack(fill="both", expand=True, padx=10, pady=10)
    
    def create_results_tab(self):
        """创建结果展示选项卡"""
        tab = self.notebook.add("📊 结果报告")
        
        # 结果展示区域
        self.results_display_frame = ctk.CTkFrame(tab)
        self.results_display_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 导出按钮
        export_frame = ctk.CTkFrame(tab)
        export_frame.pack(fill="x", padx=10, pady=10)
        
        export_pdf_btn = ctk.CTkButton(
            export_frame,
            text="📄 导出PDF报告",
            command=self.export_pdf_report,
            height=40
        )
        export_pdf_btn.pack(side="left", padx=10, pady=10)
        
        export_html_btn = ctk.CTkButton(
            export_frame,
            text="🌐 导出HTML报告",
            command=self.export_html_report,
            height=40
        )
        export_html_btn.pack(side="left", padx=10, pady=10)

    def load_models(self):
        """加载所有模型"""
        print("🔄 正在加载五模态AI模型...")

        try:
            # 加载MRI模型
            if os.path.exists(self.model_paths['mri_model']):
                self.models['mri'] = load_model(self.model_paths['mri_model'])
                print("✅ MRI模型加载成功")

            # 加载CT模型
            if os.path.exists(self.model_paths['ct_model']):
                self.models['ct'] = load_model(self.model_paths['ct_model'])
                print("✅ CT模型加载成功")

            # 加载EEG模型
            if os.path.exists(self.model_paths['eeg_model']):
                self.models['eeg'] = joblib.load(self.model_paths['eeg_model'])
                print("✅ EEG模型加载成功")

            # 加载MMSE模型
            if os.path.exists(self.model_paths['mmse_model']):
                self.models['mmse'] = joblib.load(self.model_paths['mmse_model'])
                print("✅ MMSE模型加载成功")
            else:
                print("⚠️ MMSE模型未找到，将使用规则引擎")

            # 加载血液标志物模型
            if os.path.exists(self.model_paths['blood_model']):
                self.models['blood'] = joblib.load(self.model_paths['blood_model'])
                print("✅ 血液标志物模型加载成功")
            else:
                print("⚠️ 血液标志物模型未找到，将使用规则引擎")

        except Exception as e:
            print(f"❌ 模型加载失败: {e}")

    def upload_image(self):
        """上传图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择MRI/CT图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff *.dcm")]
        )

        if file_path:
            self.current_data['image_path'] = file_path
            self.display_image(file_path)
            print(f"📁 图像已上传: {file_path}")

    def display_image(self, image_path):
        """显示图像"""
        try:
            # 清除之前的显示
            for widget in self.image_display_frame.winfo_children():
                widget.destroy()

            # 加载和显示图像
            image = Image.open(image_path)
            image = image.resize((300, 300), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)

            image_label = tk.Label(self.image_display_frame, image=photo)
            image_label.image = photo  # 保持引用
            image_label.pack(pady=20)

            # 显示文件信息
            info_label = ctk.CTkLabel(
                self.image_display_frame,
                text=f"文件: {os.path.basename(image_path)}",
                font=ctk.CTkFont(size=12)
            )
            info_label.pack()

        except Exception as e:
            messagebox.showerror("错误", f"无法显示图像: {e}")

    def upload_eeg_data(self):
        """上传EEG数据"""
        file_path = filedialog.askopenfilename(
            title="选择EEG数据文件",
            filetypes=[("EEG文件", "*.edf *.set *.fif *.csv *.npy")]
        )

        if file_path:
            try:
                # 根据文件类型加载EEG数据
                if file_path.endswith('.npy'):
                    eeg_data = np.load(file_path)
                elif file_path.endswith('.csv'):
                    eeg_data = pd.read_csv(file_path).values
                else:
                    # 对于其他格式，这里需要使用MNE库
                    eeg_data = np.random.randn(19, 1000)  # 示例数据

                self.current_data['eeg_data'] = eeg_data
                self.display_eeg_info(eeg_data)
                print(f"📊 EEG数据已上传: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"无法加载EEG数据: {e}")

    def display_eeg_info(self, eeg_data):
        """显示EEG数据信息"""
        # 清除之前的显示
        for widget in self.eeg_params_frame.winfo_children():
            widget.destroy()

        # 显示EEG参数
        info_text = f"""
        📊 EEG数据信息:
        • 数据形状: {eeg_data.shape}
        • 通道数: {eeg_data.shape[0] if len(eeg_data.shape) > 1 else 1}
        • 采样点数: {eeg_data.shape[1] if len(eeg_data.shape) > 1 else len(eeg_data)}
        • 数据类型: {eeg_data.dtype}
        """

        info_label = ctk.CTkLabel(
            self.eeg_params_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="left"
        )
        info_label.pack(pady=20)

    def calculate_mmse(self):
        """计算MMSE评分"""
        try:
            # 获取各项评分
            scores = {}
            total_score = 0

            for key, var in self.mmse_vars.items():
                value = var.get()
                if value:
                    score = float(value)
                    scores[key] = score
                    total_score += score
                else:
                    scores[key] = 0

            # 存储MMSE数据
            self.current_data['mmse_scores'] = {
                'total_score': total_score,
                'subscores': scores,
                'max_score': 30
            }

            # 分析认知状态
            cognitive_status = self.analyze_mmse_score(total_score)

            # 显示结果
            self.display_mmse_results(total_score, cognitive_status)

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def analyze_mmse_score(self, total_score):
        """分析MMSE评分"""
        if total_score >= 27:
            return {
                'status': '认知正常',
                'risk_level': '低风险',
                'description': '认知功能正常，无明显认知障碍',
                'color': 'green'
            }
        elif total_score >= 24:
            return {
                'status': '轻度认知障碍',
                'risk_level': '中风险',
                'description': '可能存在轻度认知障碍，建议进一步检查',
                'color': 'orange'
            }
        elif total_score >= 18:
            return {
                'status': '中度认知障碍',
                'risk_level': '高风险',
                'description': '存在中度认知障碍，建议及时就医',
                'color': 'red'
            }
        else:
            return {
                'status': '重度认知障碍',
                'risk_level': '极高风险',
                'description': '存在重度认知障碍，需要立即医疗干预',
                'color': 'darkred'
            }

    def display_mmse_results(self, total_score, cognitive_status):
        """显示MMSE结果"""
        # 清除之前的结果
        for widget in self.mmse_results_frame.winfo_children():
            widget.destroy()

        # 显示总分
        score_label = ctk.CTkLabel(
            self.mmse_results_frame,
            text=f"📊 MMSE总分: {total_score}/30",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        score_label.pack(pady=10)

        # 显示认知状态
        status_label = ctk.CTkLabel(
            self.mmse_results_frame,
            text=f"🧠 认知状态: {cognitive_status['status']}",
            font=ctk.CTkFont(size=16),
            text_color=cognitive_status['color']
        )
        status_label.pack(pady=5)

        # 显示风险等级
        risk_label = ctk.CTkLabel(
            self.mmse_results_frame,
            text=f"⚠️ 风险等级: {cognitive_status['risk_level']}",
            font=ctk.CTkFont(size=14)
        )
        risk_label.pack(pady=5)

        # 显示描述
        desc_label = ctk.CTkLabel(
            self.mmse_results_frame,
            text=cognitive_status['description'],
            font=ctk.CTkFont(size=12),
            wraplength=400
        )
        desc_label.pack(pady=10)

    def analyze_blood_biomarkers(self):
        """分析血液标志物"""
        try:
            # 获取血液标志物数据
            biomarkers = {}

            for key, var in self.blood_vars.items():
                value = var.get()
                if value and key != 'apoe_genotype':
                    biomarkers[key] = float(value)
                elif key == 'apoe_genotype':
                    biomarkers[key] = value

            # 存储血液标志物数据
            self.current_data['blood_biomarkers'] = biomarkers

            # 分析风险
            risk_analysis = self.analyze_blood_risk(biomarkers)

            # 显示结果
            self.display_blood_results(biomarkers, risk_analysis)

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")

    def analyze_blood_risk(self, biomarkers):
        """分析血液标志物风险"""
        risk_score = 0
        risk_factors = []

        # Aβ42/Aβ40比值分析
        if 'amyloid_beta_42' in biomarkers and 'amyloid_beta_40' in biomarkers:
            ratio = biomarkers['amyloid_beta_42'] / biomarkers['amyloid_beta_40']
            if ratio < 0.05:
                risk_score += 3
                risk_factors.append("Aβ42/Aβ40比值异常低")

        # p-Tau181分析
        if 'p_tau_181' in biomarkers:
            if biomarkers['p_tau_181'] > 20:
                risk_score += 2
                risk_factors.append("p-Tau181水平升高")

        # 神经丝轻链分析
        if 'neurofilament_light' in biomarkers:
            if biomarkers['neurofilament_light'] > 40:
                risk_score += 2
                risk_factors.append("神经丝轻链水平升高")

        # APOE基因型分析
        if 'apoe_genotype' in biomarkers:
            genotype = biomarkers['apoe_genotype']
            if 'E4' in genotype:
                if genotype == 'E4/E4':
                    risk_score += 4
                    risk_factors.append("APOE E4/E4高风险基因型")
                else:
                    risk_score += 2
                    risk_factors.append("APOE E4杂合子基因型")

        # 确定风险等级
        if risk_score >= 6:
            risk_level = "极高风险"
            color = "darkred"
        elif risk_score >= 4:
            risk_level = "高风险"
            color = "red"
        elif risk_score >= 2:
            risk_level = "中风险"
            color = "orange"
        else:
            risk_level = "低风险"
            color = "green"

        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'color': color
        }

    def display_blood_results(self, biomarkers, risk_analysis):
        """显示血液标志物结果"""
        # 清除之前的结果
        for widget in self.blood_results_frame.winfo_children():
            widget.destroy()

        # 显示风险等级
        risk_label = ctk.CTkLabel(
            self.blood_results_frame,
            text=f"🩸 血液标志物风险等级: {risk_analysis['risk_level']}",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=risk_analysis['color']
        )
        risk_label.pack(pady=10)

        # 显示风险因素
        if risk_analysis['risk_factors']:
            factors_label = ctk.CTkLabel(
                self.blood_results_frame,
                text="⚠️ 发现的风险因素:",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            factors_label.pack(pady=5)

            for factor in risk_analysis['risk_factors']:
                factor_label = ctk.CTkLabel(
                    self.blood_results_frame,
                    text=f"• {factor}",
                    font=ctk.CTkFont(size=12)
                )
                factor_label.pack(anchor="w", padx=20)

        # 显示具体数值
        values_frame = ctk.CTkFrame(self.blood_results_frame)
        values_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(values_frame, text="📊 检测数值:",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        for key, value in biomarkers.items():
            if key != 'apoe_genotype':
                value_label = ctk.CTkLabel(
                    values_frame,
                    text=f"{key}: {value}",
                    font=ctk.CTkFont(size=11)
                )
                value_label.pack(anchor="w", padx=10)
            else:
                value_label = ctk.CTkLabel(
                    values_frame,
                    text=f"APOE基因型: {value}",
                    font=ctk.CTkFont(size=11)
                )
                value_label.pack(anchor="w", padx=10)

    def analyze_image(self):
        """分析影像数据"""
        if not self.current_data['image_path']:
            messagebox.showwarning("警告", "请先上传图像")
            return

        try:
            # 图像预处理
            from tensorflow.keras.preprocessing.image import load_img, img_to_array

            img = load_img(self.current_data['image_path'], target_size=(150, 150))
            img_array = img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            results = {}

            # CT检测
            if self.models['ct']:
                ct_pred = self.models['ct'].predict(img_array, verbose=0)
                is_ct = ct_pred[0][0] > 0.5
                results['ct_detection'] = {
                    'is_ct': is_ct,
                    'confidence': float(ct_pred[0][0]),
                    'type': 'CT图像' if is_ct else '非CT图像'
                }

            # MRI痴呆症分析
            if self.models['mri']:
                mri_pred = self.models['mri'].predict(img_array, verbose=0)
                predicted_class = np.argmax(mri_pred, axis=1)[0]
                confidence = float(np.max(mri_pred))

                results['mri_analysis'] = {
                    'predicted_class': predicted_class,
                    'class_name': self.class_labels['dementia'][predicted_class],
                    'confidence': confidence,
                    'probabilities': mri_pred[0].tolist()
                }

            # 存储结果
            self.current_data['results']['image'] = results

            # 显示结果
            self.display_image_results(results)

        except Exception as e:
            messagebox.showerror("错误", f"影像分析失败: {e}")

    def display_image_results(self, results):
        """显示影像分析结果"""
        # 清除之前的结果
        for widget in self.image_results_frame.winfo_children():
            widget.destroy()

        # CT检测结果
        if 'ct_detection' in results:
            ct_result = results['ct_detection']
            ct_label = ctk.CTkLabel(
                self.image_results_frame,
                text=f"🔍 图像类型: {ct_result['type']} (置信度: {ct_result['confidence']:.2%})",
                font=ctk.CTkFont(size=14)
            )
            ct_label.pack(pady=5)

        # MRI分析结果
        if 'mri_analysis' in results:
            mri_result = results['mri_analysis']
            mri_label = ctk.CTkLabel(
                self.image_results_frame,
                text=f"🧠 痴呆症分析: {mri_result['class_name']} (置信度: {mri_result['confidence']:.2%})",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            mri_label.pack(pady=5)

            # 显示各类别概率
            prob_frame = ctk.CTkFrame(self.image_results_frame)
            prob_frame.pack(fill="x", padx=10, pady=10)

            ctk.CTkLabel(prob_frame, text="📊 各类别概率:",
                        font=ctk.CTkFont(size=12, weight="bold")).pack(pady=5)

            for i, prob in enumerate(mri_result['probabilities']):
                prob_label = ctk.CTkLabel(
                    prob_frame,
                    text=f"{self.class_labels['dementia'][i]}: {prob:.2%}",
                    font=ctk.CTkFont(size=11)
                )
                prob_label.pack(anchor="w", padx=10)

    def analyze_eeg(self):
        """分析EEG数据"""
        if self.current_data['eeg_data'] is None:
            messagebox.showwarning("警告", "请先上传EEG数据")
            return

        try:
            # 这里应该使用实际的EEG分析模型
            # 暂时使用模拟结果
            eeg_result = {
                'predicted_class': 1,
                'class_name': self.class_labels['eeg'][1],
                'confidence': 0.85,
                'probabilities': [0.1, 0.85, 0.05]
            }

            # 存储结果
            self.current_data['results']['eeg'] = eeg_result

            messagebox.showinfo("成功", f"EEG分析完成: {eeg_result['class_name']}")

        except Exception as e:
            messagebox.showerror("错误", f"EEG分析失败: {e}")

    def comprehensive_analysis(self):
        """五模态综合分析"""
        # 检查数据完整性
        available_modalities = []

        if self.current_data['image_path'] and 'image' in self.current_data['results']:
            available_modalities.append('影像')

        if self.current_data['eeg_data'] is not None and 'eeg' in self.current_data['results']:
            available_modalities.append('EEG')

        if self.current_data['mmse_scores']:
            available_modalities.append('MMSE')

        if self.current_data['blood_biomarkers']:
            available_modalities.append('血液标志物')

        if len(available_modalities) < 2:
            messagebox.showwarning("警告", "至少需要两种模态的数据进行综合分析")
            return

        try:
            # 执行多模态融合
            fusion_result = self.multimodal_fusion()

            # 显示综合结果
            self.display_comprehensive_results(fusion_result, available_modalities)

        except Exception as e:
            messagebox.showerror("错误", f"综合分析失败: {e}")

    def multimodal_fusion(self):
        """多模态数据融合"""
        fusion_strategy = self.fusion_strategy.get()

        # 收集各模态的预测结果
        modality_predictions = {}

        # 影像模态
        if 'image' in self.current_data['results']:
            mri_result = self.current_data['results']['image'].get('mri_analysis')
            if mri_result:
                modality_predictions['image'] = {
                    'dementia_prob': mri_result['probabilities'],
                    'confidence': mri_result['confidence'],
                    'weight': 0.3
                }

        # EEG模态
        if 'eeg' in self.current_data['results']:
            eeg_result = self.current_data['results']['eeg']
            modality_predictions['eeg'] = {
                'dementia_prob': eeg_result['probabilities'],
                'confidence': eeg_result['confidence'],
                'weight': 0.25
            }

        # MMSE模态
        if self.current_data['mmse_scores']:
            mmse_score = self.current_data['mmse_scores']['total_score']
            # 将MMSE分数转换为痴呆概率
            mmse_prob = self.mmse_to_dementia_prob(mmse_score)
            modality_predictions['mmse'] = {
                'dementia_prob': mmse_prob,
                'confidence': 0.9,
                'weight': 0.25
            }

        # 血液标志物模态
        if self.current_data['blood_biomarkers']:
            blood_risk = self.analyze_blood_risk(self.current_data['blood_biomarkers'])
            blood_prob = self.blood_risk_to_dementia_prob(blood_risk['risk_score'])
            modality_predictions['blood'] = {
                'dementia_prob': blood_prob,
                'confidence': 0.8,
                'weight': 0.2
            }

        # 执行融合
        if fusion_strategy == "加权平均":
            fused_result = self.weighted_average_fusion(modality_predictions)
        elif fusion_strategy == "投票机制":
            fused_result = self.voting_fusion(modality_predictions)
        else:
            fused_result = self.weighted_average_fusion(modality_predictions)

        return fused_result

    def mmse_to_dementia_prob(self, mmse_score):
        """将MMSE分数转换为痴呆概率"""
        if mmse_score >= 27:
            return [0.8, 0.1, 0.05, 0.05]  # 主要是无痴呆
        elif mmse_score >= 24:
            return [0.2, 0.1, 0.6, 0.1]   # 主要是非常轻度痴呆
        elif mmse_score >= 18:
            return [0.6, 0.2, 0.1, 0.1]   # 主要是轻度痴呆
        else:
            return [0.1, 0.7, 0.1, 0.1]   # 主要是中度痴呆

    def blood_risk_to_dementia_prob(self, risk_score):
        """将血液风险分数转换为痴呆概率"""
        if risk_score >= 6:
            return [0.1, 0.6, 0.1, 0.2]   # 高风险，倾向于中度痴呆
        elif risk_score >= 4:
            return [0.4, 0.2, 0.2, 0.2]   # 中高风险，倾向于轻度痴呆
        elif risk_score >= 2:
            return [0.2, 0.1, 0.3, 0.4]   # 中风险，倾向于非常轻度痴呆
        else:
            return [0.1, 0.05, 0.8, 0.05] # 低风险，主要是无痴呆

    def weighted_average_fusion(self, modality_predictions):
        """加权平均融合"""
        # 归一化权重
        total_weight = sum(pred['weight'] for pred in modality_predictions.values())

        # 初始化融合概率
        fused_prob = np.zeros(4)  # 4个痴呆类别
        total_confidence = 0

        for modality, pred in modality_predictions.items():
            weight = pred['weight'] / total_weight
            prob = np.array(pred['dementia_prob'])
            confidence = pred['confidence']

            fused_prob += weight * prob * confidence
            total_confidence += weight * confidence

        # 归一化概率
        fused_prob = fused_prob / np.sum(fused_prob)

        # 确定最终预测
        predicted_class = np.argmax(fused_prob)
        final_confidence = total_confidence / len(modality_predictions)

        return {
            'predicted_class': predicted_class,
            'class_name': self.class_labels['dementia'][predicted_class],
            'confidence': final_confidence,
            'probabilities': fused_prob.tolist(),
            'modalities_used': list(modality_predictions.keys()),
            'fusion_method': '加权平均'
        }

    def voting_fusion(self, modality_predictions):
        """投票机制融合"""
        votes = np.zeros(4)

        for modality, pred in modality_predictions.items():
            prob = np.array(pred['dementia_prob'])
            predicted_class = np.argmax(prob)
            confidence = pred['confidence']

            # 加权投票
            votes[predicted_class] += confidence * pred['weight']

        # 确定最终预测
        predicted_class = np.argmax(votes)
        final_confidence = votes[predicted_class] / np.sum(votes)

        # 计算概率分布
        probabilities = votes / np.sum(votes)

        return {
            'predicted_class': predicted_class,
            'class_name': self.class_labels['dementia'][predicted_class],
            'confidence': final_confidence,
            'probabilities': probabilities.tolist(),
            'modalities_used': list(modality_predictions.keys()),
            'fusion_method': '投票机制'
        }

    def display_comprehensive_results(self, fusion_result, available_modalities):
        """显示综合分析结果"""
        # 清除之前的结果
        for widget in self.comprehensive_results_frame.winfo_children():
            widget.destroy()

        # 标题
        title_label = ctk.CTkLabel(
            self.comprehensive_results_frame,
            text="🎯 五模态综合分析结果",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=10)

        # 使用的模态
        modalities_label = ctk.CTkLabel(
            self.comprehensive_results_frame,
            text=f"📊 分析模态: {', '.join(available_modalities)}",
            font=ctk.CTkFont(size=14)
        )
        modalities_label.pack(pady=5)

        # 融合方法
        method_label = ctk.CTkLabel(
            self.comprehensive_results_frame,
            text=f"🔬 融合方法: {fusion_result['fusion_method']}",
            font=ctk.CTkFont(size=14)
        )
        method_label.pack(pady=5)

        # 最终诊断
        diagnosis_frame = ctk.CTkFrame(self.comprehensive_results_frame)
        diagnosis_frame.pack(fill="x", padx=20, pady=20)

        diagnosis_label = ctk.CTkLabel(
            diagnosis_frame,
            text=f"🏥 综合诊断: {fusion_result['class_name']}",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="red" if "痴呆" in fusion_result['class_name'] else "green"
        )
        diagnosis_label.pack(pady=10)

        confidence_label = ctk.CTkLabel(
            diagnosis_frame,
            text=f"🎯 诊断置信度: {fusion_result['confidence']:.2%}",
            font=ctk.CTkFont(size=16)
        )
        confidence_label.pack(pady=5)

        # 概率分布
        prob_frame = ctk.CTkFrame(self.comprehensive_results_frame)
        prob_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(prob_frame, text="📈 各类别概率分布:",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        for i, prob in enumerate(fusion_result['probabilities']):
            prob_label = ctk.CTkLabel(
                prob_frame,
                text=f"{self.class_labels['dementia'][i]}: {prob:.2%}",
                font=ctk.CTkFont(size=12)
            )
            prob_label.pack(anchor="w", padx=10)

        # 存储综合结果
        self.current_data['results']['comprehensive'] = fusion_result

        # 更新结果展示选项卡
        self.update_results_display()

    def update_results_display(self):
        """更新结果展示"""
        # 清除之前的显示
        for widget in self.results_display_frame.winfo_children():
            widget.destroy()

        # 创建结果报告
        self.create_results_report()

    def create_results_report(self):
        """创建结果报告"""
        # 报告标题
        title_label = ctk.CTkLabel(
            self.results_display_frame,
            text="📋 五模态AI痴呆症检测报告",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=10)

        # 检测时间
        time_label = ctk.CTkLabel(
            self.results_display_frame,
            text=f"🕐 检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            font=ctk.CTkFont(size=12)
        )
        time_label.pack(pady=5)

        # 各模态结果摘要
        if 'comprehensive' in self.current_data['results']:
            comp_result = self.current_data['results']['comprehensive']

            # 最终诊断框
            final_frame = ctk.CTkFrame(self.results_display_frame)
            final_frame.pack(fill="x", padx=20, pady=20)

            ctk.CTkLabel(final_frame, text="🏥 最终诊断结果",
                        font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)

            ctk.CTkLabel(final_frame, text=comp_result['class_name'],
                        font=ctk.CTkFont(size=18, weight="bold"),
                        text_color="red" if "痴呆" in comp_result['class_name'] else "green").pack(pady=5)

            ctk.CTkLabel(final_frame, text=f"置信度: {comp_result['confidence']:.2%}",
                        font=ctk.CTkFont(size=14)).pack(pady=5)

        # 建议和说明
        recommendation_frame = ctk.CTkFrame(self.results_display_frame)
        recommendation_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(recommendation_frame, text="💡 医疗建议",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        recommendation_text = self.generate_medical_recommendation()

        ctk.CTkLabel(recommendation_frame, text=recommendation_text,
                    font=ctk.CTkFont(size=12), wraplength=600, justify="left").pack(pady=10, padx=10)

    def generate_medical_recommendation(self):
        """生成医疗建议"""
        if 'comprehensive' not in self.current_data['results']:
            return "请完成综合分析以获取医疗建议。"

        result = self.current_data['results']['comprehensive']
        class_name = result['class_name']
        confidence = result['confidence']

        if "无痴呆" in class_name:
            return """
            ✅ 检测结果显示认知功能正常。

            建议：
            • 保持健康的生活方式
            • 定期进行认知功能检查
            • 保持适度的体育锻炼和社交活动
            • 如有担忧，建议咨询专业医生
            """
        elif "非常轻度" in class_name:
            return """
            ⚠️ 检测到非常轻度的认知变化。

            建议：
            • 建议尽快咨询神经科医生
            • 进行更详细的神经心理学评估
            • 定期监测认知功能变化
            • 保持健康的生活方式
            • 考虑认知训练和康复
            """
        elif "轻度痴呆" in class_name:
            return """
            🔴 检测到轻度痴呆症状。

            建议：
            • 立即咨询神经科专家
            • 进行全面的医学评估
            • 制定个性化的治疗方案
            • 考虑药物治疗和非药物干预
            • 家属应了解疾病管理知识
            """
        else:  # 中度痴呆
            return """
            🚨 检测到中度痴呆症状。

            建议：
            • 紧急就医，寻求专业治疗
            • 需要专业的医疗团队管理
            • 考虑综合治疗方案
            • 家属需要专业指导和支持
            • 评估日常生活能力和安全性
            """

    def export_pdf_report(self):
        """导出PDF报告"""
        messagebox.showinfo("功能开发中", "PDF导出功能正在开发中...")

    def export_html_report(self):
        """导出HTML报告"""
        messagebox.showinfo("功能开发中", "HTML导出功能正在开发中...")

    def run(self):
        """运行应用"""
        self.root.mainloop()


# 主程序入口
if __name__ == "__main__":
    print("🚀 启动五模态AI痴呆症检测系统...")

    app = FiveModalDementiaDetector()
    app.run()
