# -*- coding: utf-8 -*-
"""
文字不遮挡优化代码
解决GUI界面中文字显示被遮挡的问题
"""

import customtkinter as ctk

# ==================== 历史记录显示优化 ====================

def show_history_optimized(self):
    """显示历史记录（优化版，避免文字遮挡）"""
    if not self.results_history:
        messagebox.showinfo("提示", "暂无历史记录")
        return
        
    # 创建历史记录窗口（增加窗口大小，确保有足够空间）
    history_window = ctk.CTkToplevel(self.root)
    history_window.title("症状分析历史")
    history_window.geometry("800x600")  # 增加窗口大小
    history_window.resizable(True, True)  # 允许调整大小
    
    # 添加标题
    title_label = ctk.CTkLabel(
        history_window,
        text="📋 症状分析历史记录",
        font=ctk.CTkFont(size=18, weight="bold")
    )
    title_label.pack(pady=(20, 10))
    
    # 历史记录列表（优化布局）
    history_frame = ctk.CTkScrollableFrame(history_window, height=450)  # 设置固定高度
    history_frame.pack(fill="both", expand=True, padx=20, pady=10)
    
    for i, record in enumerate(reversed(self.results_history)):
        # 创建记录框架（增加间距和高度）
        record_frame = ctk.CTkFrame(history_frame)
        record_frame.pack(fill="x", pady=8, padx=5)  # 增加垂直和水平间距
        record_frame.configure(height=120)  # 设置最小高度
        
        # 基本信息（优化显示格式，避免文字遮挡）
        info_text = f"#{len(self.results_history)-i} - {record['timestamp']}\n"
        
        # CT验证信息（辅助，简化显示）
        if record.get('ct_validation'):
            ct_data = record['ct_validation']
            info_text += f"验证: {'✅CT' if ct_data['is_ct'] else '⚠️非CT'} ({ct_data['confidence']:.1%})\n"
        
        # 症状分析结果（核心，优化显示）
        symptom_name = record['predicted_class_name'].split('(')[0] if '(' in record['predicted_class_name'] else record['predicted_class_name']
        
        # 截断长的症状名称，避免文字重叠
        if len(symptom_name) > 15:  # 可以根据UI布局调整字符长度
            symptom_name = symptom_name[:15] + "..."  # 如果名称过长，添加省略号

        # 移除 "Demented" 部分
        if "Demented" in symptom_name:
            symptom_name = symptom_name.replace("Demented", "")

        # 拼接信息文本
        info_text += f"症状: {symptom_name}\n"
        info_text += f"置信度: {record['confidence']:.1%}"

        # 确保显示区域足够大，避免显示不完整
        record_label = ctk.CTkLabel(
            record_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="left",
            wraplength=350,  # 设置最大宽度，确保文本换行
            height=100  # 增加显示区域高度，确保足够空间显示文本
        )
        
        # 只需要创建一个label并显示
        record_label.pack(padx=15, pady=10, fill="x")  # 添加fill="x"确保水平填充

# ==================== 症状结果显示优化 ====================

def create_symptom_results_panel_optimized(self):
    """创建症状分析结果显示面板（优化版，避免文字遮挡）"""
    results_frame = ctk.CTkFrame(self.right_panel)
    results_frame.pack(fill="both", expand=True, padx=20, pady=10)
    
    # 结果标题
    results_title = ctk.CTkLabel(
        results_frame,
        text="📊 症状分析结果",
        font=ctk.CTkFont(size=16, weight="bold")
    )
    results_title.pack(pady=(15, 8))
    
    # 症状分析结果容器（避免文字遮挡）
    self.symptom_result_frame = ctk.CTkFrame(results_frame)
    self.symptom_result_frame.pack(fill="x", padx=10, pady=5)
    
    # 症状分析结果（核心显示 - 重点突出，增加高度）
    self.symptom_result_label = ctk.CTkLabel(
        self.symptom_result_frame,
        text="症状类型: 等待分析...",
        font=ctk.CTkFont(size=15, weight="bold"),
        wraplength=350,  # 设置文本换行宽度
        justify="center",
        height=70  # 固定高度，避免遮挡，增加到70px
    )
    self.symptom_result_label.pack(pady=12, padx=10, fill="x")  # 增加内边距
    
    # 置信度显示容器
    self.confidence_frame = ctk.CTkFrame(results_frame)
    self.confidence_frame.pack(fill="x", padx=10, pady=3)
    
    self.confidence_label = ctk.CTkLabel(
        self.confidence_frame,
        text="",
        font=ctk.CTkFont(size=12),
        height=30  # 固定高度，增加到30px
    )
    self.confidence_label.pack(pady=6, padx=10)  # 增加内边距
    
    # 详细概率显示框（4种症状）
    prob_title = ctk.CTkLabel(
        results_frame,
        text="4种症状概率分布:",
        font=ctk.CTkFont(size=12, weight="bold")
    )
    prob_title.pack(pady=(8, 3))
    
    # 增加概率显示框高度，确保4种症状都能完整显示
    self.details_frame = ctk.CTkScrollableFrame(results_frame, height=160)  # 增加到160px
    self.details_frame.pack(fill="x", padx=10, pady=5)
    
    # CT验证结果容器（辅助信息，小字显示在底部）
    self.ct_validation_frame = ctk.CTkFrame(results_frame)
    self.ct_validation_frame.pack(fill="x", padx=10, pady=3)
    
    self.ct_validation_label = ctk.CTkLabel(
        self.ct_validation_frame,
        text="CT验证: 等待验证...",
        font=ctk.CTkFont(size=9),
        wraplength=350,
        text_color="gray",
        height=25  # 固定高度，增加到25px
    )
    self.ct_validation_label.pack(pady=4, padx=10)  # 增加内边距

def display_symptom_results_optimized(self, result_data):
    """显示症状分析结果（优化版，避免文字遮挡）"""
    # 保存到历史记录
    self.results_history.append(result_data)
    
    # 显示症状分析结果（核心显示 - 重点突出）
    class_name = result_data['predicted_class_name']
    confidence = result_data['confidence']

    # 根据症状类型设置颜色和风险等级
    if "NonDemented" in class_name:
        color = "green"
        risk_level = "低风险 ✅"
    elif "VeryMild" in class_name:
        color = "orange"
        risk_level = "轻微风险 ⚠️"
    elif "Mild" in class_name:
        color = "red"
        risk_level = "中等风险 ⚠️"
    else:  # ModerateDemented
        color = "darkred"
        risk_level = "高风险 🚨"

    # 优化文字显示，避免遮挡
    # 将长文本分为两行显示，确保完整可见
    symptom_type = class_name.split('(')[0] if '(' in class_name else class_name
    
    # 进一步简化症状名称
    if "Demented" in symptom_type:
        symptom_type = symptom_type.replace("Demented", "")
    
    # 如果名称仍然过长，进行截断
    if len(symptom_type) > 12:
        symptom_type = symptom_type[:12] + "..."
    
    result_text = f"🎯 症状: {symptom_type}\n风险: {risk_level}"
    
    self.symptom_result_label.configure(
        text=result_text,
        text_color=color,
        height=70  # 确保有足够高度显示两行文字
    )

    self.confidence_label.configure(
        text=f"🎯 置信度: {confidence:.2%}",
        text_color="lightblue"
    )
    
    # 清空详细结果框
    for widget in self.details_frame.winfo_children():
        widget.destroy()
        
    # 显示4种症状的详细概率（优化布局，避免遮挡）
    for i, prob in enumerate(result_data['probabilities']):
        prob_frame = ctk.CTkFrame(self.details_frame)
        prob_frame.pack(fill="x", pady=4, padx=5)  # 增加间距
        prob_frame.configure(height=35)  # 设置固定高度
        
        # 简化标签文本，避免过长
        label_text = self.class_labels[i].split('(')[0]
        if "Demented" in label_text:
            label_text = label_text.replace("Demented", "")
        
        # 进一步截断长名称
        if len(label_text) > 10:
            label_text = label_text[:10] + "..."
        
        prob_label = ctk.CTkLabel(
            prob_frame,
            text=f"{label_text}:",  # 简化显示
            font=ctk.CTkFont(size=11),
            width=80  # 固定宽度
        )
        prob_label.pack(side="left", padx=8, pady=8)
        
        # 概率数值显示
        prob_value = ctk.CTkLabel(
            prob_frame,
            text=f"{prob:.1%}",
            font=ctk.CTkFont(size=10, weight="bold"),
            width=50
        )
        prob_value.pack(side="left", padx=3)
        
        # 概率条（调整大小）
        prob_bar = ctk.CTkProgressBar(prob_frame, width=120, height=15)
        prob_bar.pack(side="right", padx=8, pady=8)
        prob_bar.set(prob)

# ==================== 通用文字优化函数 ====================

def optimize_text_display(text, max_length=15):
    """优化文本显示，避免过长文字遮挡"""
    # 移除括号内容
    if '(' in text:
        text = text.split('(')[0]
    
    # 移除常见的冗余词汇
    replacements = {
        "Demented": "",
        "Dementia": "",
        "Analysis": "",
        "Result": ""
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    # 截断过长文本
    if len(text) > max_length:
        text = text[:max_length] + "..."
    
    return text.strip()

def create_text_label_safe(parent, text, **kwargs):
    """创建安全的文本标签，避免遮挡"""
    # 默认参数
    default_kwargs = {
        'wraplength': 350,
        'justify': 'left',
        'height': 50,
        'font': ctk.CTkFont(size=12)
    }
    
    # 合并用户参数
    default_kwargs.update(kwargs)
    
    # 优化文本
    if 'max_text_length' in default_kwargs:
        max_length = default_kwargs.pop('max_text_length')
        text = optimize_text_display(text, max_length)
    
    return ctk.CTkLabel(parent, text=text, **default_kwargs)

# ==================== 使用示例 ====================

"""
使用方法：

1. 替换原有的历史记录显示函数：
   self.show_history = self.show_history_optimized

2. 替换原有的症状结果面板创建：
   self.create_symptom_results_panel = self.create_symptom_results_panel_optimized

3. 替换原有的症状结果显示：
   self.display_symptom_results = self.display_symptom_results_optimized

4. 使用通用文字优化函数：
   optimized_text = optimize_text_display("VeryMildDemented(非常轻度痴呆)", 12)
   
5. 使用安全文本标签创建：
   label = create_text_label_safe(
       parent_frame, 
       "长文本内容...", 
       max_text_length=15,
       height=60
   )

主要优化点：
- 增加容器高度和宽度
- 设置文本换行宽度
- 截断过长文本并添加省略号
- 增加内边距和外边距
- 移除冗余词汇
- 设置固定高度避免动态调整导致的遮挡
- 使用fill="x"确保水平填充
"""
