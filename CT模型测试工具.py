"""
CT图像识别模型测试工具
用于测试和评估CT图像识别模型的准确性
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import os
import cv2
from tkinter import filedialog, messagebox
import tkinter as tk
from tkinter import ttk
import warnings

# 抑制警告
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class CTModelTester:
    """CT图像识别模型测试器"""
    
    def __init__(self):
        self.model_path = r"D:\模型开发\ct_other_model.h5"
        self.model = None
        self.test_results = []
        
        # 创建GUI
        self.setup_gui()
        self.load_model()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("CT图像识别模型测试工具")
        self.root.geometry("800x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 CT图像识别模型测试", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 模型状态
        self.model_status_label = ttk.Label(main_frame, text="模型状态: 加载中...", 
                                           font=("Arial", 10))
        self.model_status_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=(0, 20))
        
        ttk.Button(button_frame, text="📁 测试单张图像", 
                  command=self.test_single_image).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📂 批量测试", 
                  command=self.test_batch_images).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧪 模型信息", 
                  command=self.show_model_info).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 清空结果", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="测试结果", padding="10")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, height=20, width=80, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 统计信息
        self.stats_label = ttk.Label(main_frame, text="统计信息: 暂无测试", 
                                    font=("Arial", 10))
        self.stats_label.grid(row=4, column=0, columnspan=3, pady=(10, 0))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def load_model(self):
        """加载CT识别模型"""
        try:
            if os.path.exists(self.model_path):
                self.model = tf.keras.models.load_model(self.model_path)
                self.model_status_label.config(text="模型状态: ✅ 加载成功", foreground="green")
                self.log_result("🤖 CT识别模型加载成功")
                self.log_result(f"📁 模型路径: {self.model_path}")
                self.show_model_info()
            else:
                self.model_status_label.config(text="模型状态: ❌ 文件不存在", foreground="red")
                self.log_result(f"❌ 模型文件不存在: {self.model_path}")
                messagebox.showerror("错误", f"模型文件不存在:\n{self.model_path}")
        except Exception as e:
            self.model_status_label.config(text="模型状态: ❌ 加载失败", foreground="red")
            self.log_result(f"❌ 模型加载失败: {e}")
            messagebox.showerror("错误", f"模型加载失败:\n{e}")
    
    def preprocess_image(self, image_path):
        """预处理图像"""
        try:
            # 加载图像
            image = Image.open(image_path)
            
            # 转换为RGB
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整尺寸
            image = image.resize((150, 150))
            
            # 转换为数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
        except Exception as e:
            self.log_result(f"❌ 图像预处理失败: {e}")
            return None
    
    def predict_ct(self, image_array):
        """预测是否为CT图像"""
        try:
            if self.model is None:
                return None, "模型未加载"
            
            # 进行预测
            prediction = self.model.predict(image_array, verbose=0)
            
            # 获取置信度
            confidence = float(prediction[0][0])
            
            # 判断是否为CT图像（阈值0.5）
            is_ct = confidence > 0.5
            
            return {
                'is_ct': is_ct,
                'confidence': confidence,
                'prediction_raw': prediction[0].tolist()
            }, None
            
        except Exception as e:
            return None, str(e)
    
    def test_single_image(self):
        """测试单张图像"""
        if self.model is None:
            messagebox.showerror("错误", "模型未加载")
            return
        
        # 选择图像文件
        file_path = filedialog.askopenfilename(
            title="选择要测试的图像",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        self.log_result(f"\n{'='*60}")
        self.log_result(f"🔍 测试图像: {os.path.basename(file_path)}")
        self.log_result(f"📁 完整路径: {file_path}")
        
        # 预处理图像
        image_array = self.preprocess_image(file_path)
        if image_array is None:
            return
        
        # 进行预测
        result, error = self.predict_ct(image_array)
        if error:
            self.log_result(f"❌ 预测失败: {error}")
            return
        
        # 显示结果
        is_ct = result['is_ct']
        confidence = result['confidence']
        
        self.log_result(f"🎯 预测结果: {'✅ CT图像' if is_ct else '❌ 非CT图像'}")
        self.log_result(f"📊 置信度: {confidence:.6f} ({confidence*100:.2f}%)")
        self.log_result(f"🔢 原始输出: {result['prediction_raw']}")
        
        # 置信度评估
        if confidence > 0.9:
            confidence_level = "非常高"
        elif confidence > 0.7:
            confidence_level = "高"
        elif confidence > 0.5:
            confidence_level = "中等"
        else:
            confidence_level = "低"
        
        self.log_result(f"📈 置信度等级: {confidence_level}")
        
        # 记录测试结果
        self.test_results.append({
            'file': os.path.basename(file_path),
            'is_ct': is_ct,
            'confidence': confidence,
            'manual_label': None  # 可以手动标注
        })
        
        self.update_statistics()
        
        # 询问用户实际标签
        actual_label = messagebox.askyesno(
            "人工标注", 
            f"这张图像实际上是CT图像吗？\n\n"
            f"模型预测: {'CT图像' if is_ct else '非CT图像'}\n"
            f"置信度: {confidence*100:.2f}%"
        )
        
        self.test_results[-1]['manual_label'] = actual_label
        self.log_result(f"👤 人工标注: {'✅ CT图像' if actual_label else '❌ 非CT图像'}")
        
        # 判断预测是否正确
        is_correct = (is_ct == actual_label)
        self.test_results[-1]['correct'] = is_correct
        self.log_result(f"🎯 预测准确性: {'✅ 正确' if is_correct else '❌ 错误'}")
        
        self.update_statistics()
    
    def test_batch_images(self):
        """批量测试图像"""
        if self.model is None:
            messagebox.showerror("错误", "模型未加载")
            return
        
        # 选择文件夹
        folder_path = filedialog.askdirectory(title="选择包含测试图像的文件夹")
        if not folder_path:
            return
        
        # 获取所有图像文件
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        image_files = [f for f in os.listdir(folder_path) 
                      if f.lower().endswith(image_extensions)]
        
        if not image_files:
            messagebox.showinfo("提示", "文件夹中没有找到图像文件")
            return
        
        self.log_result(f"\n{'='*60}")
        self.log_result(f"📂 批量测试文件夹: {folder_path}")
        self.log_result(f"📊 找到 {len(image_files)} 张图像")
        
        # 逐个测试
        for i, filename in enumerate(image_files, 1):
            file_path = os.path.join(folder_path, filename)
            
            self.log_result(f"\n[{i}/{len(image_files)}] 测试: {filename}")
            
            # 预处理和预测
            image_array = self.preprocess_image(file_path)
            if image_array is None:
                continue
            
            result, error = self.predict_ct(image_array)
            if error:
                self.log_result(f"❌ 预测失败: {error}")
                continue
            
            is_ct = result['is_ct']
            confidence = result['confidence']
            
            self.log_result(f"   结果: {'✅ CT' if is_ct else '❌ 非CT'} "
                          f"(置信度: {confidence:.4f})")
            
            # 记录结果
            self.test_results.append({
                'file': filename,
                'is_ct': is_ct,
                'confidence': confidence,
                'manual_label': None,
                'correct': None
            })
        
        self.log_result(f"\n✅ 批量测试完成")
        self.update_statistics()
    
    def show_model_info(self):
        """显示模型信息"""
        if self.model is None:
            self.log_result("❌ 模型未加载，无法显示信息")
            return
        
        try:
            self.log_result(f"\n{'='*60}")
            self.log_result("🤖 模型详细信息:")
            self.log_result(f"📁 模型路径: {self.model_path}")
            
            # 模型架构信息
            self.log_result(f"🏗️ 模型架构:")
            self.log_result(f"   输入形状: {self.model.input_shape}")
            self.log_result(f"   输出形状: {self.model.output_shape}")
            self.log_result(f"   层数: {len(self.model.layers)}")
            
            # 参数统计
            total_params = self.model.count_params()
            trainable_params = sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights])
            non_trainable_params = total_params - trainable_params
            
            self.log_result(f"📊 参数统计:")
            self.log_result(f"   总参数: {total_params:,}")
            self.log_result(f"   可训练参数: {trainable_params:,}")
            self.log_result(f"   不可训练参数: {non_trainable_params:,}")
            
            # 模型摘要
            self.log_result(f"\n📋 模型结构摘要:")
            
            # 重定向模型摘要输出
            import io
            import sys
            
            old_stdout = sys.stdout
            sys.stdout = buffer = io.StringIO()
            
            self.model.summary()
            
            sys.stdout = old_stdout
            summary_text = buffer.getvalue()
            
            for line in summary_text.split('\n'):
                if line.strip():
                    self.log_result(f"   {line}")
            
        except Exception as e:
            self.log_result(f"❌ 获取模型信息失败: {e}")
    
    def update_statistics(self):
        """更新统计信息"""
        if not self.test_results:
            self.stats_label.config(text="统计信息: 暂无测试")
            return
        
        total_tests = len(self.test_results)
        ct_predictions = sum(1 for r in self.test_results if r['is_ct'])
        non_ct_predictions = total_tests - ct_predictions
        
        # 计算准确率（如果有人工标注）
        labeled_results = [r for r in self.test_results if r['manual_label'] is not None]
        if labeled_results:
            correct_predictions = sum(1 for r in labeled_results if r['correct'])
            accuracy = correct_predictions / len(labeled_results) * 100
            accuracy_text = f"准确率: {accuracy:.1f}%"
        else:
            accuracy_text = "准确率: 待标注"
        
        stats_text = (f"统计信息: 总测试 {total_tests} 张 | "
                     f"预测CT {ct_predictions} 张 | "
                     f"预测非CT {non_ct_predictions} 张 | "
                     f"{accuracy_text}")
        
        self.stats_label.config(text=stats_text)
    
    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.test_results.clear()
        self.update_statistics()
    
    def log_result(self, message):
        """记录结果到文本框"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run(self):
        """运行测试工具"""
        self.root.mainloop()


def main():
    """主函数"""
    print("🚀 启动CT图像识别模型测试工具...")
    tester = CTModelTester()
    tester.run()


if __name__ == "__main__":
    main()
