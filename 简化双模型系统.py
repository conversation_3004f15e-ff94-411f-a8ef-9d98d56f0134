"""
简化双模型联用系统
专门针对您的两个模型：ct_other_model.h5 和 ct_class.h5
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import os
from tkinter import filedialog, messagebox
import tkinter as tk
from tkinter import ttk
import warnings

# 抑制警告
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class SimpleDualModelApp:
    """简化双模型应用"""
    
    def __init__(self):
        # 模型路径
        self.ct_detection_path = r"D:\模型开发\ct_other_model.h5"  # 判断是否为CT图像
        self.ct_classification_path = r"D:\模型开发\ct_class.h5"   # 识别CT图像症状
        
        # 模型实例
        self.ct_detection_model = None
        self.ct_classification_model = None
        
        # 症状分类标签
        self.class_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建GUI
        self.setup_gui()
        self.load_models()
    
    def setup_gui(self):
        """设置GUI界面"""
        self.root = tk.Tk()
        self.root.title("🧠 双模型AI痴呆症识别系统")
        self.root.geometry("800x600")
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🧠 双模型AI痴呆症识别系统", 
                               font=("Arial", 20, "bold"))
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="CT图像验证 → 症状智能分析", 
                                  font=("Arial", 12), foreground="blue")
        subtitle_label.pack(pady=(0, 20))
        
        # 模型状态
        status_frame = ttk.LabelFrame(main_frame, text="模型状态", padding="10")
        status_frame.pack(fill="x", pady=(0, 20))
        
        self.status1_label = ttk.Label(status_frame, text="CT识别模型: 🔴 未加载")
        self.status1_label.pack(anchor="w")
        
        self.status2_label = ttk.Label(status_frame, text="症状分析模型: 🔴 未加载")
        self.status2_label.pack(anchor="w")
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(0, 20))
        
        self.analyze_button = ttk.Button(button_frame, text="📁 选择图像进行分析", 
                                        command=self.analyze_image, state="disabled")
        self.analyze_button.pack(side="left", padx=5)
        
        ttk.Button(button_frame, text="🗑️ 清空结果", 
                  command=self.clear_results).pack(side="left", padx=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="分析结果", padding="10")
        result_frame.pack(fill="both", expand=True)
        
        self.result_text = tk.Text(result_frame, font=("Consolas", 11), wrap="word")
        scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def load_models(self):
        """加载两个模型"""
        self.log("🚀 开始加载模型...")
        
        # 加载CT识别模型
        try:
            if os.path.exists(self.ct_detection_path):
                self.ct_detection_model = tf.keras.models.load_model(self.ct_detection_path)
                self.status1_label.config(text="CT识别模型: 🟢 已加载", foreground="green")
                self.log("✅ CT识别模型加载成功")
            else:
                self.status1_label.config(text="CT识别模型: ❌ 文件不存在", foreground="red")
                self.log(f"❌ CT识别模型文件不存在: {self.ct_detection_path}")
        except Exception as e:
            self.status1_label.config(text="CT识别模型: ❌ 加载失败", foreground="red")
            self.log(f"❌ CT识别模型加载失败: {e}")
        
        # 加载症状分析模型
        try:
            if os.path.exists(self.ct_classification_path):
                self.ct_classification_model = tf.keras.models.load_model(self.ct_classification_path)
                self.status2_label.config(text="症状分析模型: 🟢 已加载", foreground="green")
                self.log("✅ 症状分析模型加载成功")
            else:
                self.status2_label.config(text="症状分析模型: ❌ 文件不存在", foreground="red")
                self.log(f"❌ 症状分析模型文件不存在: {self.ct_classification_path}")
        except Exception as e:
            self.status2_label.config(text="症状分析模型: ❌ 加载失败", foreground="red")
            self.log(f"❌ 症状分析模型加载失败: {e}")
        
        # 检查系统状态
        if self.ct_detection_model and self.ct_classification_model:
            self.analyze_button.config(state="normal")
            self.log("🎉 双模型系统就绪，可以开始分析！")
        else:
            self.log("⚠️ 系统未完全就绪，请检查模型文件")
    
    def preprocess_image(self, image_path):
        """预处理图像"""
        try:
            # 加载图像
            image = Image.open(image_path)
            
            # 转换为RGB
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整尺寸为150x150
            image = image.resize((150, 150))
            
            # 转换为数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
        except Exception as e:
            self.log(f"❌ 图像预处理失败: {e}")
            return None
    
    def analyze_image(self):
        """完整的图像分析流程"""
        # 选择图像文件
        file_path = filedialog.askopenfilename(
            title="选择要分析的图像",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if not file_path:
            return
        
        self.log(f"\n{'='*60}")
        self.log(f"🔍 开始分析图像: {os.path.basename(file_path)}")
        
        # 步骤1: 图像预处理
        self.log("📊 步骤1: 图像预处理...")
        image_array = self.preprocess_image(file_path)
        if image_array is None:
            return
        self.log("✅ 图像预处理完成")
        
        # 步骤2: CT图像检测
        self.log("\n🔍 步骤2: CT图像检测...")
        try:
            ct_prediction = self.ct_detection_model.predict(image_array, verbose=0)
            ct_confidence = float(ct_prediction[0][0])
            is_ct = ct_confidence > 0.5
            
            self.log(f"🎯 CT检测结果: {'✅ 是CT图像' if is_ct else '❌ 不是CT图像'}")
            self.log(f"📊 置信度: {ct_confidence:.4f} ({ct_confidence*100:.2f}%)")
            
            if not is_ct:
                self.log("\n⚠️ 分析终止: 输入图像不是CT图像")
                self.log("💡 建议: 请上传CT扫描图像")
                return
                
        except Exception as e:
            self.log(f"❌ CT检测失败: {e}")
            return
        
        # 步骤3: 症状分析
        self.log("\n🧠 步骤3: 痴呆症症状分析...")
        try:
            symptom_prediction = self.ct_classification_model.predict(image_array, verbose=0)
            predicted_class_idx = np.argmax(symptom_prediction, axis=1)[0]
            symptom_confidence = float(np.max(symptom_prediction))
            probabilities = symptom_prediction[0].tolist()
            
            predicted_class = self.class_labels[predicted_class_idx]
            
            self.log(f"🎯 症状分析结果:")
            self.log(f"   预测类别: {predicted_class}")
            self.log(f"   置信度: {symptom_confidence:.4f} ({symptom_confidence*100:.2f}%)")
            
            self.log(f"\n📊 详细概率分布:")
            for i, prob in enumerate(probabilities):
                self.log(f"   {self.class_labels[i]}: {prob:.4f} ({prob*100:.2f}%)")
            
            # 生成建议
            self.log(f"\n💡 分析建议:")
            if symptom_confidence > 0.8:
                self.log("   • 预测置信度较高，结果可信度良好")
            elif symptom_confidence > 0.6:
                self.log("   • 预测置信度中等，建议结合其他检查结果")
            else:
                self.log("   • 预测置信度较低，建议进行进一步检查")
            
            if 'NonDemented' in predicted_class:
                self.log("   • 建议定期进行认知功能检查")
            else:
                self.log("   • 建议咨询专业医生进行详细诊断")
            
            self.log(f"\n✅ 分析完成!")
            
        except Exception as e:
            self.log(f"❌ 症状分析失败: {e}")
    
    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
    
    def log(self, message):
        """记录日志"""
        self.result_text.insert(tk.END, message + "\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主函数"""
    print("🚀 启动简化双模型系统...")
    app = SimpleDualModelApp()
    app.run()


if __name__ == "__main__":
    main()


# 使用说明：
"""
这个简化版本专门为您的两个模型设计：

1. ct_other_model.h5 - 判断图片是否为CT图像
2. ct_class.h5 - 识别CT图像判断症状

工作流程：
1. 用户选择图像
2. 系统首先用ct_other_model.h5检测是否为CT图像
3. 如果是CT图像，继续用ct_class.h5分析症状
4. 如果不是CT图像，提示用户重新选择

特点：
- 界面简洁易用
- 自动加载两个模型
- 完整的分析流程
- 详细的结果显示
- 智能的建议生成

运行方法：
python 简化双模型系统.py
"""
