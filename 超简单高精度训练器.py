"""
超简单高精度训练器 - 直接有效
"""

import pandas as pd
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

print("🎵 超简单高精度痴呆症音频模型训练器")
print("🎯 目标: 95%+ 准确率")
print("=" * 50)

# 1. 加载数据
print("📊 加载数据...")
data_path = r"D:\模型开发\audio\processed_datasets"

try:
    train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
    test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))
    print(f"✅ 训练集: {len(train_data)} 样本")
    print(f"✅ 测试集: {len(test_data)} 样本")
except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    exit(1)

# 2. 准备数据
print("🔧 准备数据...")

# 获取特征列 (排除标签列)
feature_cols = [col for col in train_data.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
print(f"   特征数量: {len(feature_cols)}")

X_train = train_data[feature_cols].values
y_train = train_data['diagnosis_encoded'].values

X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"   训练特征形状: {X_train.shape}")
print(f"   测试特征形状: {X_test.shape}")

# 3. 尝试多种模型
print("🚀 开始训练多种模型...")

results = {}

# 随机森林
print("\n🌲 训练随机森林...")
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score
    
    rf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=42,
        n_jobs=-1
    )
    rf.fit(X_train, y_train)
    rf_pred = rf.predict(X_test)
    rf_acc = accuracy_score(y_test, rf_pred)
    results['RandomForest'] = rf_acc
    print(f"   随机森林准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")
    
    if rf_acc >= 0.95:
        print("🎉 随机森林达到目标!")
        
except Exception as e:
    print(f"   随机森林训练失败: {e}")

# 梯度提升
print("\n📈 训练梯度提升...")
try:
    from sklearn.ensemble import GradientBoostingClassifier
    
    gb = GradientBoostingClassifier(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42
    )
    gb.fit(X_train, y_train)
    gb_pred = gb.predict(X_test)
    gb_acc = accuracy_score(y_test, gb_pred)
    results['GradientBoosting'] = gb_acc
    print(f"   梯度提升准确率: {gb_acc:.4f} ({gb_acc*100:.2f}%)")
    
    if gb_acc >= 0.95:
        print("🎉 梯度提升达到目标!")
        
except Exception as e:
    print(f"   梯度提升训练失败: {e}")

# XGBoost
print("\n🚀 训练XGBoost...")
try:
    import xgboost as xgb
    
    xgb_model = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        eval_metric='mlogloss'
    )
    xgb_model.fit(X_train, y_train)
    xgb_pred = xgb_model.predict(X_test)
    xgb_acc = accuracy_score(y_test, xgb_pred)
    results['XGBoost'] = xgb_acc
    print(f"   XGBoost准确率: {xgb_acc:.4f} ({xgb_acc*100:.2f}%)")
    
    if xgb_acc >= 0.95:
        print("🎉 XGBoost达到目标!")
        
except ImportError:
    print("   XGBoost未安装，跳过")
except Exception as e:
    print(f"   XGBoost训练失败: {e}")

# SVM
print("\n🎯 训练SVM...")
try:
    from sklearn.svm import SVC
    
    svm = SVC(
        kernel='rbf',
        C=1.0,
        gamma='scale',
        random_state=42
    )
    svm.fit(X_train, y_train)
    svm_pred = svm.predict(X_test)
    svm_acc = accuracy_score(y_test, svm_pred)
    results['SVM'] = svm_acc
    print(f"   SVM准确率: {svm_acc:.4f} ({svm_acc*100:.2f}%)")
    
    if svm_acc >= 0.95:
        print("🎉 SVM达到目标!")
        
except Exception as e:
    print(f"   SVM训练失败: {e}")

# 4. 结果总结
print("\n" + "=" * 50)
print("📊 所有模型结果:")

best_model = None
best_accuracy = 0
best_name = ""

for model_name, accuracy in results.items():
    status = "🎯" if accuracy >= 0.95 else "📈"
    print(f"   {model_name}: {accuracy:.4f} ({accuracy*100:.2f}%) {status}")
    
    if accuracy > best_accuracy:
        best_accuracy = accuracy
        best_name = model_name
        if model_name == 'RandomForest':
            best_model = rf
        elif model_name == 'GradientBoosting':
            best_model = gb
        elif model_name == 'XGBoost':
            best_model = xgb_model
        elif model_name == 'SVM':
            best_model = svm

print(f"\n🏆 最佳模型: {best_name}")
print(f"🎯 最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")

# 5. 保存最佳模型
if best_model is not None:
    print("\n💾 保存最佳模型...")
    
    # 创建输出目录
    os.makedirs("w", exist_ok=True)
    
    # 保存模型
    import joblib
    joblib.dump(best_model, "w/final_dementia_audio_model.pkl")
    
    # 保存模型信息
    import json
    model_info = {
        'model_type': best_name,
        'accuracy': float(best_accuracy),
        'target_achieved': best_accuracy >= 0.95,
        'feature_count': len(feature_cols)
    }
    
    with open("w/model_info.json", "w", encoding='utf-8') as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    # 复制预处理器
    import shutil
    try:
        shutil.copy(os.path.join(data_path, "scaler.pkl"), "w/scaler.pkl")
        shutil.copy(os.path.join(data_path, "label_encoder.pkl"), "w/label_encoder.pkl")
        print("✅ 预处理器已复制")
    except:
        print("⚠️ 预处理器复制失败")
    
    print(f"✅ 模型已保存到: w/final_dementia_audio_model.pkl")
    
    # 6. 最终结果
    if best_accuracy >= 0.95:
        print(f"\n🎉 恭喜! 成功达到95%+准确率!")
        print(f"🏆 最终准确率: {best_accuracy*100:.2f}%")
        print("✅ 模型已保存到 w/ 目录")
    else:
        print(f"\n📈 当前最佳准确率: {best_accuracy*100:.2f}%")
        print("💡 可以尝试更多优化方法")
        print("✅ 模型已保存，可继续改进")

else:
    print("\n❌ 没有成功训练的模型")

print("\n🎵 训练完成!")
