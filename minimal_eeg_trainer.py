"""
🧠 极简EEG训练器
删除所有非必需内容，只保证能正常训练
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras import layers, Model
from sklearn.preprocessing import LabelEncoder
import mne
import warnings
warnings.filterwarnings('ignore')

# GPU配置
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

class SimpleEEGTrainer:
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.label_encoder = LabelEncoder()
        self.n_channels = 19
        self.n_samples = 128
        
    def load_data(self, split_name):
        """加载数据"""
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        # 读取标签
        labels_df = pd.read_csv(labels_file, sep='\t')
        
        X_data = []
        y_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找.set文件
            set_files = [f for f in os.listdir(split_dir) if f.startswith(subject_id) and f.endswith('.set')]
            
            if set_files:
                set_file = os.path.join(split_dir, set_files[0])
                try:
                    raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                    data = raw.get_data()
                    
                    # 创建epochs
                    n_timepoints = data.shape[1]
                    n_epochs = n_timepoints // self.n_samples
                    
                    for i in range(n_epochs):
                        start_idx = i * self.n_samples
                        end_idx = start_idx + self.n_samples
                        epoch = data[:, start_idx:end_idx]
                        
                        # 调整通道数
                        if epoch.shape[0] > self.n_channels:
                            epoch = epoch[:self.n_channels, :]
                        elif epoch.shape[0] < self.n_channels:
                            padded_epoch = np.zeros((self.n_channels, self.n_samples))
                            padded_epoch[:epoch.shape[0], :] = epoch
                            epoch = padded_epoch
                        
                        X_data.append(epoch)
                        y_labels.append(label)
                        
                except:
                    continue
        
        return np.array(X_data), np.array(y_labels)
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        # 标准化
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 0:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 转换为one-hot
        n_classes = len(self.label_encoder.classes_)
        y_categorical = tf.keras.utils.to_categorical(y_encoded, n_classes)
        
        # 添加通道维度
        X_processed = np.expand_dims(X_processed, axis=-1)
        
        return X_processed, y_categorical
    
    def build_model(self, n_classes):
        """构建模型"""
        inputs = layers.Input(shape=(self.n_channels, self.n_samples, 1))
        
        # 卷积层
        x = layers.Conv2D(32, (1, 7), padding='same', activation='relu')(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(32, (self.n_channels, 1), activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(64, (1, 7), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.25)(x)
        
        x = layers.Conv2D(128, (1, 7), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.25)(x)
        
        # 全局池化
        x = layers.GlobalAveragePooling2D()(x)
        
        # 全连接层
        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        x = layers.Dense(128, activation='relu')(x)
        x = layers.Dropout(0.5)(x)
        
        # 输出层
        outputs = layers.Dense(n_classes, activation='softmax')(x)
        
        model = Model(inputs=inputs, outputs=outputs)
        
        # 编译模型
        model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def train(self, epochs=100, batch_size=32):
        """训练模型"""
        print("开始训练...")
        
        # 加载数据
        X_train, y_train = self.load_data('train')
        X_val, y_val = self.load_data('val')
        
        # 预处理
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 构建模型
        n_classes = y_train.shape[1]
        self.model = self.build_model(n_classes)
        
        # 训练
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            verbose=1
        )
        
        # 保存模型
        self.model.save('simple_eeg_model.h5')
        
        # 保存标签编码器
        import joblib
        joblib.dump(self.label_encoder, 'simple_label_encoder.pkl')
        
        print("训练完成!")
        return history
    
    def evaluate(self):
        """评估模型"""
        print("评估模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_data('test')
        X_test, y_test = self.preprocess_data(X_test, y_test, fit_encoder=False)
        
        # 评估
        test_loss, test_acc = self.model.evaluate(X_test, y_test, verbose=0)
        
        print(f"测试准确率: {test_acc:.4f}")
        print(f"测试损失: {test_loss:.4f}")
        
        return test_acc, test_loss


def main():
    """主函数"""
    print("极简EEG训练器")
    
    trainer = SimpleEEGTrainer()
    
    # 训练
    history = trainer.train(epochs=100, batch_size=32)
    
    # 评估
    acc, loss = trainer.evaluate()
    
    print(f"最终结果: 准确率={acc:.4f}, 损失={loss:.4f}")


if __name__ == "__main__":
    main()
