"""
🎯 最终版 EEG数据集划分器
基于已验证的患者标签，生成完整的训练/验证/测试集划分方案
"""

import json
import os
from sklearn.model_selection import train_test_split
from collections import Counter

def load_verified_labels():
    """加载已验证的患者标签"""
    print("📋 加载已验证的患者标签...")
    
    with open('verified_patient_labels.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    patient_labels = data['patient_labels']
    label_definitions = data['label_definitions']
    
    print(f"✅ 加载了 {len(patient_labels)} 个患者标签")
    print(f"📊 标签分布:")
    for label, count in data['statistics'].items():
        description = label_definitions[label]
        print(f"   {label} ({description}): {count} 人")
    
    return patient_labels, label_definitions

def create_stratified_split(patient_labels):
    """创建分层划分"""
    print(f"\n🔄 创建分层数据划分...")
    
    # 准备数据
    subject_ids = list(patient_labels.keys())
    labels = list(patient_labels.values())
    
    # 划分比例 (针对双模型联用优化)
    train_ratio = 0.70
    val_ratio = 0.15
    test_ratio = 0.15
    
    print(f"📊 划分比例: 训练{train_ratio*100:.0f}% | 验证{val_ratio*100:.0f}% | 测试{test_ratio*100:.0f}%")
    
    # 第一次划分: 分出测试集
    train_val_ids, test_ids, train_val_labels, test_labels = train_test_split(
        subject_ids, labels,
        test_size=test_ratio,
        random_state=42,
        stratify=labels
    )
    
    # 第二次划分: 从训练+验证中分出验证集
    val_ratio_adjusted = val_ratio / (train_ratio + val_ratio)
    train_ids, val_ids, train_labels, val_labels = train_test_split(
        train_val_ids, train_val_labels,
        test_size=val_ratio_adjusted,
        random_state=42,
        stratify=train_val_labels
    )
    
    # 保存划分结果
    split_result = {
        'train': {
            'subject_ids': train_ids,
            'labels': train_labels,
            'count': len(train_ids)
        },
        'val': {
            'subject_ids': val_ids,
            'labels': val_labels,
            'count': len(val_ids)
        },
        'test': {
            'subject_ids': test_ids,
            'labels': test_labels,
            'count': len(test_ids)
        }
    }
    
    return split_result

def display_split_results(split_result, label_definitions):
    """显示划分结果"""
    print(f"\n✅ 数据划分完成!")
    print("=" * 60)
    
    class_names = {
        'A': '阿尔茨海默病',
        'C': '健康对照',
        'F': '额颞叶痴呆'
    }
    
    total_patients = sum(split_data['count'] for split_data in split_result.values())
    
    for split_name, split_data in split_result.items():
        print(f"\n📋 {split_name.upper()}集 ({split_data['count']} 人, {split_data['count']/total_patients*100:.1f}%):")
        
        # 统计各类别数量
        label_counts = Counter(split_data['labels'])
        for label, count in sorted(label_counts.items()):
            class_name = class_names.get(label, label)
            percentage = count / split_data['count'] * 100
            print(f"   {label} ({class_name}): {count} 人 ({percentage:.1f}%)")
        
        # 显示具体的患者ID范围
        subject_nums = [int(sid.split('-')[1]) for sid in split_data['subject_ids']]
        subject_nums.sort()
        print(f"   患者范围: sub-{subject_nums[0]:03d} 到 sub-{subject_nums[-1]:03d}")
        
        # 显示前10个患者ID
        sorted_subjects = sorted(split_data['subject_ids'])
        print(f"   患者示例: {', '.join(sorted_subjects[:10])}")
        if len(sorted_subjects) > 10:
            print(f"   ... 还有 {len(sorted_subjects)-10} 个")

def save_split_files(split_result, label_definitions):
    """保存划分文件"""
    print(f"\n💾 保存划分文件...")
    
    # 创建输出目录
    output_dir = "EEG_splits"
    os.makedirs(output_dir, exist_ok=True)
    
    # 为每个划分保存文件
    for split_name, split_data in split_result.items():
        split_dir = os.path.join(output_dir, split_name)
        os.makedirs(split_dir, exist_ok=True)
        
        # 保存患者列表文件
        patient_list_file = os.path.join(split_dir, "patient_list.txt")
        with open(patient_list_file, 'w', encoding='utf-8') as f:
            f.write("subject_id\tlabel\tlabel_name\n")
            for subject_id, label in zip(split_data['subject_ids'], split_data['labels']):
                label_name = label_definitions.get(label, label)
                f.write(f"{subject_id}\t{label}\t{label_name}\n")
        
        print(f"✅ {split_name.upper()}集患者列表: {patient_list_file}")
    
    # 保存完整的划分元数据
    metadata = {
        'dataset_info': {
            'total_patients': sum(split_data['count'] for split_data in split_result.values()),
            'split_ratios': {
                'train': 0.70,
                'val': 0.15,
                'test': 0.15
            }
        },
        'label_definitions': label_definitions,
        'split_result': split_result
    }
    
    metadata_file = os.path.join(output_dir, "split_metadata.json")
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 划分元数据: {metadata_file}")

def generate_training_script_template(split_result):
    """生成训练脚本模板"""
    print(f"\n📝 生成训练脚本模板...")
    
    template = f'''"""
🧠 EEG模型训练脚本模板
基于划分好的数据集进行训练
"""

import os
import numpy as np
from sklearn.model_selection import train_test_split

# 数据集路径配置
TRAIN_PATIENTS = {split_result['train']['subject_ids']}
VAL_PATIENTS = {split_result['val']['subject_ids']}
TEST_PATIENTS = {split_result['test']['subject_ids']}

# 标签映射
LABEL_MAPPING = {{
    'A': 1,  # 阿尔茨海默病
    'C': 0,  # 健康对照
    'F': 2   # 额颞叶痴呆
}}

TRAIN_LABELS = {split_result['train']['labels']}
VAL_LABELS = {split_result['val']['labels']}
TEST_LABELS = {split_result['test']['labels']}

def load_eeg_data(patient_ids, labels):
    """
    加载EEG数据的函数模板
    您需要根据实际的EEG文件格式实现这个函数
    """
    # TODO: 实现EEG数据加载逻辑
    # 示例:
    # features = []
    # for patient_id in patient_ids:
    #     eeg_file = f"path/to/{{patient_id}}_task-eyesclosed_eeg.set"
    #     feature = extract_eeg_features(eeg_file)
    #     features.append(feature)
    # return np.array(features), np.array([LABEL_MAPPING[l] for l in labels])
    pass

def train_model():
    """训练模型"""
    print("🚀 开始训练EEG模型...")
    
    # 加载数据
    X_train, y_train = load_eeg_data(TRAIN_PATIENTS, TRAIN_LABELS)
    X_val, y_val = load_eeg_data(VAL_PATIENTS, VAL_LABELS)
    X_test, y_test = load_eeg_data(TEST_PATIENTS, TEST_LABELS)
    
    print(f"训练集: {{len(X_train)}} 样本")
    print(f"验证集: {{len(X_val)}} 样本")
    print(f"测试集: {{len(X_test)}} 样本")
    
    # TODO: 实现模型训练逻辑
    
if __name__ == "__main__":
    train_model()
'''
    
    template_file = "EEG_splits/train_eeg_template.py"
    with open(template_file, 'w', encoding='utf-8') as f:
        f.write(template)
    
    print(f"✅ 训练脚本模板: {template_file}")

def generate_summary_report(split_result, label_definitions):
    """生成总结报告"""
    print(f"\n📊 生成总结报告...")
    
    report_file = "EEG_splits/split_summary.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("EEG数据集划分总结报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("🎯 针对双模型联用的优化划分方案\n\n")
        
        f.write("📊 划分结果:\n")
        total_patients = sum(split_data['count'] for split_data in split_result.values())
        
        for split_name, split_data in split_result.items():
            percentage = split_data['count'] / total_patients * 100
            f.write(f"  {split_name.upper()}集: {split_data['count']} 人 ({percentage:.1f}%)\n")
            
            label_counts = Counter(split_data['labels'])
            for label, count in sorted(label_counts.items()):
                label_name = label_definitions.get(label, label)
                f.write(f"    {label} ({label_name}): {count} 人\n")
            f.write("\n")
        
        f.write("🔗 双模型联用集成建议:\n")
        f.write("  1. EEG模型: 3分类 (健康/AD/FTD)\n")
        f.write("  2. 保持现有MRI模型: 4分类 (无痴呆/轻度/中度/非常轻度)\n")
        f.write("  3. 标签映射: MRI的痴呆类别统一映射到AD\n")
        f.write("  4. 多模态融合: 加权平均或投票机制\n")
        f.write("  5. 验证策略: 单模态+多模态双重验证\n\n")
        
        f.write("📁 文件说明:\n")
        f.write("  train/patient_list.txt - 训练集患者列表\n")
        f.write("  val/patient_list.txt - 验证集患者列表\n")
        f.write("  test/patient_list.txt - 测试集患者列表\n")
        f.write("  split_metadata.json - 完整划分元数据\n")
        f.write("  train_eeg_template.py - 训练脚本模板\n")
    
    print(f"✅ 总结报告: {report_file}")

def main():
    """主函数"""
    print("🎯 最终版 EEG数据集划分器")
    print("=" * 50)
    print("基于已验证的88个患者标签进行优化划分")
    print()
    
    # 1. 加载已验证的标签
    patient_labels, label_definitions = load_verified_labels()
    
    # 2. 创建分层划分
    split_result = create_stratified_split(patient_labels)
    
    # 3. 显示划分结果
    display_split_results(split_result, label_definitions)
    
    # 4. 保存划分文件
    save_split_files(split_result, label_definitions)
    
    # 5. 生成训练脚本模板
    generate_training_script_template(split_result)
    
    # 6. 生成总结报告
    generate_summary_report(split_result, label_definitions)
    
    print(f"\n🎉 EEG数据集划分完成!")
    print("=" * 60)
    print(f"📁 输出目录: EEG_splits/")
    print(f"📋 包含:")
    print(f"   - train/patient_list.txt (训练集患者列表)")
    print(f"   - val/patient_list.txt (验证集患者列表)")
    print(f"   - test/patient_list.txt (测试集患者列表)")
    print(f"   - split_metadata.json (完整元数据)")
    print(f"   - train_eeg_template.py (训练脚本模板)")
    print(f"   - split_summary.txt (总结报告)")
    print(f"\n📋 下一步: 根据患者列表加载实际的EEG文件进行训练")
    print("=" * 60)

if __name__ == "__main__":
    main()
