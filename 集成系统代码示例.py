"""
AI痴呆症识别集成系统
整合CT图像识别和痴呆症分类两个模型的完整解决方案
"""

import tensorflow as tf
import numpy as np
from PIL import Image
import cv2
import os
import json
from datetime import datetime
import logging
from typing import Dict, Tuple, Optional, List
import warnings

# 抑制TensorFlow警告
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class IntegratedDementiaDetectionSystem:
    """
    集成的痴呆症检测系统
    结合CT图像识别和痴呆症分类功能
    """
    
    def __init__(self, ct_model_path: str, dementia_model_path: str):
        """
        初始化集成系统
        
        Args:
            ct_model_path: CT图像识别模型路径
            dementia_model_path: 痴呆症分类模型路径
        """
        self.ct_model_path = ct_model_path
        self.dementia_model_path = dementia_model_path
        
        # 模型实例
        self.ct_model = None
        self.dementia_model = None
        
        # 类别标签
        self.dementia_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 配置日志
        self.setup_logging()
        
        # 加载模型
        self.load_models()
    
    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('dementia_detection.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def load_models(self):
        """加载AI模型"""
        try:
            self.logger.info("🤖 开始加载AI模型...")
            
            # 加载CT识别模型
            if os.path.exists(self.ct_model_path):
                self.ct_model = tf.keras.models.load_model(self.ct_model_path)
                self.logger.info("✅ CT识别模型加载成功")
            else:
                self.logger.error(f"❌ CT模型文件不存在: {self.ct_model_path}")
                raise FileNotFoundError(f"CT模型文件不存在: {self.ct_model_path}")
            
            # 加载痴呆症分类模型
            if os.path.exists(self.dementia_model_path):
                self.dementia_model = tf.keras.models.load_model(self.dementia_model_path)
                self.logger.info("✅ 痴呆症分类模型加载成功")
            else:
                self.logger.error(f"❌ 痴呆症模型文件不存在: {self.dementia_model_path}")
                raise FileNotFoundError(f"痴呆症模型文件不存在: {self.dementia_model_path}")
            
            self.logger.info("🎉 所有模型加载完成")
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def preprocess_image(self, image_path: str) -> Optional[np.ndarray]:
        """
        图像预处理
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            预处理后的图像数组，失败返回None
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(image_path):
                self.logger.error(f"图像文件不存在: {image_path}")
                return None
            
            # 加载图像
            image = Image.open(image_path)
            
            # 转换为RGB格式（如果需要）
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整尺寸为150x150（与训练时一致）
            image = image.resize((150, 150))
            
            # 转换为numpy数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0
            
            # 添加批次维度
            image_array = np.expand_dims(image_array, axis=0)
            
            self.logger.info(f"✅ 图像预处理完成: {image_path}")
            return image_array
            
        except Exception as e:
            self.logger.error(f"❌ 图像预处理失败: {e}")
            return None
    
    def validate_ct_image(self, image_array: np.ndarray, threshold: float = 0.5) -> Dict:
        """
        验证是否为CT图像
        
        Args:
            image_array: 预处理后的图像数组
            threshold: CT识别阈值
            
        Returns:
            验证结果字典
        """
        try:
            # 使用CT识别模型进行预测
            ct_prediction = self.ct_model.predict(image_array, verbose=0)
            ct_confidence = float(ct_prediction[0][0])
            is_ct = ct_confidence > threshold
            
            result = {
                'is_ct': is_ct,
                'confidence': ct_confidence,
                'threshold': threshold,
                'status': 'success'
            }
            
            if is_ct:
                self.logger.info(f"✅ CT图像验证通过，置信度: {ct_confidence:.4f}")
            else:
                self.logger.warning(f"⚠️ 非CT图像，置信度: {ct_confidence:.4f}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ CT图像验证失败: {e}")
            return {
                'is_ct': False,
                'confidence': 0.0,
                'threshold': threshold,
                'status': 'error',
                'error': str(e)
            }
    
    def classify_dementia(self, image_array: np.ndarray) -> Dict:
        """
        痴呆症分类
        
        Args:
            image_array: 预处理后的图像数组
            
        Returns:
            分类结果字典
        """
        try:
            # 使用痴呆症分类模型进行预测
            dementia_prediction = self.dementia_model.predict(image_array, verbose=0)
            
            # 获取预测结果
            predicted_class_idx = np.argmax(dementia_prediction, axis=1)[0]
            predicted_class_name = self.dementia_labels[predicted_class_idx]
            confidence = float(np.max(dementia_prediction))
            probabilities = dementia_prediction[0].tolist()
            
            # 构建详细的概率分布
            probability_details = {}
            for i, label in enumerate(self.dementia_labels):
                probability_details[label] = {
                    'probability': float(probabilities[i]),
                    'percentage': f"{probabilities[i]*100:.2f}%"
                }
            
            result = {
                'predicted_class': predicted_class_name,
                'predicted_class_index': int(predicted_class_idx),
                'confidence': confidence,
                'confidence_percentage': f"{confidence*100:.2f}%",
                'probabilities': probabilities,
                'probability_details': probability_details,
                'status': 'success'
            }
            
            self.logger.info(f"✅ 痴呆症分类完成: {predicted_class_name}, 置信度: {confidence:.4f}")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 痴呆症分类失败: {e}")
            return {
                'predicted_class': None,
                'confidence': 0.0,
                'status': 'error',
                'error': str(e)
            }
    
    def analyze_image(self, image_path: str, ct_threshold: float = 0.5) -> Dict:
        """
        完整的图像分析流程
        
        Args:
            image_path: 图像文件路径
            ct_threshold: CT识别阈值
            
        Returns:
            完整的分析结果
        """
        analysis_start_time = datetime.now()
        
        result = {
            'image_path': image_path,
            'timestamp': analysis_start_time.isoformat(),
            'ct_validation': None,
            'dementia_classification': None,
            'overall_status': 'processing',
            'processing_time': None,
            'recommendations': []
        }
        
        try:
            self.logger.info(f"🔍 开始分析图像: {image_path}")
            
            # 步骤1: 图像预处理
            image_array = self.preprocess_image(image_path)
            if image_array is None:
                result['overall_status'] = 'failed'
                result['error'] = '图像预处理失败'
                return result
            
            # 步骤2: CT图像验证
            ct_result = self.validate_ct_image(image_array, ct_threshold)
            result['ct_validation'] = ct_result
            
            if not ct_result['is_ct']:
                result['overall_status'] = 'rejected'
                result['error'] = '输入图像不是CT图像'
                result['recommendations'] = [
                    "请确保上传的是CT扫描图像",
                    "检查图像质量和清晰度",
                    "如果确认是CT图像，可以尝试调整识别阈值"
                ]
                return result
            
            # 步骤3: 痴呆症分类
            dementia_result = self.classify_dementia(image_array)
            result['dementia_classification'] = dementia_result
            
            if dementia_result['status'] == 'success':
                result['overall_status'] = 'completed'
                
                # 添加建议
                confidence = dementia_result['confidence']
                predicted_class = dementia_result['predicted_class']
                
                if confidence > 0.8:
                    result['recommendations'].append("预测置信度较高，结果可信度良好")
                elif confidence > 0.6:
                    result['recommendations'].append("预测置信度中等，建议结合其他检查结果")
                else:
                    result['recommendations'].append("预测置信度较低，建议进行进一步检查")
                
                if 'NonDemented' in predicted_class:
                    result['recommendations'].append("建议定期进行认知功能检查")
                else:
                    result['recommendations'].append("建议咨询专业医生进行详细诊断")
            else:
                result['overall_status'] = 'failed'
                result['error'] = dementia_result.get('error', '痴呆症分类失败')
            
        except Exception as e:
            self.logger.error(f"❌ 图像分析过程出错: {e}")
            result['overall_status'] = 'failed'
            result['error'] = str(e)
        
        finally:
            # 计算处理时间
            processing_time = (datetime.now() - analysis_start_time).total_seconds()
            result['processing_time'] = f"{processing_time:.2f}秒"
            
            self.logger.info(f"🏁 图像分析完成，耗时: {processing_time:.2f}秒")
        
        return result
    
    def batch_analyze(self, image_paths: List[str], ct_threshold: float = 0.5) -> List[Dict]:
        """
        批量分析多张图像
        
        Args:
            image_paths: 图像文件路径列表
            ct_threshold: CT识别阈值
            
        Returns:
            分析结果列表
        """
        self.logger.info(f"📊 开始批量分析 {len(image_paths)} 张图像")
        
        results = []
        for i, image_path in enumerate(image_paths, 1):
            self.logger.info(f"处理第 {i}/{len(image_paths)} 张图像")
            result = self.analyze_image(image_path, ct_threshold)
            results.append(result)
        
        self.logger.info("✅ 批量分析完成")
        return results
    
    def save_results(self, results: Dict, output_path: str):
        """
        保存分析结果到JSON文件
        
        Args:
            results: 分析结果
            output_path: 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"✅ 结果已保存到: {output_path}")
        except Exception as e:
            self.logger.error(f"❌ 保存结果失败: {e}")
    
    def print_analysis_summary(self, result: Dict):
        """
        打印分析结果摘要
        
        Args:
            result: 分析结果字典
        """
        print("\n" + "="*60)
        print("🧠 AI痴呆症识别系统 - 分析报告")
        print("="*60)
        
        print(f"📁 图像路径: {result['image_path']}")
        print(f"⏰ 分析时间: {result['timestamp']}")
        print(f"⚡ 处理耗时: {result.get('processing_time', 'N/A')}")
        print(f"📊 整体状态: {result['overall_status']}")
        
        # CT验证结果
        if result['ct_validation']:
            ct_result = result['ct_validation']
            print(f"\n🔍 CT图像验证:")
            print(f"   结果: {'✅ 通过' if ct_result['is_ct'] else '❌ 未通过'}")
            print(f"   置信度: {ct_result['confidence']:.4f} ({ct_result['confidence']*100:.2f}%)")
        
        # 痴呆症分类结果
        if result['dementia_classification'] and result['dementia_classification']['status'] == 'success':
            dementia_result = result['dementia_classification']
            print(f"\n🎯 痴呆症分类结果:")
            print(f"   预测类别: {dementia_result['predicted_class']}")
            print(f"   置信度: {dementia_result['confidence_percentage']}")
            
            print(f"\n📈 详细概率分布:")
            for label, details in dementia_result['probability_details'].items():
                print(f"   {label}: {details['percentage']}")
        
        # 建议
        if result['recommendations']:
            print(f"\n💡 建议:")
            for i, recommendation in enumerate(result['recommendations'], 1):
                print(f"   {i}. {recommendation}")
        
        # 错误信息
        if result.get('error'):
            print(f"\n❌ 错误: {result['error']}")
        
        print("="*60)


def main():
    """主函数 - 演示系统使用"""
    
    # 模型路径配置
    CT_MODEL_PATH = "models/best_ct_detection_model.h5"
    DEMENTIA_MODEL_PATH = "models/dementia_classification_model.h5"
    
    try:
        # 初始化集成系统
        print("🚀 初始化AI痴呆症识别集成系统...")
        system = IntegratedDementiaDetectionSystem(CT_MODEL_PATH, DEMENTIA_MODEL_PATH)
        
        # 示例：分析单张图像
        image_path = "test_images/sample_ct.jpg"  # 替换为实际图像路径
        
        if os.path.exists(image_path):
            print(f"\n📸 分析图像: {image_path}")
            result = system.analyze_image(image_path)
            
            # 显示结果
            system.print_analysis_summary(result)
            
            # 保存结果
            output_file = f"analysis_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            system.save_results(result, output_file)
        else:
            print(f"❌ 测试图像不存在: {image_path}")
            print("💡 请将测试图像放在指定路径，或修改 image_path 变量")
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        print("💡 请检查模型文件路径是否正确")


if __name__ == "__main__":
    main()


# 使用示例和测试代码
"""
使用示例：

1. 基本使用：
   system = IntegratedDementiaDetectionSystem(
       ct_model_path="models/ct_model.h5",
       dementia_model_path="models/dementia_model.h5"
   )
   result = system.analyze_image("path/to/ct_image.jpg")

2. 批量处理：
   image_paths = ["image1.jpg", "image2.jpg", "image3.jpg"]
   results = system.batch_analyze(image_paths)

3. 自定义阈值：
   result = system.analyze_image("image.jpg", ct_threshold=0.7)

4. 保存结果：
   system.save_results(result, "analysis_report.json")

输出结果格式：
{
    "image_path": "test.jpg",
    "timestamp": "2024-01-01T12:00:00",
    "ct_validation": {
        "is_ct": true,
        "confidence": 0.95,
        "threshold": 0.5,
        "status": "success"
    },
    "dementia_classification": {
        "predicted_class": "NonDemented(无痴呆)",
        "confidence": 0.87,
        "confidence_percentage": "87.00%",
        "probabilities": [0.05, 0.03, 0.87, 0.05],
        "probability_details": {
            "MildDemented(轻度痴呆)": {"probability": 0.05, "percentage": "5.00%"},
            "ModerateDemented(中度痴呆)": {"probability": 0.03, "percentage": "3.00%"},
            "NonDemented(无痴呆)": {"probability": 0.87, "percentage": "87.00%"},
            "VeryMildDemented(非常轻度痴呆)": {"probability": 0.05, "percentage": "5.00%"}
        },
        "status": "success"
    },
    "overall_status": "completed",
    "processing_time": "2.34秒",
    "recommendations": [
        "预测置信度较高，结果可信度良好",
        "建议定期进行认知功能检查"
    ]
}
"""
