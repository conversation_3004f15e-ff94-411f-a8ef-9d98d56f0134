# -*- coding: utf-8 -*-
"""
最终双模型症状分析系统
重点：CT图像的4种症状分析（不是判断是否为CT图像）
HTML功能：完全照搬现有GUI的HTML生成功能
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np
from fpdf import FPDF
import warnings

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 抑制警告
warnings.filterwarnings('ignore')

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CTSymptomAnalysisSystem:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🧠 CT症状分析系统 - 双模型智能版 v2.0")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.ct_validation_model = None     # CT验证模型（辅助）
        self.symptom_analysis_model = None  # 症状分析模型（核心）
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        
        # 模型路径
        self.ct_validation_path = r"D:\模型开发\ct_other_model.h5"  # CT验证（辅助）
        self.symptom_analysis_path = r"D:\模型开发\ct_class.h5"     # 症状分析（核心）
        
        # 症状分类标签（核心功能 - 4种症状）
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_models_async()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 CT症状分析系统",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="专业CT图像痴呆症症状智能分析 | 四种症状精准识别",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果（增加宽度，优化布局）
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=420)  # 增加宽度，避免文字遮挡
        
        self.create_left_panel()
        self.create_right_panel()
        
        # 底部状态栏
        self.create_status_bar()
        
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        # 图像显示标题
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 CT图像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 默认显示
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择图像进行症状分析\n\n支持格式: JPG, PNG, BMP\n\n• CT图像：顺利分析\n• 非CT图像：验证警告但继续分析",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
        
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 模型状态显示
        self.create_model_status_panel()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 症状分析结果显示区域（核心）
        self.create_symptom_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
        
    def create_model_status_panel(self):
        """创建模型状态面板"""
        status_frame = ctk.CTkFrame(self.right_panel)
        status_frame.pack(fill="x", padx=20, pady=20)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="🤖 双模型状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 10))
        
        # 症状分析模型状态（核心 - 重点显示）
        self.symptom_analysis_status = ctk.CTkLabel(
            status_frame,
            text="症状分析模型: 🔴 未加载",
            font=ctk.CTkFont(size=13, weight="bold")
        )
        self.symptom_analysis_status.pack(pady=3)
        
        # CT验证模型状态（辅助 - 小字显示）
        self.ct_validation_status = ctk.CTkLabel(
            status_frame,
            text="CT验证模型: 🔴 未加载",
            font=ctk.CTkFont(size=10)
        )
        self.ct_validation_status.pack(pady=1)
        
        # 系统整体状态
        self.system_status = ctk.CTkLabel(
            status_frame,
            text="系统状态: ⏳ 初始化中...",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.system_status.pack(pady=(10, 15))
        
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # 选择CT图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择CT图像",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 开始症状分析按钮（核心功能 - 重点突出）
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🧠 分析症状类型",
            font=ctk.CTkFont(size=15, weight="bold"),
            height=45,
            command=self.start_symptom_analysis,
            state="disabled",
            fg_color="red"
        )
        self.analyze_btn.pack(fill="x", pady=10)

        # 摄像头控制按钮
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=10)

        # 拍照分析按钮
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="📸 拍照分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=10)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(control_frame)
        self.progress.pack(fill="x", pady=10)
        self.progress.set(0)
        
    def create_symptom_results_panel(self):
        """创建症状分析结果显示面板（核心）"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # 结果标题
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 症状分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 8))

        # 症状分析结果容器（避免文字遮挡）
        self.symptom_result_frame = ctk.CTkFrame(results_frame)
        self.symptom_result_frame.pack(fill="x", padx=10, pady=5)

        # 症状分析结果（核心显示 - 重点突出，增加高度）
        self.symptom_result_label = ctk.CTkLabel(
            self.symptom_result_frame,
            text="症状类型: 等待分析...",
            font=ctk.CTkFont(size=15, weight="bold"),
            wraplength=350,
            justify="center",
            height=60  # 固定高度，避免遮挡
        )
        self.symptom_result_label.pack(pady=10, padx=10, fill="x")

        # 置信度显示容器
        self.confidence_frame = ctk.CTkFrame(results_frame)
        self.confidence_frame.pack(fill="x", padx=10, pady=3)

        self.confidence_label = ctk.CTkLabel(
            self.confidence_frame,
            text="",
            font=ctk.CTkFont(size=12),
            height=25  # 固定高度
        )
        self.confidence_label.pack(pady=5, padx=10)

        # 详细概率显示框（4种症状）
        prob_title = ctk.CTkLabel(
            results_frame,
            text="4种症状概率分布:",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        prob_title.pack(pady=(8, 3))

        # 增加概率显示框高度，确保4种症状都能完整显示
        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=140)
        self.details_frame.pack(fill="x", padx=10, pady=5)

        # CT验证结果容器（辅助信息，小字显示在底部）
        self.ct_validation_frame = ctk.CTkFrame(results_frame)
        self.ct_validation_frame.pack(fill="x", padx=10, pady=3)

        self.ct_validation_label = ctk.CTkLabel(
            self.ct_validation_frame,
            text="CT验证: 等待验证...",
            font=ctk.CTkFont(size=9),
            wraplength=350,
            text_color="gray",
            height=20  # 固定高度
        )
        self.ct_validation_label.pack(pady=3, padx=10)
        
    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=20, pady=10)
        
        # 保存结果按钮
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled"
        )
        self.save_btn.pack(fill="x", pady=3)

        # 生成PDF报告按钮
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="orange"
        )
        self.pdf_btn.pack(fill="x", pady=3)

        # 生成HTML报告按钮（完全照搬现有功能）
        self.html_btn = ctk.CTkButton(
            func_frame,
            text="🌐 生成HTML报告",
            command=self.generate_html_report,
            state="disabled",
            fg_color="purple"
        )
        self.html_btn.pack(fill="x", pady=3)
        
        # 查看历史按钮
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history
        )
        self.history_btn.pack(fill="x", pady=3)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about
        )
        self.about_btn.pack(fill="x", pady=3)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载症状分析系统...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # 版本信息
        self.version_label = ctk.CTkLabel(
            self.status_frame,
            text="v2.0 症状分析专业版",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.version_label.pack(side="right", padx=20, pady=10)

    def load_models_async(self):
        """异步加载双模型"""
        def load_models():
            try:
                import tensorflow as tf
                from tensorflow.keras.preprocessing import image
                import numpy as np

                # 设置TensorFlow日志级别
                tf.get_logger().setLevel('ERROR')

                try:
                    import absl.logging
                    absl.logging.set_verbosity(absl.logging.ERROR)
                except:
                    pass

                # 加载症状分析模型（核心 - 优先加载）
                if os.path.exists(self.symptom_analysis_path):
                    self.symptom_analysis_model = tf.keras.models.load_model(self.symptom_analysis_path)
                    self.tf = tf
                    self.image_module = image
                    self.np = np
                    self.root.after(0, self.update_symptom_analysis_status, True)
                else:
                    self.root.after(0, self.update_symptom_analysis_status, False)

                # 加载CT验证模型（辅助）
                if os.path.exists(self.ct_validation_path):
                    self.ct_validation_model = tf.keras.models.load_model(self.ct_validation_path)
                    self.root.after(0, self.update_ct_validation_status, True)
                else:
                    self.root.after(0, self.update_ct_validation_status, False)

                # 检查系统状态（只要症状分析模型加载成功即可）
                if self.symptom_analysis_model:
                    self.root.after(0, self.on_system_ready)
                else:
                    self.root.after(0, self.on_system_error)

            except Exception as e:
                self.root.after(0, self.on_model_error, str(e))

        # 在后台线程中加载模型
        threading.Thread(target=load_models, daemon=True).start()

    def update_symptom_analysis_status(self, success):
        """更新症状分析模型状态（核心）"""
        if success:
            self.symptom_analysis_status.configure(text="症状分析模型: 🟢 已加载", text_color="green")
        else:
            self.symptom_analysis_status.configure(text="症状分析模型: 🔴 加载失败", text_color="red")

    def update_ct_validation_status(self, success):
        """更新CT验证模型状态（辅助）"""
        if success:
            self.ct_validation_status.configure(text="CT验证模型: 🟢 已加载", text_color="green")
        else:
            self.ct_validation_status.configure(text="CT验证模型: 🔴 加载失败", text_color="orange")

    def on_system_ready(self):
        """系统就绪回调"""
        self.system_status.configure(text="系统状态: ✅ 症状分析就绪", text_color="green")
        self.status_label.configure(text="✅ CT症状分析系统已就绪 - 可以分析4种症状类型")

    def on_system_error(self):
        """系统错误回调"""
        self.system_status.configure(text="系统状态: ❌ 系统错误", text_color="red")
        self.status_label.configure(text="❌ 症状分析系统未就绪")
        messagebox.showerror("错误", "症状分析模型加载失败，请检查模型文件路径")

    def on_model_error(self, error):
        """模型加载错误回调"""
        self.system_status.configure(text="系统状态: ❌ 加载失败", text_color="red")
        self.status_label.configure(text="❌ 模型加载失败")
        messagebox.showerror("错误", f"模型加载失败：{error}")

    def select_image(self):
        """选择CT图像文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择CT图像文件",
            filetypes=file_types
        )

        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"📁 已选择CT图像: {os.path.basename(file_path)}")

    def display_image(self, image_path):
        """显示选择的图像"""
        try:
            # 加载并调整图像大小
            pil_image = Image.open(image_path)

            # 计算合适的显示尺寸
            display_size = (450, 450)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(pil_image)

            # 更新显示
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用

        except Exception as e:
            messagebox.showerror("错误", f"无法加载图像：{e}")

    def start_symptom_analysis(self):
        """开始症状分析（核心功能）"""
        if not self.symptom_analysis_model:
            messagebox.showerror("错误", "症状分析模型未加载")
            return

        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择CT图像文件")
            return

        # 禁用按钮，显示进度
        self.analyze_btn.configure(state="disabled", text="🔄 症状分析中...")
        self.progress.set(0)

        # 在后台线程中进行分析
        threading.Thread(target=self.perform_symptom_analysis, daemon=True).start()

    def perform_symptom_analysis(self):
        """执行症状分析（核心功能）"""
        try:
            # 步骤1: 图像预处理
            self.root.after(0, lambda: self.progress.set(0.2))
            self.root.after(0, self.update_status, "📊 步骤1: 图像预处理...")

            # 加载和预处理图像
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            # 步骤2: CT图像验证（如果有验证模型 - 辅助功能）
            ct_validation_result = None
            if self.ct_validation_model:
                self.root.after(0, lambda: self.progress.set(0.4))
                self.root.after(0, self.update_status, "🔍 步骤2: CT图像验证...")

                ct_prediction = self.ct_validation_model.predict(img_array, verbose=0)
                ct_confidence = float(ct_prediction[0][0])
                is_ct = ct_confidence > 0.5

                ct_validation_result = {
                    'is_ct': is_ct,
                    'confidence': ct_confidence
                }

                # 更新CT验证结果（辅助信息，优化显示）
                ct_result_text = f"{'✅ CT图像' if is_ct else '⚠️ 非CT'} ({ct_confidence:.1%})"
                self.root.after(0, lambda: self.ct_validation_label.configure(
                    text=f"验证: {ct_result_text}",  # 简化文本
                    text_color="green" if is_ct else "red"
                ))

                # 对于非CT图像，显示验证警告但继续分析
                if not is_ct:
                    self.root.after(0, lambda: messagebox.showwarning(
                        "验证警告",
                        f"⚠️ 检测到非CT图像！\n\n"
                        f"验证置信度: {ct_confidence:.2%}\n\n"
                        "系统将继续进行症状分析，但结果可能不准确。\n"
                        "建议使用标准CT图像以获得最佳分析效果。\n\n"
                        "点击确定继续分析..."
                    ))

            # 步骤3: 症状分析（核心功能 - 重点）
            self.root.after(0, lambda: self.progress.set(0.7))
            self.root.after(0, self.update_status, "🧠 步骤3: 痴呆症症状分析...")

            symptom_prediction = self.symptom_analysis_model.predict(img_array, verbose=0)
            predicted_class = self.np.argmax(symptom_prediction, axis=1)
            prediction_probs = symptom_prediction[0].tolist()
            symptom_confidence = max(prediction_probs)

            self.root.after(0, lambda: self.progress.set(0.9))

            # 准备完整结果
            result_data = {
                'image_path': self.current_image_path,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'ct_validation': ct_validation_result,
                'predicted_class': predicted_class[0],
                'predicted_class_name': self.class_labels[predicted_class[0]],
                'confidence': symptom_confidence,
                'probabilities': prediction_probs,
                'analysis_completed': True
            }

            self.root.after(0, lambda: self.progress.set(1.0))
            self.root.after(0, self.display_symptom_results, result_data)

        except Exception as e:
            self.root.after(0, self.on_analysis_error, str(e))

    def display_symptom_results(self, result_data):
        """显示症状分析结果（核心功能）"""
        # 保存到历史记录
        self.results_history.append(result_data)

        # 显示症状分析结果（核心显示 - 重点突出）
        class_name = result_data['predicted_class_name']
        confidence = result_data['confidence']

        # 根据症状类型设置颜色和风险等级
        if "NonDemented" in class_name:
            color = "green"
            risk_level = "低风险 ✅"
        elif "VeryMild" in class_name:
            color = "orange"
            risk_level = "轻微风险 ⚠️"
        elif "Mild" in class_name:
            color = "red"
            risk_level = "中等风险 ⚠️"
        else:  # ModerateDemented
            color = "darkred"
            risk_level = "高风险 🚨"

        # 优化文字显示，避免遮挡
        # 将长文本分为两行显示，确保完整可见
        symptom_type = class_name.split('(')[0] if '(' in class_name else class_name
        result_text = f"🎯 症状: {symptom_type}\n风险: {risk_level}"

        self.symptom_result_label.configure(
            text=result_text,
            text_color=color,
            height=60  # 确保有足够高度显示两行文字
        )

        self.confidence_label.configure(
            text=f"🎯 置信度: {confidence:.2%}",
            text_color="lightblue"
        )

        # 清空详细结果框
        for widget in self.details_frame.winfo_children():
            widget.destroy()

        # 显示4种症状的详细概率（优化布局，避免遮挡）
        for i, prob in enumerate(result_data['probabilities']):
            prob_frame = ctk.CTkFrame(self.details_frame)
            prob_frame.pack(fill="x", pady=3, padx=5)  # 增加间距

            # 简化标签文本，避免过长
            label_text = self.class_labels[i].split('(')[0]
            if "Demented" in label_text:
                label_text = label_text.replace("Demented", "")

            prob_label = ctk.CTkLabel(
                prob_frame,
                text=f"{label_text}: {prob:.1%}",  # 简化百分比显示
                font=ctk.CTkFont(size=11),
                width=120  # 固定宽度
            )
            prob_label.pack(side="left", padx=8, pady=6)

            # 概率条（调整大小）
            prob_bar = ctk.CTkProgressBar(prob_frame, width=120, height=12)
            prob_bar.pack(side="right", padx=8, pady=6)
            prob_bar.set(prob)

            # 添加概率数值显示
            prob_value = ctk.CTkLabel(
                prob_frame,
                text=f"{prob:.1%}",
                font=ctk.CTkFont(size=9),
                text_color="gray"
            )
            prob_value.pack(side="right", padx=3)

        # 启用功能按钮
        self.save_btn.configure(state="normal")
        self.pdf_btn.configure(state="normal")
        self.html_btn.configure(state="normal")

        self.root.after(0, self.analysis_complete)
        self.root.after(0, self.update_status, "✅ CT症状分析完成")

    def update_status(self, message):
        """更新状态信息"""
        self.status_label.configure(text=message)

    def analysis_complete(self):
        """分析完成回调"""
        self.analyze_btn.configure(state="normal", text="🧠 分析症状类型")
        self.progress.set(0)

    def on_analysis_error(self, error):
        """分析错误处理"""
        self.analyze_btn.configure(state="normal", text="🧠 分析症状类型")
        self.progress.set(0)
        self.status_label.configure(text="❌ 症状分析失败")
        messagebox.showerror("分析错误", f"症状分析失败：{error}")

    def toggle_camera(self):
        """切换摄像头状态"""
        if not self.camera_active:
            self.start_camera()
        else:
            self.stop_camera()

    def start_camera(self):
        """启动摄像头"""
        try:
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                messagebox.showerror("错误", "无法打开摄像头")
                return

            self.camera_active = True
            self.camera_btn.configure(text="📹 关闭摄像头", fg_color="red")
            self.capture_btn.configure(state="normal")
            self.status_label.configure(text="📹 摄像头已启动 - 可以拍照进行症状分析")

            # 显示摄像头使用说明
            info_text = ("📹 摄像头模式说明：\n\n"
                        "• 摄像头主要用于预览和拍照\n"
                        "• 点击'拍照分析'可保存当前画面并进行症状分析\n"
                        "• 系统将分析CT图像中的4种痴呆症症状类型\n"
                        "• 注意：请确保拍摄的是CT医学影像")

            messagebox.showinfo("摄像头使用说明", info_text)

            # 开始摄像头循环
            self.camera_loop()

        except Exception as e:
            messagebox.showerror("错误", f"摄像头启动失败：{e}")

    def stop_camera(self):
        """停止摄像头"""
        self.camera_active = False

        if self.camera:
            self.camera.release()
            self.camera = None

        self.camera_btn.configure(text="📹 启动摄像头", fg_color="green")
        self.capture_btn.configure(state="disabled")
        self.status_label.configure(text="📹 摄像头已关闭")

        # 恢复默认显示
        self.image_label.configure(
            image="",
            text="🖼️\n\n请选择图像进行症状分析\n\n支持格式: JPG, PNG, BMP\n\n• CT图像：顺利分析\n• 非CT图像：验证警告但继续分析"
        )

    def camera_loop(self):
        """摄像头循环显示"""
        if self.camera_active and self.camera:
            ret, frame = self.camera.read()
            if ret:
                # 保存当前帧供拍照使用
                self.current_frame = frame.copy()

                # 转换颜色空间
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 调整大小
                height, width = frame_rgb.shape[:2]
                max_size = 450
                if width > height:
                    new_width = max_size
                    new_height = int(height * max_size / width)
                else:
                    new_height = max_size
                    new_width = int(width * max_size / height)

                frame_resized = cv2.resize(frame_rgb, (new_width, new_height))

                # 转换为PhotoImage
                pil_image = Image.fromarray(frame_resized)
                photo = ImageTk.PhotoImage(pil_image)

                # 更新显示
                self.image_label.configure(image=photo, text="")
                self.image_label.image = photo

            # 继续循环
            self.root.after(30, self.camera_loop)  # 约33 FPS

    def capture_and_analyze(self):
        """拍照并分析"""
        if not self.camera_active or not hasattr(self, 'current_frame'):
            messagebox.showerror("错误", "摄像头未启动或无可用画面")
            return

        try:
            # 保存当前帧为临时文件
            import tempfile
            temp_dir = tempfile.gettempdir()
            temp_path = os.path.join(temp_dir, "camera_capture.jpg")

            # 保存图像
            cv2.imwrite(temp_path, self.current_frame)

            # 显示拍照确认
            confirm_text = ("📸 已拍照！\n\n"
                          "⚠️ 重要提醒：\n"
                          "• 系统将先验证是否为CT图像\n"
                          "• 对于非CT图像会显示验证警告但继续分析\n"
                          "• 对于CT图像可以顺利进行症状分析\n"
                          "• 分析结果仅供参考\n\n"
                          "是否继续进行症状分析？")

            result = messagebox.askyesno("拍照确认", confirm_text)

            if result:
                # 设置为当前分析图像
                self.current_image_path = temp_path

                # 显示拍摄的图像
                self.display_image(temp_path)

                # 开始症状分析
                self.start_symptom_analysis()

                self.status_label.configure(text="📸 已拍照并开始症状分析")
            else:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                self.status_label.configure(text="📸 拍照已取消")

        except Exception as e:
            messagebox.showerror("错误", f"拍照失败：{e}")

    def save_results(self):
        """保存分析结果"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存症状分析结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results_history, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", "症状分析结果已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败：{e}")

    def generate_pdf_report(self):
        """生成PDF诊断报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        # 获取最新结果
        latest_result = self.results_history[-1]

        # 让用户选择保存位置和文件名
        default_name = f"CT症状分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        file_path = filedialog.asksaveasfilename(
            title="保存PDF报告",
            defaultextension=".pdf",
            initialname=default_name,
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_pdf_report(latest_result, file_path)
                messagebox.showinfo("成功", f"PDF报告已生成：{file_path}")

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "是否立即打开生成的PDF报告？"):
                    os.startfile(file_path)

            except Exception as e:
                messagebox.showerror("错误", f"PDF报告生成失败：{e}")

    def create_pdf_report(self, result_data, file_path):
        """创建PDF报告"""
        try:
            pdf = FPDF()
            pdf.add_page()

            # 设置字体
            pdf.set_font('Arial', 'B', 18)
            pdf.cell(0, 15, 'CT Symptom Analysis Report', 0, 1, 'C')
            pdf.ln(10)

            # 基本信息
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, 'Basic Information:', 0, 1)
            pdf.set_font('Arial', '', 12)
            pdf.cell(0, 8, f"Analysis Date: {result_data['timestamp']}", 0, 1)
            pdf.cell(0, 8, f"Image File: {os.path.basename(result_data['image_path'])}", 0, 1)
            pdf.ln(5)

            # CT验证结果
            if result_data.get('ct_validation'):
                pdf.set_font('Arial', 'B', 14)
                pdf.cell(0, 10, 'CT Image Validation:', 0, 1)
                pdf.set_font('Arial', '', 12)

                ct_data = result_data['ct_validation']
                ct_result = "Validated as CT Image" if ct_data['is_ct'] else "Possibly Non-CT Image"
                pdf.cell(0, 8, f"Validation Result: {ct_result}", 0, 1)
                pdf.cell(0, 8, f"Confidence Level: {ct_data['confidence']:.2%}", 0, 1)
                pdf.ln(5)

            # 症状分析结果（核心）
            pdf.set_font('Arial', 'B', 14)
            pdf.cell(0, 10, 'Symptom Analysis Results (Core):', 0, 1)
            pdf.set_font('Arial', '', 12)

            predicted_class = result_data['predicted_class_name']
            confidence = result_data['confidence']

            pdf.cell(0, 8, f"Predicted Symptom Type: {predicted_class}", 0, 1)
            pdf.cell(0, 8, f"Confidence Level: {confidence:.2%}", 0, 1)
            pdf.ln(5)

            # 详细概率分布（4种症状）
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, '4 Symptom Types Probability Distribution:', 0, 1)
            pdf.set_font('Arial', '', 10)

            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                pdf.cell(0, 6, f"  {class_name}: {prob:.4f} ({prob:.2%})", 0, 1)

            pdf.ln(10)

            # 免责声明
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'IMPORTANT DISCLAIMER:', 0, 1)
            pdf.set_font('Arial', '', 9)
            disclaimer = ("This AI analysis is for research and reference purposes only. "
                         "It cannot replace professional medical diagnosis. "
                         "Please consult qualified medical professionals for accurate diagnosis.")

            # 分行显示免责声明
            words = disclaimer.split()
            line = ""
            for word in words:
                if len(line + word) < 70:
                    line += word + " "
                else:
                    pdf.cell(0, 5, line.strip(), 0, 1)
                    line = word + " "
            if line:
                pdf.cell(0, 5, line.strip(), 0, 1)

            pdf.output(file_path)

        except Exception as e:
            raise Exception(f"PDF creation failed: {e}")

    def generate_html_report(self):
        """生成HTML可视化报告（完全照搬现有GUI的HTML功能）"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        # 获取最新结果
        latest_result = self.results_history[-1]

        # 让用户选择保存位置和文件名
        default_name = f"CT症状分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        file_path = filedialog.asksaveasfilename(
            title="保存HTML报告",
            defaultextension=".html",
            initialname=default_name,
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_html_report(latest_result, file_path)
                messagebox.showinfo("成功", f"HTML报告已生成：{file_path}")

                # 询问是否打开报告
                if messagebox.askyesno("打开报告", "是否立即在浏览器中打开HTML报告？"):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(file_path)}")

            except Exception as e:
                messagebox.showerror("错误", f"HTML报告生成失败：{e}")

    def create_html_report(self, result_data, file_path):
        """创建HTML可视化报告（完全照搬现有GUI的HTML功能）"""
        try:
            import base64

            # 读取图像并转换为base64
            image_base64 = ""
            try:
                with open(result_data['image_path'], 'rb') as img_file:
                    image_base64 = base64.b64encode(img_file.read()).decode('utf-8')
                    image_ext = os.path.splitext(result_data['image_path'])[1].lower()
                    if image_ext in ['.jpg', '.jpeg']:
                        image_mime = 'image/jpeg'
                    elif image_ext == '.png':
                        image_mime = 'image/png'
                    else:
                        image_mime = 'image/jpeg'
            except:
                pass

            # 生成概率分布图表数据
            chart_data = []
            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i].split('(')[1].replace(')', '') if '(' in self.class_labels[i] else self.class_labels[i]
                chart_data.append({'name': class_name, 'value': prob * 100})

            # HTML模板（完全照搬）
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CT症状分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .info-section {{
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }}
        .result-section {{
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }}
        .chart-section {{
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }}
        .recommendation-section {{
            background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
        }}
        .disclaimer-section {{
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-left: 5px solid #e17055;
        }}
        .section h2 {{
            color: #2d3436;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
            margin-top: 0;
        }}
        .image-container {{
            text-align: center;
            margin: 20px 0;
        }}
        .analysis-image {{
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }}
        .result-highlight {{
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3436;
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
            margin: 20px 0;
        }}
        .confidence-bar {{
            width: 100%;
            height: 30px;
            background: #ddd;
            border-radius: 15px;
            overflow: hidden;
            margin: 10px 0;
        }}
        .confidence-fill {{
            height: 100%;
            background: linear-gradient(90deg, #00b894, #00cec9, #74b9ff);
            border-radius: 15px;
            transition: width 0.5s ease;
        }}
        .chart-container {{
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }}
        .recommendation-list {{
            list-style: none;
            padding: 0;
        }}
        .recommendation-list li {{
            background: rgba(255,255,255,0.7);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #00b894;
        }}
        .footer {{
            text-align: center;
            padding: 20px;
            background: #2d3436;
            color: white;
        }}
        @media print {{
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 CT症状分析报告</h1>
            <p>基于深度学习的医学影像智能分析</p>
        </div>

        <div class="content">
            <div class="section info-section">
                <h2>📋 基本信息</h2>
                <p><strong>分析时间:</strong> {result_data['timestamp']}</p>
                <p><strong>图像文件:</strong> {os.path.basename(result_data['image_path'])}</p>
                <p><strong>分析模型:</strong> CT症状分析模型 v2.0</p>
                {"<p><strong>图像验证:</strong> " + str(result_data.get('ct_validation', {}).get('confidence', '未知')) + "置信度</p>" if 'ct_validation' in result_data else ""}
            </div>

            {"<div class='image-container'><img src='data:" + image_mime + ";base64," + image_base64 + "' alt='分析图像' class='analysis-image'></div>" if image_base64 else ""}

            <div class="section result-section">
                <h2>🎯 症状分析结果</h2>
                <div class="result-highlight">
                    预测症状类型: {result_data['predicted_class_name']}
                </div>
                <p><strong>置信度:</strong> {result_data['confidence']:.2%}</p>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: {result_data['confidence']*100}%"></div>
                </div>
            </div>

            <div class="section chart-section">
                <h2>📊 4种症状详细概率分布</h2>
                <div class="chart-container">
                    <canvas id="probabilityChart"></canvas>
                </div>
            </div>

            <div class="section recommendation-section">
                <h2>🏥 综合诊断与建议</h2>
"""

            # 添加诊断内容（完全照搬）
            predicted_class = result_data['predicted_class_name']
            if "NonDemented" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #00b894;">
                    风险等级: 低风险 ✅
                </div>
                <p>根据AI分析，未检测到明显的痴呆症状。大脑结构显示正常特征。</p>
                <ul class="recommendation-list">
                    <li>🏃‍♂️ 继续保持规律的体育锻炼</li>
                    <li>🥗 维持健康的饮食习惯</li>
                    <li>🧠 保持智力活动和社交互动</li>
                    <li>🏥 定期进行健康检查</li>
                </ul>
"""
            elif "VeryMild" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #fdcb6e;">
                    风险等级: 轻微风险 ⚠️
                </div>
                <p>检测到非常轻微的认知变化。这可能是正常老化过程，但建议进行更详细的评估。</p>
                <ul class="recommendation-list">
                    <li>👨‍⚕️ 建议咨询神经科医生</li>
                    <li>🧪 进行详细的神经心理学评估</li>
                    <li>📅 定期随访观察变化</li>
                    <li>🧘‍♀️ 加强认知训练活动</li>
                </ul>
"""
            elif "Mild" in predicted_class:
                html_content += """
                <div class="result-highlight" style="color: #e17055;">
                    风险等级: 中等风险 ⚠️
                </div>
                <p>检测到轻度痴呆的特征。建议尽快咨询神经科医生，进行全面的认知评估。</p>
                <ul class="recommendation-list">
                    <li>🚨 尽快就医咨询专业医生</li>
                    <li>🧠 进行全面的认知功能评估</li>
                    <li>💊 考虑药物治疗干预</li>
                    <li>👨‍👩‍👧‍👦 家属应给予更多关注和支持</li>
                </ul>
"""
            else:
                html_content += """
                <div class="result-highlight" style="color: #d63031;">
                    风险等级: 高风险 🚨
                </div>
                <p>检测到中度痴呆的特征。强烈建议立即就医，需要专业医生的诊断和治疗干预。</p>
                <ul class="recommendation-list">
                    <li>🏥 立即就医，寻求专业治疗</li>
                    <li>💊 制定个性化治疗方案</li>
                    <li>🏠 考虑日常生活照护安排</li>
                    <li>👨‍👩‍👧‍👦 家属需要专业指导和支持</li>
                </ul>
"""

            html_content += f"""
            </div>

            <div class="section disclaimer-section">
                <h2>⚠️ 重要免责声明</h2>
                <p><strong>本AI分析仅供研究和参考使用，不能替代专业医学诊断。</strong></p>
                <p>任何医疗决策都应基于合格医疗专业人员的建议。请咨询神经科医生或相关专家获取准确的诊断和治疗方案。</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 CT症状分析系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
    </div>

    <script>
        // 创建概率分布图表
        const ctx = document.getElementById('probabilityChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'doughnut',
            data: {{
                labels: {[item['name'] for item in chart_data]},
                datasets: [{{
                    data: {[item['value'] for item in chart_data]},
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom',
                        labels: {{
                            padding: 20,
                            font: {{
                                size: 14
                            }}
                        }}
                    }},
                    tooltip: {{
                        callbacks: {{
                            label: function(context) {{
                                return context.label + ': ' + context.parsed.toFixed(2) + '%';
                            }}
                        }}
                    }}
                }}
            }}
        }});

        // 添加打印功能
        function printReport() {{
            window.print();
        }}

        // 添加打印按钮
        document.addEventListener('DOMContentLoaded', function() {{
            const printBtn = document.createElement('button');
            printBtn.innerHTML = '🖨️ 打印报告';
            printBtn.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 10px 20px; background: #74b9ff; color: white; border: none; border-radius: 5px; cursor: pointer; z-index: 1000;';
            printBtn.onclick = printReport;
            document.body.appendChild(printBtn);
        }});
    </script>
</body>
</html>
"""

            # 写入HTML文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            # 如果HTML生成失败，创建简单的HTML
            self.create_simple_html_report(result_data, file_path)

    def create_simple_html_report(self, result_data, file_path):
        """创建简单的HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>CT症状分析报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        .header {{ text-align: center; color: #333; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CT症状分析报告</h1>
    </div>
    <div class="section">
        <h2>基本信息</h2>
        <p>分析时间: {result_data['timestamp']}</p>
        <p>图像文件: {os.path.basename(result_data['image_path'])}</p>
    </div>
    <div class="section">
        <h2>症状分析结果</h2>
        <p>预测症状类型: {result_data['predicted_class_name']}</p>
        <p>置信度: {result_data['confidence']:.2%}</p>
    </div>
    <div class="section">
        <h2>4种症状概率分布</h2>
"""
        for i, prob in enumerate(result_data['probabilities']):
            html_content += f"<p>{self.class_labels[i]}: {prob:.4f} ({prob:.2%})</p>"

        html_content += """
    </div>
    <div class="section">
        <h2>免责声明</h2>
        <p>本AI分析仅供研究和参考使用，不能替代专业医学诊断。</p>
    </div>
</body>
</html>
"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

    def show_history(self):
        """显示历史记录"""
        if not self.results_history:
            messagebox.showinfo("提示", "暂无历史记录")
            return

        # 创建历史记录窗口（增加窗口大小，确保有足够空间）
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("症状分析历史")
        history_window.geometry("800x600")  # 增加窗口大小
        history_window.resizable(True, True)  # 允许调整大小

        # 添加标题
        title_label = ctk.CTkLabel(
            history_window,
            text="📋 症状分析历史记录",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        title_label.pack(pady=(20, 10))

        # 历史记录列表（优化布局）
        history_frame = ctk.CTkScrollableFrame(history_window, height=450)  # 设置固定高度
        history_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for i, record in enumerate(reversed(self.results_history)):
            # 创建记录框架（增加间距和高度）
            record_frame = ctk.CTkFrame(history_frame)
            record_frame.pack(fill="x", pady=8, padx=5)  # 增加垂直和水平间距
            record_frame.configure(height=120)  # 设置最小高度

            # 基本信息（优化显示格式，避免文字遮挡）
            info_text = f"#{len(self.results_history)-i} - {record['timestamp']}\n"

            # CT验证信息（辅助，简化显示）
            if record.get('ct_validation'):
                ct_data = record['ct_validation']
                info_text += f"验证: {'✅CT' if ct_data['is_ct'] else '⚠️非CT'} ({ct_data['confidence']:.1%})\n"

            # 症状分析结果（核心，优化显示）
            symptom_name = record['predicted_class_name'].split('(')[0] if '(' in record['predicted_class_name'] else record['predicted_class_name']

            # 截断长的症状名称，避免文字重叠
            if len(symptom_name) > 15:  # 可以根据UI布局调整字符长度
                symptom_name = symptom_name[:15] + "..."  # 如果名称过长，添加省略号

            # 移除 "Demented" 部分
            if "Demented" in symptom_name:
                symptom_name = symptom_name.replace("Demented", "")

            # 拼接信息文本
            info_text += f"症状: {symptom_name}\n"
            info_text += f"置信度: {record['confidence']:.1%}"

            # 确保显示区域足够大，避免显示不完整
            record_label = ctk.CTkLabel(
                record_frame,
                text=info_text,
                font=ctk.CTkFont(size=12),
                justify="left",
                wraplength=350,  # 设置最大宽度，确保文本换行
                height=100  # 增加显示区域高度，确保足够空间显示文本
            )

            # 只需要创建一个label并显示
            record_label.pack(padx=15, pady=10, fill="x")  # 添加fill="x"确保水平填充

    def show_about(self):
        """显示关于信息"""
        about_text = """
🧠 CT症状分析系统 v2.0

基于深度学习的CT图像痴呆症症状智能分析系统

✨ 主要功能:
• 专业CT图像症状分析（核心功能）
• 双模型智能验证（CT验证 + 症状分析）
• 4种痴呆症症状精准识别
• 实时摄像头支持
• 概率分布可视化
• 结果历史记录
• 专业报告生成（PDF/HTML）

🔬 技术特点:
• 核心功能：CT图像症状识别
• 辅助功能：CT图像验证
• 高精度症状分类
• 现代化GUI界面

🎯 分析类别（4种症状）:
• 无痴呆 (NonDemented)
• 非常轻度痴呆 (VeryMildDemented)
• 轻度痴呆 (MildDemented)
• 中度痴呆 (ModerateDemented)

🔍 验证逻辑:
• 对于CT图像：顺利进行症状分析
• 对于非CT图像：显示验证警告但继续分析
• 系统会提示用户注意分析结果的准确性

⚠️ 重要声明:
本软件仅供研究和参考使用，
不能替代专业医学诊断。
请咨询专业医生获取准确诊断。

© 2024 CT症状分析系统
        """

        messagebox.showinfo("关于软件", about_text)

    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        finally:
            # 确保摄像头被正确释放
            if self.camera:
                self.camera.release()

def main():
    """主函数"""
    app = CTSymptomAnalysisSystem()
    app.run()

if __name__ == "__main__":
    main()
