"""
📁 EEG文件信息详细读取器
读取EEG_extracted目录下所有内容的完整信息
"""

import os
import glob
from collections import Counter
import json

def detailed_eeg_info():
    """详细读取EEG_extracted目录信息"""
    print("📁 EEG_extracted 目录详细信息读取")
    print("=" * 80)
    
    base_path = "EEG_extracted"
    
    # 检查基础目录是否存在
    if not os.path.exists(base_path):
        print(f"❌ 目录不存在: {base_path}")
        print("请确认路径是否正确")
        return
    
    print(f"✅ 找到目录: {base_path}")
    print(f"📍 完整路径: {os.path.abspath(base_path)}")
    
    # 1. 总体目录结构
    print(f"\n{'='*80}")
    print("📂 第一步：总体目录结构")
    print(f"{'='*80}")
    
    all_items = []
    for root, dirs, files in os.walk(base_path):
        level = root.replace(base_path, '').count(os.sep)
        indent = '  ' * level
        rel_path = os.path.relpath(root, base_path)
        
        if level == 0:
            print(f"{indent}📁 {os.path.basename(root)}/")
        else:
            print(f"{indent}📁 {os.path.basename(root)}/ ({rel_path})")
        
        # 统计文件
        subindent = '  ' * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            file_ext = os.path.splitext(file)[1].lower()
            
            # 格式化文件大小
            if file_size > 1024*1024:
                size_str = f"{file_size/(1024*1024):.1f} MB"
            elif file_size > 1024:
                size_str = f"{file_size/1024:.1f} KB"
            else:
                size_str = f"{file_size} bytes"
            
            print(f"{subindent}📄 {file} ({size_str}) [{file_ext}]")
            
            all_items.append({
                'path': file_path,
                'name': file,
                'size': file_size,
                'extension': file_ext,
                'directory': root,
                'level': level
            })
    
    # 2. 文件类型统计
    print(f"\n{'='*80}")
    print("📊 第二步：文件类型统计")
    print(f"{'='*80}")
    
    extensions = [item['extension'] for item in all_items if item['extension']]
    ext_counts = Counter(extensions)
    
    print(f"📈 文件扩展名统计:")
    for ext, count in ext_counts.most_common():
        total_size = sum(item['size'] for item in all_items if item['extension'] == ext)
        if total_size > 1024*1024:
            size_str = f"{total_size/(1024*1024):.1f} MB"
        else:
            size_str = f"{total_size/1024:.1f} KB"
        print(f"   {ext}: {count} 个文件, 总大小: {size_str}")
    
    # 3. 目录级别分析
    print(f"\n{'='*80}")
    print("📁 第三步：目录级别分析")
    print(f"{'='*80}")
    
    # 获取所有子目录
    subdirs = []
    for root, dirs, files in os.walk(base_path):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            rel_path = os.path.relpath(dir_path, base_path)
            
            # 统计该目录下的文件
            dir_files = []
            for file in files:
                if os.path.dirname(os.path.join(root, file)) == dir_path:
                    dir_files.append(file)
            
            # 递归统计子目录文件
            total_files = 0
            total_size = 0
            for subroot, subdirs_inner, subfiles in os.walk(dir_path):
                total_files += len(subfiles)
                for file in subfiles:
                    file_path = os.path.join(subroot, file)
                    total_size += os.path.getsize(file_path)
            
            subdirs.append({
                'name': dir_name,
                'path': dir_path,
                'rel_path': rel_path,
                'file_count': total_files,
                'total_size': total_size
            })
    
    print(f"📂 子目录详情:")
    for subdir in subdirs:
        if subdir['total_size'] > 1024*1024:
            size_str = f"{subdir['total_size']/(1024*1024):.1f} MB"
        else:
            size_str = f"{subdir['total_size']/1024:.1f} KB"
        
        print(f"   📁 {subdir['rel_path']}")
        print(f"      文件数量: {subdir['file_count']}")
        print(f"      总大小: {size_str}")
    
    # 4. 特殊文件检查
    print(f"\n{'='*80}")
    print("🔍 第四步：特殊文件检查")
    print(f"{'='*80}")
    
    # 检查常见的EEG相关文件
    eeg_extensions = ['.set', '.fdt', '.mat', '.edf', '.bdf', '.cnt', '.vhdr', '.vmrk', '.eeg']
    label_files = ['labels.txt', 'patient_list.txt', 'subjects.txt', 'info.txt']
    
    print(f"🧠 EEG相关文件:")
    eeg_files = [item for item in all_items if item['extension'] in eeg_extensions]
    if eeg_files:
        for file_info in eeg_files[:10]:  # 显示前10个
            rel_path = os.path.relpath(file_info['path'], base_path)
            if file_info['size'] > 1024*1024:
                size_str = f"{file_info['size']/(1024*1024):.1f} MB"
            else:
                size_str = f"{file_info['size']/1024:.1f} KB"
            print(f"   📄 {rel_path} ({size_str})")
        
        if len(eeg_files) > 10:
            print(f"   ... 还有 {len(eeg_files)-10} 个EEG文件")
    else:
        print(f"   ❌ 未找到EEG格式文件")
    
    print(f"\n📋 标签/信息文件:")
    for label_file in label_files:
        found_files = [item for item in all_items if item['name'].lower() == label_file.lower()]
        if found_files:
            for file_info in found_files:
                rel_path = os.path.relpath(file_info['path'], base_path)
                print(f"   ✅ {rel_path} ({file_info['size']} bytes)")
        else:
            print(f"   ❌ 未找到: {label_file}")
    
    # 5. 文件内容预览（文本文件）
    print(f"\n{'='*80}")
    print("📝 第五步：文本文件内容预览")
    print(f"{'='*80}")
    
    text_extensions = ['.txt', '.csv', '.json', '.log', '.md']
    text_files = [item for item in all_items if item['extension'] in text_extensions]
    
    for file_info in text_files:
        print(f"\n📄 文件: {os.path.relpath(file_info['path'], base_path)}")
        print(f"   大小: {file_info['size']} bytes")
        
        try:
            with open(file_info['path'], 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"   行数: {len(lines)}")
            print(f"   内容预览 (前10行):")
            
            for i, line in enumerate(lines[:10], 1):
                line_preview = line.strip()[:100]  # 限制每行显示长度
                print(f"     {i:2d}: {repr(line_preview)}")
            
            if len(lines) > 10:
                print(f"     ... 还有 {len(lines)-10} 行")
                
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    
    # 6. 总结报告
    print(f"\n{'='*80}")
    print("📊 第六步：总结报告")
    print(f"{'='*80}")
    
    total_files = len(all_items)
    total_size = sum(item['size'] for item in all_items)
    
    if total_size > 1024*1024*1024:
        size_str = f"{total_size/(1024*1024*1024):.2f} GB"
    elif total_size > 1024*1024:
        size_str = f"{total_size/(1024*1024):.1f} MB"
    else:
        size_str = f"{total_size/1024:.1f} KB"
    
    print(f"📈 总体统计:")
    print(f"   总文件数: {total_files}")
    print(f"   总大小: {size_str}")
    print(f"   目录数: {len(subdirs)}")
    print(f"   文件类型数: {len(ext_counts)}")
    
    print(f"\n🔍 关键发现:")
    if eeg_files:
        print(f"   ✅ 找到 {len(eeg_files)} 个EEG数据文件")
    else:
        print(f"   ❌ 未找到EEG数据文件")
    
    if text_files:
        print(f"   ✅ 找到 {len(text_files)} 个文本/标签文件")
    else:
        print(f"   ❌ 未找到文本/标签文件")
    
    print(f"\n✅ 详细信息读取完成！")
    print(f"=" * 80)

if __name__ == "__main__":
    detailed_eeg_info()
