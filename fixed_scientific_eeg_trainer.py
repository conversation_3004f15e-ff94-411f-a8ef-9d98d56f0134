"""
🧠 修复版科学EEG GPU训练器
解决TensorFlow版本兼容性问题
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score
from sklearn.preprocessing import LabelEncoder
import mne
import warnings
warnings.filterwarnings('ignore')

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"🚀 GPU配置成功: {len(gpus)} 个GPU可用")
    except RuntimeError as e:
        print(f"⚠️ GPU配置警告: {e}")

class FixedEEGTrainer:
    """修复版EEG训练器"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.label_encoder = LabelEncoder()
        
        # EEG参数
        self.sampling_rate = 128
        self.n_channels = 19
        self.epoch_length = 1.0
        self.n_samples = int(self.sampling_rate * self.epoch_length)
        
        print("🧠 修复版科学EEG GPU训练器初始化")
        print(f"📁 数据路径: {self.data_path}")
        print(f"📊 EEG参数: {self.n_channels}通道, {self.sampling_rate}Hz, {self.n_samples}样本/epoch")
    
    def load_eeg_data(self, split_name):
        """加载EEG数据"""
        print(f"\n📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        # 读取标签
        labels_df = pd.read_csv(labels_file, sep='\t')
        print(f"📋 标签信息: {len(labels_df)} 个受试者")
        
        # 加载EEG数据
        X_data = []
        y_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找对应的.set文件
            set_files = [f for f in os.listdir(split_dir) if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                print(f"⚠️ 未找到{subject_id}的.set文件")
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                # 使用MNE读取.set文件
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                
                # 获取数据
                data = raw.get_data()
                
                # 分割成epochs
                epochs = self.create_epochs(data)
                
                # 添加到数据集
                for epoch in epochs:
                    X_data.append(epoch)
                    y_labels.append(label)
                
                print(f"✅ {subject_id}: {len(epochs)} epochs")
                
            except Exception as e:
                print(f"❌ 加载{subject_id}失败: {e}")
                continue
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        print(f"📊 {split_name}数据加载完成:")
        print(f"   数据形状: {X.shape}")
        print(f"   标签数量: {len(y)}")
        print(f"   标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    
    def create_epochs(self, data):
        """创建epochs"""
        n_channels, n_timepoints = data.shape
        n_epochs = n_timepoints // self.n_samples
        
        epochs = []
        for i in range(n_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            
            epoch = data[:, start_idx:end_idx]
            
            # 确保通道数正确
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]
            elif epoch.shape[0] < self.n_channels:
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            epochs.append(epoch)
        
        return epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("\n🔧 预处理数据...")
        
        # 标准化EEG数据
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 0:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
            print(f"📋 标签编码: {dict(zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_))))}")
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 转换为分类标签
        n_classes = len(self.label_encoder.classes_)
        y_categorical = keras.utils.to_categorical(y_encoded, n_classes)
        
        print(f"✅ 预处理完成:")
        print(f"   输入形状: {X_processed.shape}")
        print(f"   标签形状: {y_categorical.shape}")
        print(f"   类别数: {n_classes}")
        
        return X_processed, y_categorical
    
    def build_model(self, n_classes):
        """构建深度学习模型"""
        print(f"\n🏗️ 构建模型 (类别数: {n_classes})...")
        
        # 输入层
        input_layer = layers.Input(shape=(self.n_channels, self.n_samples, 1))
        
        # 第一个卷积块
        x = layers.Conv2D(32, (1, 7), padding='same', activation='relu')(input_layer)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(32, (self.n_channels, 1), activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.25)(x)
        
        # 第二个卷积块
        x = layers.Conv2D(64, (1, 7), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.25)(x)
        
        # 第三个卷积块
        x = layers.Conv2D(128, (1, 7), padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.25)(x)
        
        # 全局平均池化
        x = layers.GlobalAveragePooling2D()(x)
        
        # 全连接层
        x = layers.Dense(256, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        x = layers.Dense(128, activation='relu')(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)
        
        # 输出层
        output = layers.Dense(n_classes, activation='softmax')(x)
        
        # 创建模型
        model = keras.Model(inputs=input_layer, outputs=output)
        
        # 修复版编译 - 只使用accuracy指标
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']  # 只使用accuracy，避免版本兼容问题
        )
        
        print("✅ 模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model
    
    def train_model(self, epochs=100, batch_size=32):
        """训练模型"""
        print(f"\n🚀 开始训练模型...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        
        # 加载数据
        X_train, y_train = self.load_eeg_data('train')
        X_val, y_val = self.load_eeg_data('val')
        
        # 预处理数据
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 添加通道维度
        X_train = np.expand_dims(X_train, axis=-1)
        X_val = np.expand_dims(X_val, axis=-1)
        
        # 构建模型
        n_classes = y_train.shape[1]
        self.model = self.build_model(n_classes)
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 训练模型
        print("🎯 开始训练...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )
        
        print("✅ 训练完成!")
        
        # 保存模型
        self.model.save('final_eeg_model.h5')
        print("💾 模型已保存: final_eeg_model.h5")
        
        return self.history
    
    def evaluate_model(self):
        """评估模型"""
        print("\n📊 评估模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_eeg_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        X_test = np.expand_dims(X_test, axis=-1)
        
        # 预测
        y_pred_prob = self.model.predict(X_test)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)
        
        # 计算指标
        test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
        
        # 手动计算其他指标
        test_prec = precision_score(y_true, y_pred, average='weighted')
        test_rec = recall_score(y_true, y_pred, average='weighted')
        
        print(f"📈 测试结果:")
        print(f"   准确率: {test_acc:.4f}")
        print(f"   精确率: {test_prec:.4f}")
        print(f"   召回率: {test_rec:.4f}")
        print(f"   损失: {test_loss:.4f}")
        
        # 分类报告
        class_names = self.label_encoder.classes_
        report = classification_report(y_true, y_pred, target_names=class_names)
        print(f"\n📋 详细分类报告:")
        print(report)
        
        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'accuracy': test_acc,
            'precision': test_prec,
            'recall': test_rec,
            'loss': test_loss,
            'classification_report': report
        }
    
    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("❌ 没有训练历史")
            return
        
        print("\n📈 绘制训练历史...")
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 5))
        
        # 准确率
        axes[0].plot(self.history.history['accuracy'], label='训练准确率')
        axes[0].plot(self.history.history['val_accuracy'], label='验证准确率')
        axes[0].set_title('模型准确率')
        axes[0].set_xlabel('Epoch')
        axes[0].set_ylabel('准确率')
        axes[0].legend()
        axes[0].grid(True)
        
        # 损失
        axes[1].plot(self.history.history['loss'], label='训练损失')
        axes[1].plot(self.history.history['val_loss'], label='验证损失')
        axes[1].set_title('模型损失')
        axes[1].set_xlabel('Epoch')
        axes[1].set_ylabel('损失')
        axes[1].legend()
        axes[1].grid(True)
        
        plt.tight_layout()
        plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
        plt.show()


def main():
    """主函数"""
    print("🧠 修复版科学EEG GPU训练系统")
    print("=" * 60)
    
    # 创建训练器
    trainer = FixedEEGTrainer()
    
    # 训练模型
    history = trainer.train_model(epochs=100, batch_size=32)
    
    # 绘制训练历史
    trainer.plot_training_history()
    
    # 评估模型
    results = trainer.evaluate_model()
    
    print("\n🎉 训练完成!")
    print(f"📊 最终准确率: {results['accuracy']:.4f}")


if __name__ == "__main__":
    main()
