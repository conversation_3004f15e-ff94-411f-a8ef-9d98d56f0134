# -*- coding: utf-8 -*-
"""
双模型AI痴呆症识别系统
AI Dementia Detection System with Dual Models

基于深度学习的医学影像智能分析系统
CT图像验证 + 症状智能分析

Author: AI Medical Solutions
Version: 2.0.0
License: MIT
"""

__version__ = "2.0.0"
__author__ = "AI Medical Solutions"
__email__ = "<EMAIL>"
__license__ = "MIT"
__description__ = "双模型AI痴呆症识别系统 - CT图像验证 + 症状智能分析"

# 核心模块导入
from .core.detector import DualModelDetector
from .core.models import ModelManager
from .core.utils import ImageProcessor, ResultsManager

# GUI模块
try:
    from .gui import DualModelAIDetectionApp
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

# 报告生成模块
from .reports.pdf_generator import PDFReportGenerator
from .reports.html_generator import HTMLReportGenerator

# 配置管理
from .config.settings import Settings, load_config

# 版本信息
VERSION_INFO = {
    "major": 2,
    "minor": 0,
    "patch": 0,
    "release": "stable"
}

# 支持的模型类型
SUPPORTED_MODELS = {
    "ct_detection": {
        "description": "CT图像识别模型",
        "input_shape": (150, 150, 3),
        "output_classes": 2,
        "threshold": 0.5
    },
    "symptom_analysis": {
        "description": "症状分析模型", 
        "input_shape": (150, 150, 3),
        "output_classes": 4,
        "class_labels": [
            "MildDemented(轻度痴呆)",
            "ModerateDemented(中度痴呆)", 
            "NonDemented(无痴呆)",
            "VeryMildDemented(非常轻度痴呆)"
        ]
    }
}

# 默认配置
DEFAULT_CONFIG = {
    "models": {
        "ct_detection_path": "models/ct_other_model.h5",
        "symptom_analysis_path": "models/ct_class.h5"
    },
    "processing": {
        "image_size": (150, 150),
        "normalization": True,
        "preprocessing": "standard"
    },
    "output": {
        "save_results": True,
        "generate_reports": True,
        "report_formats": ["pdf", "html"]
    }
}

# 公开API
__all__ = [
    # 核心类
    "DualModelDetector",
    "ModelManager", 
    "ImageProcessor",
    "ResultsManager",
    
    # GUI (如果可用)
    "DualModelAIDetectionApp" if GUI_AVAILABLE else None,
    
    # 报告生成
    "PDFReportGenerator",
    "HTMLReportGenerator",
    
    # 配置
    "Settings",
    "load_config",
    
    # 常量
    "VERSION_INFO",
    "SUPPORTED_MODELS",
    "DEFAULT_CONFIG",
    
    # 元信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    "__description__"
]

# 移除None值
__all__ = [item for item in __all__ if item is not None]

def get_version():
    """获取版本信息"""
    return __version__

def get_system_info():
    """获取系统信息"""
    import platform
    import sys
    
    return {
        "version": __version__,
        "python_version": sys.version,
        "platform": platform.platform(),
        "gui_available": GUI_AVAILABLE,
        "tensorflow_available": _check_tensorflow(),
        "opencv_available": _check_opencv()
    }

def _check_tensorflow():
    """检查TensorFlow是否可用"""
    try:
        import tensorflow as tf
        return tf.__version__
    except ImportError:
        return False

def _check_opencv():
    """检查OpenCV是否可用"""
    try:
        import cv2
        return cv2.__version__
    except ImportError:
        return False

def quick_start():
    """快速启动指南"""
    info = """
🧠 双模型AI痴呆症识别系统 v2.0 快速启动

1. 准备模型文件:
   - ct_other_model.h5 (CT图像识别)
   - ct_class.h5 (症状分析)

2. 启动GUI界面:
   from dual_model_detector import DualModelAIDetectionApp
   app = DualModelAIDetectionApp()
   app.run()

3. 使用API:
   from dual_model_detector import DualModelDetector
   detector = DualModelDetector()
   result = detector.analyze_image("image.jpg")

4. 命令行使用:
   dmd-gui  # 启动GUI
   dmd-cli --image image.jpg  # 命令行分析

更多信息请查看文档: https://dual-model-detector.readthedocs.io/
    """
    print(info)

# 初始化检查
def _init_check():
    """初始化检查"""
    import warnings
    
    # 检查必要依赖
    missing_deps = []
    
    try:
        import tensorflow
    except ImportError:
        missing_deps.append("tensorflow")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    if missing_deps:
        warnings.warn(
            f"缺少必要依赖: {', '.join(missing_deps)}. "
            f"请运行: pip install {' '.join(missing_deps)}",
            ImportWarning
        )
    
    # 检查GUI依赖
    if not GUI_AVAILABLE:
        warnings.warn(
            "GUI功能不可用，缺少customtkinter依赖. "
            "请运行: pip install customtkinter",
            ImportWarning
        )

# 执行初始化检查
_init_check()
