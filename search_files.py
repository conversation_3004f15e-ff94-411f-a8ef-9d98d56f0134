import os
import glob

print("🔍 搜索所有可能的文件...")

# 搜索模式
patterns = [
    "*untitled*",
    "*Untitled*", 
    "*UNTITLED*",
    "*eeg*",
    "*EEG*",
    "*extracted*",
    "*目录*",
    "*信息*"
]

all_files = []

for pattern in patterns:
    files = glob.glob(pattern, recursive=False)
    all_files.extend(files)

# 去重并排序
all_files = sorted(list(set(all_files)))

print(f"📁 找到的相关文件:")
for i, file in enumerate(all_files, 1):
    if os.path.isfile(file):
        size = os.path.getsize(file)
        print(f"   {i}. {file} ({size} bytes)")

# 也搜索子目录
print(f"\n📁 搜索子目录中的文件...")
for root, dirs, files in os.walk('.'):
    for file in files:
        if any(keyword in file.lower() for keyword in ['untitled', 'eeg', 'extracted', '目录', '信息']):
            full_path = os.path.join(root, file)
            size = os.path.getsize(full_path)
            print(f"   📄 {full_path} ({size} bytes)")

print(f"\n📋 请告诉我您要查看的确切文件名！")
