# -*- coding: utf-8 -*-
"""
AI痴呆症识别器 - 工作版本
包含HTML报告功能，确保能正常启动
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class AIDetectionApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("AI痴呆症识别器 v1.0 - 工作版")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.model = None
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_model_async()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 AI痴呆症识别器 - 工作版",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=350)
        
        self.create_left_panel()
        self.create_right_panel()
        self.create_status_bar()
        
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 医学影像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
        
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 控制按钮区域
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=20)
        
        # 选择图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择影像文件",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 开始分析按钮
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🔍 开始AI分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.start_analysis,
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", pady=10)
        
        # 摄像头控制按钮
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=10)
        
        # 拍照分析按钮
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="📸 拍照分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=10)
        
        # HTML报告按钮 - 放在拍照按钮下面
        self.html_btn = ctk.CTkButton(
            control_frame,
            text="🌐 生成HTML报告",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.generate_html_report,
            state="disabled",
            fg_color="orange"
        )
        self.html_btn.pack(fill="x", pady=10)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(control_frame)
        self.progress.pack(fill="x", pady=10)
        self.progress.set(0)
        
        # 结果显示区域
        self.create_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
        
    def create_results_panel(self):
        """创建结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        self.result_label = ctk.CTkLabel(
            results_frame,
            text="等待分析...",
            font=ctk.CTkFont(size=14),
            wraplength=280
        )
        self.result_label.pack(pady=10)
        
        self.confidence_label = ctk.CTkLabel(
            results_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.confidence_label.pack(pady=5)
        
        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=150)
        self.details_frame.pack(fill="x", padx=10, pady=10)
        
    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=20, pady=10)
        
        # 保存结果按钮
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled"
        )
        self.save_btn.pack(fill="x", pady=5)
        
        # 生成PDF报告按钮
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="green"
        )
        self.pdf_btn.pack(fill="x", pady=5)
        
        # 查看历史按钮
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history
        )
        self.history_btn.pack(fill="x", pady=5)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about
        )
        self.about_btn.pack(fill="x", pady=5)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载AI模型...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        self.model_status = ctk.CTkLabel(
            self.status_frame,
            text="⏳ 加载中",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        self.model_status.pack(side="right", padx=20, pady=10)

    def load_model_async(self):
        """异步加载模型"""
        def load_model():
            try:
                import warnings
                warnings.filterwarnings('ignore')

                import tensorflow as tf
                from tensorflow.keras.preprocessing import image
                import numpy as np

                tf.get_logger().setLevel('ERROR')

                try:
                    import absl.logging
                    absl.logging.set_verbosity(absl.logging.ERROR)
                except:
                    pass

                model_path = r"D:\模型开发\升级model.h5"
                if os.path.exists(model_path):
                    self.model = tf.keras.models.load_model(model_path)
                    self.tf = tf
                    self.image_module = image
                    self.np = np
                    self.root.after(0, self.on_model_loaded, True)
                else:
                    self.root.after(0, self.on_model_loaded, False)

            except Exception as e:
                self.root.after(0, self.on_model_error, str(e))

        threading.Thread(target=load_model, daemon=True).start()

    def on_model_loaded(self, success):
        """模型加载完成回调"""
        if success:
            self.status_label.configure(text="✅ AI模型已就绪")
            self.model_status.configure(text="🟢 已加载", text_color="green")
        else:
            self.status_label.configure(text="❌ 模型文件未找到")
            self.model_status.configure(text="🔴 未找到", text_color="red")
            messagebox.showerror("错误", "未找到模型文件：D:\\模型开发\\升级model.h5")

    def on_model_error(self, error):
        """模型加载错误回调"""
        self.status_label.configure(text="❌ 模型加载失败")
        self.model_status.configure(text="🔴 错误", text_color="red")
        messagebox.showerror("错误", f"模型加载失败：{error}")

    def select_image(self):
        """选择图像文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择医学影像文件",
            filetypes=file_types
        )

        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_btn.configure(state="normal")
            self.status_label.configure(text=f"📁 已选择: {os.path.basename(file_path)}")

    def display_image(self, image_path):
        """显示选择的图像"""
        try:
            pil_image = Image.open(image_path)
            display_size = (400, 400)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(pil_image)
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图像：{e}")

    def start_analysis(self):
        """开始AI分析"""
        if not self.model:
            messagebox.showerror("错误", "AI模型未加载")
            return

        if not self.current_image_path:
            messagebox.showerror("错误", "请先选择图像文件")
            return

        self.analyze_btn.configure(state="disabled", text="🔄 分析中...")
        self.progress.set(0)
        threading.Thread(target=self.perform_analysis, daemon=True).start()

    def perform_analysis(self):
        """执行AI分析"""
        try:
            self.root.after(0, lambda: self.progress.set(0.2))

            # 加载和预处理图像
            img = self.image_module.load_img(self.current_image_path, target_size=(150, 150))
            img_array = self.image_module.img_to_array(img)
            img_array = self.np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            self.root.after(0, lambda: self.progress.set(0.6))

            # 进行预测
            predictions = self.model.predict(img_array, verbose=0)
            predicted_class = self.np.argmax(predictions, axis=1)
            prediction_probs = predictions[0].tolist()

            self.root.after(0, lambda: self.progress.set(0.9))

            # 准备结果
            result_data = {
                'predicted_class': predicted_class[0],
                'predicted_class_name': self.class_labels[predicted_class[0]],
                'confidence': max(prediction_probs),
                'probabilities': prediction_probs,
                'image_path': self.current_image_path,
                'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.root.after(0, lambda: self.progress.set(1.0))
            self.root.after(0, self.display_results, result_data)

        except Exception as e:
            self.root.after(0, self.on_analysis_error, str(e))

    def display_results(self, result_data):
        """显示分析结果"""
        self.results_history.append(result_data)

        class_name = result_data['predicted_class_name']
        confidence = result_data['confidence']

        # 显示结果
        result_text = f"🎯 预测结果:\n{class_name}"
        self.result_label.configure(text=result_text, text_color="white")
        self.confidence_label.configure(text=f"🎯 置信度: {confidence:.2%}", text_color="lightblue")

        # 清空详细结果框
        for widget in self.details_frame.winfo_children():
            widget.destroy()

        # 显示详细概率
        for i, prob in enumerate(result_data['probabilities']):
            prob_frame = ctk.CTkFrame(self.details_frame)
            prob_frame.pack(fill="x", pady=2)

            label_text = self.class_labels[i].split('(')[0]
            prob_label = ctk.CTkLabel(prob_frame, text=f"{label_text}: {prob:.2%}", font=ctk.CTkFont(size=11))
            prob_label.pack(side="left", padx=10, pady=5)

            prob_bar = ctk.CTkProgressBar(prob_frame, width=100, height=10)
            prob_bar.pack(side="right", padx=10, pady=5)
            prob_bar.set(prob)

        # 重置按钮状态
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.save_btn.configure(state="normal")
        self.pdf_btn.configure(state="normal")
        self.html_btn.configure(state="normal")
        self.progress.set(0)
        self.status_label.configure(text="✅ 分析完成")

    def on_analysis_error(self, error):
        """分析错误处理"""
        self.analyze_btn.configure(state="normal", text="🔍 开始AI分析")
        self.progress.set(0)
        self.status_label.configure(text="❌ 分析失败")
        messagebox.showerror("分析错误", f"AI分析失败：{error}")

    def save_results(self):
        """保存分析结果"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可保存的结果")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存分析结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.results_history, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"结果已保存到：{file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败：{e}")

    def generate_pdf_report(self):
        """生成PDF报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        latest_result = self.results_history[-1]
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        file_path = filedialog.asksaveasfilename(
            title="保存PDF报告",
            defaultextension=".pdf",
            initialfile=default_name,
            filetypes=[("PDF文件", "*.pdf"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_pdf_report(latest_result, file_path)
                messagebox.showinfo("成功", f"PDF报告已生成：{file_path}")
                if messagebox.askyesno("打开报告", "是否立即打开PDF报告？"):
                    os.startfile(file_path)
            except Exception as e:
                # PDF失败，创建文本报告
                text_path = file_path.replace('.pdf', '.txt')
                self.create_text_report(latest_result, text_path)
                messagebox.showinfo("成功", f"文本报告已生成：{text_path}")
                if messagebox.askyesno("打开报告", "是否立即打开文本报告？"):
                    os.startfile(text_path)

    def generate_html_report(self):
        """生成HTML报告"""
        if not self.results_history:
            messagebox.showwarning("警告", "没有可生成报告的结果")
            return

        latest_result = self.results_history[-1]
        default_name = f"AI痴呆症诊断报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"

        file_path = filedialog.asksaveasfilename(
            title="保存HTML报告",
            defaultextension=".html",
            initialfile=default_name,
            filetypes=[("HTML文件", "*.html"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                self.create_html_report(latest_result, file_path)
                messagebox.showinfo("成功", f"HTML报告已生成：{file_path}")
                if messagebox.askyesno("打开报告", "是否在浏览器中打开HTML报告？"):
                    import webbrowser
                    webbrowser.open(f"file://{os.path.abspath(file_path)}")
            except Exception as e:
                messagebox.showerror("错误", f"HTML报告生成失败：{e}")

    def create_html_report(self, result_data, file_path):
        """创建包含原始图像的HTML报告"""
        try:
            import base64

            # 读取原始图像并转换为base64
            image_section = ""

            try:
                with open(result_data['image_path'], 'rb') as img_file:
                    image_data = img_file.read()
                    image_base64 = base64.b64encode(image_data).decode('utf-8')

                    # 根据文件扩展名确定MIME类型
                    image_ext = os.path.splitext(result_data['image_path'])[1].lower()
                    if image_ext in ['.jpg', '.jpeg']:
                        image_mime = 'image/jpeg'
                    elif image_ext == '.png':
                        image_mime = 'image/png'
                    elif image_ext == '.bmp':
                        image_mime = 'image/bmp'
                    else:
                        image_mime = 'image/jpeg'

                    image_section = f'''
                    <div style="text-align: center; margin: 20px 0; padding: 20px; background: #f0f8ff; border-radius: 10px;">
                        <h2>📷 原始医学影像</h2>
                        <img src="data:{image_mime};base64,{image_base64}" alt="分析图像" style="max-width: 400px; max-height: 400px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                        <p style="margin-top: 10px; color: #666;">上图为本次分析的原始医学影像</p>
                    </div>
                    '''
            except Exception as e:
                print(f"无法读取图像文件: {e}")
                image_section = '''
                <div style="text-align: center; margin: 20px 0; padding: 20px; background: #ffe6e6; border-radius: 10px;">
                    <h2>📷 原始医学影像</h2>
                    <p style="color: #e74c3c;">⚠️ 无法显示原始图像</p>
                </div>
                '''

            # 创建概率分布列表
            prob_list_html = ""
            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                if '(' in class_name:
                    chinese_name = class_name.split('(')[1].replace(')', '')
                    english_name = class_name.split('(')[0]
                else:
                    chinese_name = class_name
                    english_name = class_name

                prob_list_html += f'''
                <div style="background: rgba(255,255,255,0.8); padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #3498db;">
                    <strong>{chinese_name}</strong><br>
                    <small>{english_name}</small><br>
                    <span style="font-size: 1.2em; color: #2c3e50;">{prob:.4f} ({prob:.2%})</span>
                </div>
                '''

            # 创建HTML内容
            html_content = f'''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI痴呆症诊断报告 - {result_data['timestamp']}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }}
        .container {{
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            font-size: 2.2em;
            margin: 0 0 10px 0;
        }}
        .content {{
            padding: 30px;
        }}
        .section {{
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .info-section {{
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }}
        .result-section {{
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            text-align: center;
        }}
        .result-highlight {{
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            margin: 15px 0;
        }}
        .confidence {{
            font-size: 1.3em;
            color: #27ae60;
            font-weight: bold;
        }}
        .prob-section {{
            background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
        }}
        .disclaimer {{
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 2px solid #f39c12;
            text-align: center;
        }}
        .footer {{
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 15px;
            font-size: 0.9em;
        }}
        .print-btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 15px;
        }}
        .print-btn:hover {{
            background: #2980b9;
        }}
        @media print {{
            body {{ background: white; padding: 0; }}
            .container {{ box-shadow: none; }}
            .print-btn {{ display: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI痴呆症诊断报告</h1>
            <p>基于深度学习的医学影像智能分析系统</p>
            <button class="print-btn" onclick="window.print()">🖨️ 打印报告</button>
        </div>

        <div class="content">
            <div class="section info-section">
                <h2>📋 基本信息</h2>
                <p><strong>分析时间:</strong> {result_data['timestamp']}</p>
                <p><strong>图像文件:</strong> {os.path.basename(result_data['image_path'])}</p>
                <p><strong>文件大小:</strong> {self.get_file_size(result_data['image_path'])}</p>
            </div>

            {image_section}

            <div class="section result-section">
                <h2>🎯 AI分析结果</h2>
                <div class="result-highlight">
                    {result_data['predicted_class_name']}
                </div>
                <div class="confidence">
                    置信度: {result_data['confidence']:.2%}
                </div>
            </div>

            <div class="section prob-section">
                <h2>📊 详细概率分布</h2>
                {prob_list_html}
            </div>

            <div class="section disclaimer">
                <h2>⚠️ 重要医学声明</h2>
                <p><strong>本AI分析结果仅供研究和参考使用，不能替代专业医学诊断。</strong></p>
                <p>任何医疗决策都应该咨询合格的医疗专业人员。</p>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 AI Medical Solutions | 报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
            '''

            # 保存HTML文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            raise Exception(f"HTML报告生成失败: {e}")

    def get_file_size(self, file_path):
        """获取文件大小的友好显示"""
        try:
            size = os.path.getsize(file_path)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"

    def create_pdf_report(self, result_data, file_path):
        """创建PDF报告"""
        try:
            from fpdf import FPDF

            pdf = FPDF()
            pdf.add_page()

            # 标题
            pdf.set_font('Arial', 'B', 16)
            pdf.cell(0, 10, 'AI Dementia Detection Report', 0, 1, 'C')
            pdf.ln(10)

            # 基本信息
            pdf.set_font('Arial', '', 12)
            pdf.cell(0, 8, f"Analysis Date: {result_data['timestamp']}", 0, 1)
            pdf.cell(0, 8, f"Image File: {os.path.basename(result_data['image_path'])}", 0, 1)
            pdf.ln(5)

            # 分析结果
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Analysis Results:', 0, 1)
            pdf.set_font('Arial', '', 11)

            predicted_class = result_data['predicted_class_name']
            confidence = result_data['confidence']

            pdf.cell(0, 8, f"Predicted Classification: {predicted_class}", 0, 1)
            pdf.cell(0, 8, f"Confidence Level: {confidence:.2%}", 0, 1)
            pdf.ln(5)

            # 详细概率分布
            pdf.set_font('Arial', 'B', 12)
            pdf.cell(0, 8, 'Detailed Probability Distribution:', 0, 1)
            pdf.set_font('Arial', '', 10)

            for i, prob in enumerate(result_data['probabilities']):
                class_name = self.class_labels[i]
                pdf.cell(0, 6, f"  {class_name}: {prob:.4f} ({prob:.2%})", 0, 1)

            pdf.ln(10)

            # 免责声明
            pdf.set_font('Arial', 'B', 10)
            pdf.cell(0, 6, 'IMPORTANT DISCLAIMER:', 0, 1)
            pdf.set_font('Arial', '', 9)
            disclaimer = ("This AI analysis is for research and reference purposes only. "
                         "It cannot replace professional medical diagnosis. "
                         "Please consult qualified healthcare professionals.")

            words = disclaimer.split()
            line = ""
            for word in words:
                if len(line + word) < 80:
                    line += word + " "
                else:
                    pdf.cell(0, 5, line.strip(), 0, 1)
                    line = word + " "
            if line:
                pdf.cell(0, 5, line.strip(), 0, 1)

            pdf.output(file_path)

        except ImportError:
            raise Exception("PDF库未安装，请安装fpdf2: pip install fpdf2")
        except Exception as e:
            raise Exception(f"PDF创建失败: {e}")

    def create_text_report(self, result_data, file_path):
        """创建文本报告"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("AI痴呆症检测报告\n")
            f.write("=" * 30 + "\n\n")
            f.write(f"分析时间: {result_data['timestamp']}\n")
            f.write(f"图像文件: {os.path.basename(result_data['image_path'])}\n\n")
            f.write(f"预测结果: {result_data['predicted_class_name']}\n")
            f.write(f"置信度: {result_data['confidence']:.2%}\n\n")
            f.write("详细概率分布:\n")
            for i, prob in enumerate(result_data['probabilities']):
                f.write(f"  {self.class_labels[i]}: {prob:.4f} ({prob:.2%})\n")
            f.write("\n重要声明:\n")
            f.write("此结果仅供研究和参考使用，不能替代专业医学诊断。\n")
            f.write("请咨询合格的医疗专业人员获取准确诊断。\n")

    def show_history(self):
        """显示历史记录"""
        if not self.results_history:
            messagebox.showinfo("提示", "暂无历史记录")
            return

        history_window = ctk.CTkToplevel(self.root)
        history_window.title("分析历史")
        history_window.geometry("600x400")

        history_frame = ctk.CTkScrollableFrame(history_window)
        history_frame.pack(fill="both", expand=True, padx=20, pady=20)

        for i, record in enumerate(reversed(self.results_history)):
            record_frame = ctk.CTkFrame(history_frame)
            record_frame.pack(fill="x", pady=5)

            info_text = f"#{len(self.results_history)-i} - {record['timestamp']}\n"
            info_text += f"结果: {record['predicted_class_name']}\n"
            info_text += f"置信度: {record['confidence']:.2%}"

            record_label = ctk.CTkLabel(record_frame, text=info_text, font=ctk.CTkFont(size=12), justify="left")
            record_label.pack(padx=15, pady=10)

    def show_about(self):
        """显示关于信息"""
        about_text = """
🧠 AI痴呆症识别器 v1.0 - 工作版

基于深度学习的医学影像智能分析系统

✨ 主要功能:
• 智能图像分析
• 多类别痴呆症检测
• 概率分布可视化
• 结果历史记录
• 专业报告生成
• HTML报告包含原始图像

⚠️ 重要声明:
本软件仅供研究和参考使用，
不能替代专业医学诊断。
请咨询专业医生获取准确诊断。

© 2024 AI Medical Solutions
        """
        messagebox.showinfo("关于软件", about_text)

    def toggle_camera(self):
        """切换摄像头状态"""
        messagebox.showinfo("提示", "摄像头功能暂未实现")

    def capture_and_analyze(self):
        """拍照分析"""
        messagebox.showinfo("提示", "拍照功能暂未实现")

    def run(self):
        """运行应用程序"""
        try:
            self.root.mainloop()
        finally:
            if self.camera:
                self.camera.release()

if __name__ == "__main__":
    app = AIDetectionApp()
    app.run()
