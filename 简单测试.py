#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的TensorFlow测试脚本
"""

print("🔍 开始测试...")

# 步骤1：测试基本Python功能
print("✅ Python基本功能正常")

# 步骤2：测试TensorFlow导入
try:
    print("📦 正在导入TensorFlow...")
    import tensorflow as tf
    print(f"✅ TensorFlow导入成功！版本: {tf.__version__}")
except ImportError as e:
    print(f"❌ TensorFlow导入失败: {e}")
    print("💡 解决方案: 运行 'pip install tensorflow'")
    exit(1)

# 步骤3：测试其他依赖
try:
    import numpy as np
    print("✅ NumPy导入成功")
except ImportError:
    print("❌ NumPy导入失败")

try:
    from tensorflow.keras.preprocessing import image
    print("✅ Keras preprocessing导入成功")
except ImportError:
    print("❌ Keras preprocessing导入失败")

# 步骤4：测试文件路径
import os
model_path = r"D:\模型开发\升级model.h5"
image_path = r"D:\模型开发\1.jpg"

print(f"\n📁 检查文件路径:")
print(f"模型文件: {model_path}")
print(f"存在: {'✅' if os.path.exists(model_path) else '❌'}")

print(f"图片文件: {image_path}")
print(f"存在: {'✅' if os.path.exists(image_path) else '❌'}")

# 步骤5：简单的TensorFlow测试
try:
    print(f"\n🧪 TensorFlow功能测试:")
    test_tensor = tf.constant([1, 2, 3])
    print(f"✅ 张量创建成功: {test_tensor}")
except Exception as e:
    print(f"❌ TensorFlow功能测试失败: {e}")

print(f"\n🎯 测试完成！")
print(f"如果所有项目都显示 ✅，那么您的环境应该可以运行主程序。")
