"""
突破性解决方案
专门解决极端类别不平衡问题 (166:3806:3032)
目标: 90%+ 准确率 + Dementia类别有效识别
"""

import pandas as pd
import numpy as np
import os
import time
import warnings
warnings.filterwarnings('ignore')

print("💥 突破性解决方案")
print("🎯 专门解决极端类别不平衡问题")
print("⚖️ 类别分布: Dementia(166) vs MCI(3806) vs Normal(3032)")
print("🚀 目标: 90%+ 准确率 + Dementia有效识别")
print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 70)

start_time = time.time()
data_path = r"D:\模型开发\audio\processed_datasets"
output_path = r"D:\模型开发\audio"

# 加载数据
print("📊 加载数据...")
train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))

combined_train = pd.concat([train_data, val_data], ignore_index=True)

feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
X_train = combined_train[feature_cols].values
y_train = combined_train['diagnosis_encoded'].values
X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"✅ 数据加载完成")
print(f"   训练集: {X_train.shape}")
print(f"   测试集: {X_test.shape}")

class_counts = np.bincount(y_train)
print(f"   训练集类别分布: Dementia({class_counts[0]}) MCI({class_counts[1]}) Normal({class_counts[2]})")

test_class_counts = np.bincount(y_test)
print(f"   测试集类别分布: Dementia({test_class_counts[0]}) MCI({test_class_counts[1]}) Normal({test_class_counts[2]})")

# 突破性策略1: 极端SMOTE过采样
print(f"\n💥 突破性策略1: 极端SMOTE过采样...")

try:
    from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
    from imblearn.combine import SMOTEENN, SMOTETomek
    
    # 计算目标分布 - 让Dementia类别达到合理数量
    target_dementia = max(1000, class_counts[0] * 6)  # 至少1000个样本
    target_mci = class_counts[1]  # 保持MCI不变
    target_normal = class_counts[2]  # 保持Normal不变
    
    print(f"   目标分布: Dementia({target_dementia}) MCI({target_mci}) Normal({target_normal})")
    
    # 使用自定义采样策略
    sampling_strategy = {
        0: target_dementia,  # Dementia
        1: target_mci,       # MCI
        2: target_normal     # Normal
    }
    
    # 尝试多种SMOTE方法
    smote_methods = {
        'SMOTE': SMOTE(sampling_strategy=sampling_strategy, random_state=42, k_neighbors=3),
        'BorderlineSMOTE': BorderlineSMOTE(sampling_strategy=sampling_strategy, random_state=42, k_neighbors=3),
        'ADASYN': ADASYN(sampling_strategy=sampling_strategy, random_state=42, n_neighbors=3)
    }
    
    best_smote = None
    best_balance = 0
    
    for name, method in smote_methods.items():
        try:
            X_temp, y_temp = method.fit_resample(X_train, y_train)
            temp_counts = np.bincount(y_temp)
            balance_score = min(temp_counts) / max(temp_counts)
            
            print(f"     {name}: {temp_counts}, 平衡度: {balance_score:.3f}")
            
            if balance_score > best_balance:
                best_balance = balance_score
                best_smote = method
                X_train_balanced = X_temp
                y_train_balanced = y_temp
        
        except Exception as e:
            print(f"     {name} 失败: {e}")
    
    if best_smote:
        final_counts = np.bincount(y_train_balanced)
        print(f"   ✅ 最佳SMOTE结果: {final_counts}")
        use_smote = True
    else:
        print("   ❌ SMOTE失败，使用原始数据")
        X_train_balanced = X_train
        y_train_balanced = y_train
        use_smote = False

except ImportError:
    print("   ❌ imblearn库不可用")
    X_train_balanced = X_train
    y_train_balanced = y_train
    use_smote = False

# 突破性策略2: 极端类别权重
print(f"\n💥 突破性策略2: 极端类别权重...")

from sklearn.utils.class_weight import compute_class_weight

# 计算极端权重 - 给Dementia类别超高权重
extreme_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
extreme_weights[0] *= 5.0  # Dementia权重放大5倍

class_weight_dict = {i: extreme_weights[i] for i in range(len(extreme_weights))}
print(f"   极端权重: {class_weight_dict}")

# 突破性策略3: 多阶段训练
print(f"\n💥 突破性策略3: 多阶段训练...")

# 阶段1: 专门训练Dementia vs 其他的二分类器
print("   阶段1: Dementia vs 其他二分类...")

# 创建二分类标签
y_binary = (y_train_balanced == 0).astype(int)  # 1=Dementia, 0=其他
y_test_binary = (y_test == 0).astype(int)

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, f1_score

# 训练二分类器
binary_rf = RandomForestClassifier(
    n_estimators=1000,
    max_depth=None,
    class_weight={0: 1, 1: 10},  # 极端权重
    random_state=42,
    n_jobs=-1
)

binary_rf.fit(X_train_balanced, y_binary)
binary_pred = binary_rf.predict(X_test)
binary_acc = accuracy_score(y_test_binary, binary_pred)

print(f"     二分类准确率: {binary_acc:.4f}")
print(f"     二分类报告:")
print(classification_report(y_test_binary, binary_pred, target_names=['其他', 'Dementia']))

# 阶段2: 在非Dementia样本中区分MCI和Normal
print("   阶段2: MCI vs Normal分类...")

# 提取非Dementia样本
non_dementia_mask = y_train_balanced != 0
X_non_dementia = X_train_balanced[non_dementia_mask]
y_non_dementia = y_train_balanced[non_dementia_mask]

# 重新编码标签 (1->0, 2->1)
y_non_dementia_binary = (y_non_dementia == 2).astype(int)  # 1=Normal, 0=MCI

mci_normal_rf = RandomForestClassifier(
    n_estimators=500,
    max_depth=20,
    class_weight='balanced',
    random_state=42,
    n_jobs=-1
)

mci_normal_rf.fit(X_non_dementia, y_non_dementia_binary)

# 突破性策略4: 集成多种强力模型
print(f"\n💥 突破性策略4: 集成多种强力模型...")

models = {}
results = {}

# 模型1: 极端随机森林
print("   训练极端随机森林...")
try:
    extreme_rf = RandomForestClassifier(
        n_estimators=2000,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        max_features='sqrt',
        class_weight=class_weight_dict,
        random_state=42,
        n_jobs=-1
    )
    
    extreme_rf.fit(X_train_balanced, y_train_balanced)
    rf_pred = extreme_rf.predict(X_test)
    rf_acc = accuracy_score(y_test, rf_pred)
    
    models['ExtremeRandomForest'] = extreme_rf
    results['ExtremeRandomForest'] = {
        'accuracy': rf_acc,
        'f1_score': f1_score(y_test, rf_pred, average='weighted'),
        'classification_report': classification_report(y_test, rf_pred, output_dict=True)
    }
    
    print(f"     极端随机森林准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")
    
    # 检查Dementia识别
    rf_report = classification_report(y_test, rf_pred, output_dict=True)
    if '0' in rf_report:
        dem_recall = rf_report['0']['recall']
        dem_precision = rf_report['0']['precision']
        print(f"     Dementia: 精确率={dem_precision:.3f}, 召回率={dem_recall:.3f}")
    
except Exception as e:
    print(f"     失败: {e}")

# 模型2: XGBoost with extreme parameters
print("   训练极端XGBoost...")
try:
    import xgboost as xgb
    
    # 计算样本权重
    sample_weights = np.array([class_weight_dict[label] for label in y_train_balanced])
    
    extreme_xgb = xgb.XGBClassifier(
        n_estimators=2000,
        max_depth=10,
        learning_rate=0.03,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=0.1,
        random_state=42,
        eval_metric='mlogloss',
        tree_method='hist'
    )
    
    extreme_xgb.fit(
        X_train_balanced, y_train_balanced,
        sample_weight=sample_weights,
        eval_set=[(X_test, y_test)],
        verbose=False
    )
    
    xgb_pred = extreme_xgb.predict(X_test)
    xgb_acc = accuracy_score(y_test, xgb_pred)
    
    models['ExtremeXGBoost'] = extreme_xgb
    results['ExtremeXGBoost'] = {
        'accuracy': xgb_acc,
        'f1_score': f1_score(y_test, xgb_pred, average='weighted'),
        'classification_report': classification_report(y_test, xgb_pred, output_dict=True)
    }
    
    print(f"     极端XGBoost准确率: {xgb_acc:.4f} ({xgb_acc*100:.2f}%)")
    
    # 检查Dementia识别
    xgb_report = classification_report(y_test, xgb_pred, output_dict=True)
    if '0' in xgb_report:
        dem_recall = xgb_report['0']['recall']
        dem_precision = xgb_report['0']['precision']
        print(f"     Dementia: 精确率={dem_precision:.3f}, 召回率={dem_recall:.3f}")
    
except ImportError:
    print("     XGBoost不可用")
except Exception as e:
    print(f"     失败: {e}")

# 模型3: 深度学习 with extreme regularization
print("   训练极端深度学习...")
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from tensorflow.keras.regularizers import l1_l2
    
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    extreme_nn = Sequential([
        Dense(1024, activation='relu', input_shape=(X_train_balanced.shape[1],), 
              kernel_regularizer=l1_l2(l1=1e-4, l2=1e-3)),
        BatchNormalization(),
        Dropout(0.6),
        
        Dense(512, activation='relu', kernel_regularizer=l1_l2(l1=1e-4, l2=1e-3)),
        BatchNormalization(),
        Dropout(0.5),
        
        Dense(256, activation='relu', kernel_regularizer=l1_l2(l1=1e-4, l2=1e-3)),
        BatchNormalization(),
        Dropout(0.4),
        
        Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-4, l2=1e-3)),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(64, activation='relu'),
        BatchNormalization(),
        Dropout(0.2),
        
        Dense(3, activation='softmax')
    ])
    
    extreme_nn.compile(
        optimizer=Adam(learning_rate=0.0003),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    callbacks = [
        EarlyStopping(monitor='val_accuracy', patience=30, restore_best_weights=True),
        ReduceLROnPlateau(monitor='val_loss', factor=0.2, patience=20, min_lr=1e-8)
    ]
    
    extreme_nn.fit(
        X_train_balanced, y_train_balanced,
        validation_split=0.2,
        epochs=300,
        batch_size=32,
        class_weight=class_weight_dict,
        callbacks=callbacks,
        verbose=1
    )
    
    nn_proba = extreme_nn.predict(X_test)
    nn_pred = np.argmax(nn_proba, axis=1)
    nn_acc = accuracy_score(y_test, nn_pred)
    
    models['ExtremeDeepLearning'] = extreme_nn
    results['ExtremeDeepLearning'] = {
        'accuracy': nn_acc,
        'f1_score': f1_score(y_test, nn_pred, average='weighted'),
        'classification_report': classification_report(y_test, nn_pred, output_dict=True)
    }
    
    print(f"     极端深度学习准确率: {nn_acc:.4f} ({nn_acc*100:.2f}%)")
    
    # 检查Dementia识别
    nn_report = classification_report(y_test, nn_pred, output_dict=True)
    if '0' in nn_report:
        dem_recall = nn_report['0']['recall']
        dem_precision = nn_report['0']['precision']
        print(f"     Dementia: 精确率={dem_precision:.3f}, 召回率={dem_recall:.3f}")
    
except Exception as e:
    print(f"     失败: {e}")

# 突破性策略5: 分层集成
print(f"\n💥 突破性策略5: 分层集成...")

try:
    if len(results) >= 2:
        # 第一层: 二分类器识别Dementia
        dementia_predictions = []
        
        for name, model in models.items():
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X_test)
                # Dementia概率
                dementia_prob = proba[:, 0]
            else:
                pred = model.predict(X_test)
                dementia_prob = (pred == 0).astype(float)
            
            dementia_predictions.append(dementia_prob)
        
        # 集成Dementia预测
        ensemble_dementia_prob = np.mean(dementia_predictions, axis=0)
        
        # 第二层: 对于非Dementia样本，使用MCI vs Normal分类器
        final_predictions = []
        
        for i, dem_prob in enumerate(ensemble_dementia_prob):
            if dem_prob > 0.3:  # 降低阈值以提高Dementia召回率
                final_predictions.append(0)  # Dementia
            else:
                # 使用MCI vs Normal分类器
                sample = X_test[i:i+1]
                mci_normal_pred = mci_normal_rf.predict(sample)[0]
                if mci_normal_pred == 1:
                    final_predictions.append(2)  # Normal
                else:
                    final_predictions.append(1)  # MCI
        
        final_predictions = np.array(final_predictions)
        hierarchical_acc = accuracy_score(y_test, final_predictions)
        
        results['HierarchicalEnsemble'] = {
            'accuracy': hierarchical_acc,
            'f1_score': f1_score(y_test, final_predictions, average='weighted'),
            'classification_report': classification_report(y_test, final_predictions, output_dict=True)
        }
        
        print(f"   分层集成准确率: {hierarchical_acc:.4f} ({hierarchical_acc*100:.2f}%)")
        
        # 检查Dementia识别
        hier_report = classification_report(y_test, final_predictions, output_dict=True)
        if '0' in hier_report:
            dem_recall = hier_report['0']['recall']
            dem_precision = hier_report['0']['precision']
            print(f"   Dementia: 精确率={dem_precision:.3f}, 召回率={dem_recall:.3f}")
    
except Exception as e:
    print(f"   失败: {e}")

# 显示突破性结果
print(f"\n" + "="*80)
print("💥 突破性解决方案结果")
print("="*80)
print(f"{'方法':<25} {'准确率':<10} {'F1分数':<10} {'Dementia召回':<12}")
print("-"*80)

best_accuracy = 0
best_method = ""
best_dementia_recall = 0

for method, result in results.items():
    accuracy = result['accuracy']
    f1 = result['f1_score']
    
    # 提取Dementia召回率
    dem_recall = 0
    if 'classification_report' in result and '0' in result['classification_report']:
        dem_recall = result['classification_report']['0']['recall']
    
    status = "🏆" if accuracy >= 0.90 else "✅" if accuracy >= 0.85 else "📈" if accuracy >= 0.75 else "⚠️"
    dem_status = "🎯" if dem_recall >= 0.7 else "📈" if dem_recall >= 0.3 else "❌"
    
    print(f"{method:<25} {accuracy:<10.4f} {f1:<10.4f} {dem_recall:<12.4f} {status}{dem_status}")
    
    # 综合评分: 70%准确率 + 30%Dementia召回率
    combined_score = 0.7 * accuracy + 0.3 * dem_recall
    
    if combined_score > (0.7 * best_accuracy + 0.3 * best_dementia_recall):
        best_accuracy = accuracy
        best_method = method
        best_dementia_recall = dem_recall

print("-"*80)
print(f"🏆 最佳突破性方法: {best_method}")
print(f"🎯 准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
print(f"🎯 Dementia召回率: {best_dementia_recall:.4f} ({best_dementia_recall*100:.2f}%)")

# 保存突破性模型
if best_accuracy >= 0.85 or best_dementia_recall >= 0.5:
    print(f"\n💾 保存突破性模型...")
    
    os.makedirs(output_path, exist_ok=True)
    
    # 保存最佳模型
    if best_method in models:
        best_model = models[best_method]
        
        if hasattr(best_model, 'save'):
            best_model.save(os.path.join(output_path, "breakthrough_model.h5"))
            model_type = "deep_learning"
        else:
            import joblib
            joblib.dump(best_model, os.path.join(output_path, "breakthrough_model.pkl"))
            model_type = "machine_learning"
    
    # 保存突破性报告
    import json
    breakthrough_report = {
        'breakthrough_solution': {
            'best_method': best_method,
            'overall_accuracy': float(best_accuracy),
            'dementia_recall': float(best_dementia_recall),
            'used_extreme_smote': use_smote,
            'extreme_weights': class_weight_dict,
            'training_time_hours': (time.time() - start_time) / 3600,
            'strategies_used': [
                'extreme_smote_oversampling',
                'extreme_class_weights',
                'multi_stage_training',
                'ensemble_multiple_models',
                'hierarchical_classification'
            ]
        },
        'all_results': {k: {key: float(val) if isinstance(val, (int, float, np.number)) else val 
                          for key, val in v.items() if key != 'classification_report'} 
                       for k, v in results.items()},
        'breakthrough_analysis': {
            'target_achieved': best_accuracy >= 0.90,
            'dementia_detection_breakthrough': best_dementia_recall >= 0.5,
            'medical_readiness': best_accuracy >= 0.85 and best_dementia_recall >= 0.7,
            'overall_assessment': 'Revolutionary' if best_accuracy >= 0.90 else 'Breakthrough' if best_accuracy >= 0.85 else 'Significant Progress'
        }
    }
    
    with open(os.path.join(output_path, "breakthrough_report.json"), "w", encoding='utf-8') as f:
        json.dump(breakthrough_report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 突破性模型已保存到: {output_path}")
    
    if best_accuracy >= 0.90 and best_dementia_recall >= 0.7:
        print("🎉 革命性突破! 达到90%+准确率且Dementia识别优秀!")
    elif best_accuracy >= 0.85:
        print("💥 重大突破! 达到85%+准确率!")
    elif best_dementia_recall >= 0.5:
        print("🎯 Dementia识别突破! 召回率达到50%+!")
    else:
        print("📈 显著进展!")

else:
    print(f"📈 当前最佳准确率: {best_accuracy*100:.2f}%")
    print(f"📈 Dementia召回率: {best_dementia_recall*100:.2f}%")

total_time = (time.time() - start_time) / 3600
print(f"\n⏰ 突破性训练耗时: {total_time:.2f}小时")
print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("💥 突破性解决方案完成!")
