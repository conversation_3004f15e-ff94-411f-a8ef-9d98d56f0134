"""
🔍 EEG数据集结构探索工具
用于分析实际的EEG.zip文件内容和结构
"""

import os
import zipfile
import json
import pandas as pd
from pathlib import Path

def explore_zip_structure(zip_path):
    """探索ZIP文件结构"""
    print(f"🔍 探索ZIP文件: {zip_path}")
    
    if not os.path.exists(zip_path):
        print(f"❌ 文件不存在: {zip_path}")
        return None
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            print(f"📊 ZIP文件信息:")
            print(f"   文件总数: {len(file_list)}")
            print(f"   压缩大小: {os.path.getsize(zip_path) / (1024*1024):.2f} MB")
            
            # 分析文件类型
            file_types = {}
            for file_path in file_list:
                ext = Path(file_path).suffix.lower()
                file_types[ext] = file_types.get(ext, 0) + 1
            
            print(f"\n📋 文件类型统计:")
            for ext, count in sorted(file_types.items()):
                if ext:
                    print(f"   {ext}: {count} 个文件")
                else:
                    print(f"   文件夹: {count} 个")
            
            # 显示目录结构
            print(f"\n📁 目录结构 (前50个条目):")
            for i, file_path in enumerate(file_list[:50]):
                level = file_path.count('/')
                indent = "  " * level
                name = os.path.basename(file_path) if file_path.endswith('/') else file_path.split('/')[-1]
                if file_path.endswith('/'):
                    print(f"{indent}📁 {name}")
                else:
                    size_info = ""
                    try:
                        info = zip_ref.getinfo(file_path)
                        size_mb = info.file_size / (1024*1024)
                        if size_mb > 1:
                            size_info = f" ({size_mb:.1f}MB)"
                        elif info.file_size > 1024:
                            size_info = f" ({info.file_size/1024:.1f}KB)"
                    except:
                        pass
                    print(f"{indent}📄 {name}{size_info}")
            
            if len(file_list) > 50:
                print(f"   ... 还有 {len(file_list) - 50} 个文件/文件夹")
            
            return file_list
            
    except Exception as e:
        print(f"❌ 读取ZIP文件失败: {e}")
        return None

def analyze_eeg_files(zip_path):
    """分析EEG相关文件"""
    print(f"\n🧠 分析EEG文件...")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            # 查找EEG相关文件
            eeg_files = []
            json_files = []
            other_files = []
            
            for file_path in file_list:
                if file_path.endswith('.set'):
                    eeg_files.append(file_path)
                elif file_path.endswith('.fdt'):
                    eeg_files.append(file_path)
                elif file_path.endswith('.json'):
                    json_files.append(file_path)
                elif not file_path.endswith('/'):
                    other_files.append(file_path)
            
            print(f"📊 EEG文件统计:")
            print(f"   .set文件: {len([f for f in eeg_files if f.endswith('.set')])} 个")
            print(f"   .fdt文件: {len([f for f in eeg_files if f.endswith('.fdt')])} 个")
            print(f"   .json文件: {len(json_files)} 个")
            print(f"   其他文件: {len(other_files)} 个")
            
            # 分析被试者结构
            subjects = set()
            for file_path in eeg_files:
                if 'sub-' in file_path:
                    # 提取被试者ID
                    parts = file_path.split('/')
                    for part in parts:
                        if part.startswith('sub-'):
                            subjects.add(part)
                            break
            
            print(f"\n👥 被试者信息:")
            print(f"   被试者数量: {len(subjects)}")
            if subjects:
                sorted_subjects = sorted(list(subjects))
                print(f"   被试者范围: {sorted_subjects[0]} 到 {sorted_subjects[-1]}")
                print(f"   前10个被试者: {sorted_subjects[:10]}")
            
            # 分析JSON文件内容（如果有的话）
            if json_files:
                print(f"\n📋 JSON文件分析:")
                sample_json = json_files[0]
                try:
                    with zip_ref.open(sample_json) as f:
                        json_content = json.load(f)
                        print(f"   示例JSON文件: {sample_json}")
                        print(f"   JSON键值: {list(json_content.keys())}")
                        
                        # 查找可能的标签信息
                        for key, value in json_content.items():
                            if any(keyword in key.lower() for keyword in ['diagnosis', 'label', 'group', 'condition']):
                                print(f"   可能的标签字段: {key} = {value}")
                                
                except Exception as e:
                    print(f"   JSON解析失败: {e}")
            
            return {
                'eeg_files': eeg_files,
                'json_files': json_files,
                'subjects': sorted(list(subjects)),
                'other_files': other_files
            }
            
    except Exception as e:
        print(f"❌ EEG文件分析失败: {e}")
        return None

def extract_sample_files(zip_path, output_dir="sample_extraction"):
    """提取样本文件进行详细分析"""
    print(f"\n📦 提取样本文件到: {output_dir}")
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            # 提取第一个被试者的所有文件
            first_subject_files = []
            for file_path in file_list:
                if 'sub-001' in file_path or (file_list and 'sub-' in file_list[0]):
                    first_subject_files.append(file_path)
            
            if first_subject_files:
                print(f"🔍 提取第一个被试者的文件:")
                for file_path in first_subject_files[:5]:  # 只提取前5个文件
                    if not file_path.endswith('/'):
                        try:
                            zip_ref.extract(file_path, output_dir)
                            print(f"   ✅ {file_path}")
                        except Exception as e:
                            print(f"   ❌ {file_path}: {e}")
                
                # 提取README或描述文件
                readme_files = [f for f in file_list if any(name in f.lower() for name in ['readme', 'description', 'dataset_description'])]
                for readme in readme_files[:3]:
                    if not readme.endswith('/'):
                        try:
                            zip_ref.extract(readme, output_dir)
                            print(f"   ✅ {readme}")
                        except:
                            pass
                            
                print(f"✅ 样本文件已提取到: {output_dir}")
                return True
            else:
                print("❌ 未找到被试者文件")
                return False
                
    except Exception as e:
        print(f"❌ 提取失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 EEG数据集结构探索工具")
    print("=" * 50)
    
    # 可能的ZIP文件位置
    possible_paths = [
        "/root/autodl-tmp/open-nuro-dataset.zip",
        "/root/autodl-tmp/EEG.zip",
        "open-nuro-dataset.zip",
        "EEG.zip"
    ]
    
    zip_path = None
    for path in possible_paths:
        if os.path.exists(path):
            zip_path = path
            break
    
    if not zip_path:
        print("❌ 未找到EEG数据集ZIP文件")
        print("请确保以下文件之一存在:")
        for path in possible_paths:
            print(f"   - {path}")
        return
    
    print(f"✅ 找到数据集: {zip_path}")
    
    # 1. 探索ZIP结构
    file_list = explore_zip_structure(zip_path)
    
    if file_list:
        # 2. 分析EEG文件
        eeg_analysis = analyze_eeg_files(zip_path)
        
        # 3. 提取样本文件
        extract_sample_files(zip_path)
        
        print(f"\n🎯 探索完成!")
        print(f"请查看提取的样本文件以了解具体的数据格式")
    
if __name__ == "__main__":
    main()
