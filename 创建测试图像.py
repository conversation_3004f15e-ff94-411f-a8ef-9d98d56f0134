#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
创建测试图像用于演示验证功能
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os

def create_test_images():
    """创建各种测试图像"""
    
    # 1. 创建一个彩色风景图像（应该被拒绝）
    landscape = Image.new('RGB', (400, 300), color='skyblue')
    draw = ImageDraw.Draw(landscape)
    
    # 画一些简单的风景元素
    # 太阳
    draw.ellipse([320, 30, 370, 80], fill='yellow')
    # 山
    draw.polygon([(0, 200), (150, 100), (300, 200), (0, 200)], fill='green')
    # 云
    draw.ellipse([50, 50, 120, 90], fill='white')
    draw.ellipse([80, 40, 150, 80], fill='white')
    
    landscape.save('测试图像_风景.jpg')
    print("✅ 创建了彩色风景图像")
    
    # 2. 创建一个人物照片（应该被拒绝）
    portrait = Image.new('RGB', (300, 400), color='lightblue')
    draw = ImageDraw.Draw(portrait)
    
    # 简单的人脸
    draw.ellipse([100, 100, 200, 200], fill='peachpuff')  # 脸
    draw.ellipse([120, 130, 130, 140], fill='black')      # 左眼
    draw.ellipse([170, 130, 180, 140], fill='black')      # 右眼
    draw.ellipse([145, 160, 155, 170], fill='pink')       # 鼻子
    draw.arc([130, 170, 170, 190], 0, 180, fill='red')    # 嘴巴
    
    portrait.save('测试图像_人物.jpg')
    print("✅ 创建了人物照片")
    
    # 3. 创建一个模拟的医学影像（应该通过验证）
    medical = Image.new('L', (300, 300), color=0)  # 灰度图像
    draw = ImageDraw.Draw(medical)
    
    # 模拟脑部扫描的圆形结构
    draw.ellipse([50, 50, 250, 250], fill=128, outline=200)
    draw.ellipse([100, 100, 200, 200], fill=64, outline=150)
    draw.ellipse([130, 130, 170, 170], fill=32, outline=100)
    
    # 添加一些噪点模拟医学影像特征
    for _ in range(100):
        x = np.random.randint(0, 300)
        y = np.random.randint(0, 300)
        brightness = np.random.randint(0, 255)
        draw.point((x, y), fill=brightness)
    
    medical.save('测试图像_医学影像.jpg')
    print("✅ 创建了模拟医学影像")
    
    # 4. 创建一个纯色图像（应该被拒绝）
    solid = Image.new('RGB', (200, 200), color='red')
    solid.save('测试图像_纯色.jpg')
    print("✅ 创建了纯色图像")
    
    # 5. 创建一个低分辨率图像（应该有警告）
    low_res = Image.new('RGB', (50, 50), color='blue')
    low_res.save('测试图像_低分辨率.jpg')
    print("✅ 创建了低分辨率图像")

def create_usage_guide():
    """创建使用指南"""
    guide = """
# 🧪 图像验证功能测试指南

## 📁 测试图像说明

### ✅ 应该通过验证的图像：
- `测试图像_医学影像.jpg` - 模拟的灰度医学影像

### ⚠️ 应该触发警告的图像：
- `测试图像_风景.jpg` - 彩色风景图（非医学影像）
- `测试图像_人物.jpg` - 人物照片（非医学影像）
- `测试图像_纯色.jpg` - 纯色图像（缺乏细节）
- `测试图像_低分辨率.jpg` - 低分辨率图像

## 🔍 测试步骤

1. **启动AI痴呆症识别器**
2. **测试正常医学影像**：
   - 选择 `测试图像_医学影像.jpg`
   - 应该直接进行分析，无警告

3. **测试问题图像**：
   - 选择其他测试图像
   - 应该显示验证警告对话框
   - 可以选择"是"强制分析或"否"取消

4. **观察结果显示**：
   - 强制分析的结果会显示橙色警告
   - 正常分析显示白色文字

## 📹 摄像头功能测试

1. **启动摄像头**
2. **查看使用说明**
3. **拍照分析**：
   - 点击"拍照分析"
   - 会显示重要提醒
   - 确认后进行分析

## 🎯 验证原理

程序会检查：
- 图像是否为彩色（医学影像通常是灰度）
- 分辨率是否足够
- 亮度分布是否正常
- 边缘细节是否适中
- 是否具有医学影像特征

## ⚠️ 重要说明

这些验证只是基本检查，无法100%确定图像类型。
最终还是需要用户的专业判断。
"""
    
    with open('图像验证测试指南.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("✅ 创建了测试指南")

if __name__ == "__main__":
    print("🧪 创建测试图像...")
    create_test_images()
    create_usage_guide()
    print("\n🎉 测试图像创建完成！")
    print("📖 请查看 '图像验证测试指南.md' 了解使用方法")
