#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI痴呆症识别器打包脚本
将GUI应用程序打包成Windows可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['AI痴呆症识别器_GUI.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('D:/模型开发/升级model.h5', '.'),
    ],
    hiddenimports=[
        'tensorflow',
        'tensorflow.keras',
        'tensorflow.keras.preprocessing',
        'tensorflow.keras.preprocessing.image',
        'customtkinter',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'cv2',
        'numpy',
        'fpdf',
        'reportlab',
        'reportlab.lib',
        'reportlab.lib.pagesizes',
        'reportlab.platypus',
        'reportlab.lib.styles',
        'reportlab.lib.units',
        'absl',
        'absl.logging',
        'darkdetect',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI痴呆症识别器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('AI痴呆症识别器.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 规格文件已创建")

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 使用PyInstaller构建
        cmd = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=AI痴呆症识别器',
            '--add-data=D:/模型开发/升级model.h5;.',
            '--hidden-import=tensorflow',
            '--hidden-import=customtkinter',
            '--hidden-import=PIL',
            '--hidden-import=cv2',
            '--hidden-import=fpdf',
            '--hidden-import=reportlab',
            '--hidden-import=absl.logging',
            '--hidden-import=darkdetect',
            'AI痴呆症识别器_GUI.py'
        ]
        
        # 如果有图标文件，添加图标
        if os.path.exists('icon.ico'):
            cmd.extend(['--icon=icon.ico'])
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ 构建成功！")
            print(f"📁 可执行文件位置: {os.path.abspath('dist/AI痴呆症识别器.exe')}")
        else:
            print("❌ 构建失败！")
            print("错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''
@echo off
chcp 65001 >nul
echo 🚀 AI痴呆症识别器安装程序
echo ================================
echo.

set "INSTALL_DIR=%USERPROFILE%\\Desktop\\AI痴呆症识别器"

echo 📁 创建安装目录...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 复制程序文件...
copy "AI痴呆症识别器.exe" "%INSTALL_DIR%\\" >nul
if exist "升级model.h5" copy "升级model.h5" "%INSTALL_DIR%\\" >nul

echo 🔗 创建桌面快捷方式...
set "SHORTCUT=%USERPROFILE%\\Desktop\\AI痴呆症识别器.lnk"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\AI痴呆症识别器.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'AI痴呆症识别器 - 基于深度学习的医学影像分析'; $Shortcut.Save()"

echo.
echo ✅ 安装完成！
echo 📍 程序已安装到: %INSTALL_DIR%
echo 🖥️ 桌面快捷方式已创建
echo.
echo 🎯 现在可以通过桌面快捷方式启动程序
echo.
pause
'''
    
    with open('install.bat', 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print("✅ 安装脚本已创建")

def create_readme():
    """创建说明文档"""
    readme_content = '''# AI痴呆症识别器

## 🧠 软件介绍

AI痴呆症识别器是一款基于深度学习的医学影像智能分析系统，能够通过分析医学影像来辅助痴呆症的早期检测和诊断。

## ✨ 主要功能

### 🖼️ 图像分析
- 支持多种图像格式（JPG, PNG, BMP等）
- 智能图像预处理和优化
- 高精度AI模型预测

### 📹 实时摄像头分析
- 实时摄像头图像捕获
- 实时AI分析和结果显示
- 可切换的实时分析模式

### 📊 结果可视化
- 详细的概率分布显示
- 直观的置信度指示
- 专业的结果解释

### 📄 报告生成
- 专业PDF诊断报告
- 详细的分析结果记录
- 医学建议和免责声明

### 📋 历史记录
- 完整的分析历史记录
- 结果数据导出功能
- 便于追踪和对比

## 🚀 使用方法

### 安装
1. 运行 `install.bat` 进行自动安装
2. 或直接运行 `AI痴呆症识别器.exe`

### 基本使用
1. **选择图像**: 点击"选择影像文件"按钮选择要分析的医学影像
2. **开始分析**: 点击"开始AI分析"进行智能分析
3. **查看结果**: 在右侧面板查看详细的分析结果
4. **生成报告**: 点击"生成PDF报告"创建专业诊断报告

### 实时分析
1. **启动摄像头**: 点击"启动摄像头"按钮
2. **开启实时分析**: 切换"实时分析"开关
3. **查看实时结果**: 在界面上查看实时分析结果

## ⚠️ 重要声明

**本软件仅供研究和参考使用，不能替代专业医学诊断。请咨询专业医生获取准确诊断和治疗建议。**

## 🔧 系统要求

- Windows 10/11 (64位)
- 内存: 4GB以上推荐
- 硬盘空间: 2GB以上
- 摄像头: 可选（用于实时分析功能）

## 📞 技术支持

如有技术问题或建议，请联系开发团队。

## 📄 许可证

本软件免费提供，仅供学习和研究使用。

---

© 2024 AI Medical Solutions
版本: 1.0
'''
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 说明文档已创建")

def main():
    """主函数"""
    print("🚀 AI痴呆症识别器打包工具")
    print("=" * 40)
    
    # 检查必要文件
    if not os.path.exists('AI痴呆症识别器_GUI.py'):
        print("❌ 找不到主程序文件: AI痴呆症识别器_GUI.py")
        return
    
    if not os.path.exists('D:/模型开发/升级model.h5'):
        print("⚠️ 警告: 找不到模型文件: D:/模型开发/升级model.h5")
        print("   程序仍会继续打包，但运行时可能需要手动指定模型路径")
    
    print("\n📋 开始打包流程...")
    
    # 创建规格文件
    create_spec_file()
    
    # 构建可执行文件
    build_executable()
    
    # 创建安装脚本
    create_installer_script()
    
    # 创建说明文档
    create_readme()
    
    print("\n🎉 打包完成！")
    print("\n📁 生成的文件:")
    print("   - dist/AI痴呆症识别器.exe (主程序)")
    print("   - install.bat (安装脚本)")
    print("   - README.md (使用说明)")
    
    print("\n🎯 使用方法:")
    print("   1. 将 dist 文件夹中的所有文件复制到目标计算机")
    print("   2. 运行 install.bat 进行安装")
    print("   3. 或直接运行 AI痴呆症识别器.exe")

if __name__ == "__main__":
    main()
