# -*- coding: utf-8 -*-
"""
正确双模型症状分析系统
重点：CT图像症状分析（4种症状分类）
HTML功能：完全照搬现有GUI的HTML生成功能
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np
from fpdf import FPDF
import warnings

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 抑制警告
warnings.filterwarnings('ignore')

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CTSymptomAnalysisSystem:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🧠 CT症状分析系统 - 双模型智能版 v2.0")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.ct_validation_model = None     # CT验证模型（辅助）
        self.symptom_analysis_model = None  # 症状分析模型（核心）
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        
        # 模型路径
        self.ct_validation_path = r"D:\模型开发\ct_other_model.h5"  # CT验证（辅助）
        self.symptom_analysis_path = r"D:\模型开发\ct_class.h5"     # 症状分析（核心）
        
        # 症状分类标签（核心功能）
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_models_async()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 CT症状分析系统",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="专业CT图像痴呆症症状智能分析 | 四种症状精准识别",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=400)
        
        self.create_left_panel()
        self.create_right_panel()
        
        # 底部状态栏
        self.create_status_bar()
        
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        # 图像显示标题
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 CT图像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 默认显示
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择CT图像进行症状分析\n\n支持格式: JPG, PNG, BMP\n\n系统将分析4种痴呆症症状类型",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
        
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 模型状态显示
        self.create_model_status_panel()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 症状分析结果显示区域
        self.create_symptom_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
        
    def create_model_status_panel(self):
        """创建模型状态面板"""
        status_frame = ctk.CTkFrame(self.right_panel)
        status_frame.pack(fill="x", padx=20, pady=20)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="🤖 双模型状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 10))
        
        # 症状分析模型状态（核心）
        self.symptom_analysis_status = ctk.CTkLabel(
            status_frame,
            text="症状分析模型: 🔴 未加载",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.symptom_analysis_status.pack(pady=2)
        
        # CT验证模型状态（辅助）
        self.ct_validation_status = ctk.CTkLabel(
            status_frame,
            text="CT验证模型: 🔴 未加载",
            font=ctk.CTkFont(size=11)
        )
        self.ct_validation_status.pack(pady=2)
        
        # 系统整体状态
        self.system_status = ctk.CTkLabel(
            status_frame,
            text="系统状态: ⏳ 初始化中...",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.system_status.pack(pady=(10, 15))
        
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # 选择CT图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择CT图像",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 开始症状分析按钮（核心功能）
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🧠 分析症状类型",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.start_symptom_analysis,
            state="disabled",
            fg_color="red"
        )
        self.analyze_btn.pack(fill="x", pady=10)

        # 摄像头控制按钮
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=10)

        # 拍照分析按钮
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="📸 拍照分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=10)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(control_frame)
        self.progress.pack(fill="x", pady=10)
        self.progress.set(0)
        
    def create_symptom_results_panel(self):
        """创建症状分析结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 结果标题
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 症状分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # 症状分析结果（核心显示）
        self.symptom_result_label = ctk.CTkLabel(
            results_frame,
            text="症状类型: 等待分析...",
            font=ctk.CTkFont(size=16, weight="bold"),
            wraplength=320
        )
        self.symptom_result_label.pack(pady=15)
        
        # 置信度显示
        self.confidence_label = ctk.CTkLabel(
            results_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.confidence_label.pack(pady=5)
        
        # 详细概率显示框（4种症状）
        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=120)
        self.details_frame.pack(fill="x", padx=10, pady=10)
        
        # CT验证结果（辅助信息，小字显示）
        self.ct_validation_label = ctk.CTkLabel(
            results_frame,
            text="CT验证: 等待验证...",
            font=ctk.CTkFont(size=10),
            wraplength=320,
            text_color="gray"
        )
        self.ct_validation_label.pack(pady=2)
        
    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=20, pady=10)
        
        # 保存结果按钮
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled"
        )
        self.save_btn.pack(fill="x", pady=3)

        # 生成PDF报告按钮
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="orange"
        )
        self.pdf_btn.pack(fill="x", pady=3)

        # 生成HTML报告按钮（完全照搬现有功能）
        self.html_btn = ctk.CTkButton(
            func_frame,
            text="🌐 生成HTML报告",
            command=self.generate_html_report,
            state="disabled",
            fg_color="purple"
        )
        self.html_btn.pack(fill="x", pady=3)
        
        # 查看历史按钮
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history
        )
        self.history_btn.pack(fill="x", pady=3)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about
        )
        self.about_btn.pack(fill="x", pady=3)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载症状分析系统...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # 版本信息
        self.version_label = ctk.CTkLabel(
            self.status_frame,
            text="v2.0 症状分析专业版",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.version_label.pack(side="right", padx=20, pady=10)
