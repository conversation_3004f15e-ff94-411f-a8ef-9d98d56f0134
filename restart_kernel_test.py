"""
🔄 重启内核后的cuDNN测试
解决"Physical devices cannot be modified"问题
"""

def test_cudnn_fresh():
    """在新的Python进程中测试cuDNN"""
    print("🧪 测试cuDNN (新进程)")
    print("=" * 40)
    
    try:
        import tensorflow as tf
        print(f"📊 TensorFlow版本: {tf.__version__}")
        
        # 检查GPU
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 GPU数量: {len(gpus)}")
        
        if gpus:
            # 设置GPU内存增长
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("✅ GPU内存增长设置成功")
            
            # 测试简单的卷积操作
            print("🧪 测试卷积操作...")
            x = tf.random.normal((1, 19, 128, 1))
            conv = tf.keras.layers.Conv2D(32, (1, 7), padding='same', activation='relu')
            y = conv(x)
            print(f"✅ 卷积测试成功: {x.shape} -> {y.shape}")
            
            # 测试更复杂的模型
            print("🧪 测试完整模型...")
            model = tf.keras.Sequential([
                tf.keras.layers.Conv2D(16, (1, 5), padding='same', activation='relu', 
                                     input_shape=(19, 128, 1)),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.Conv2D(16, (19, 1), activation='relu'),
                tf.keras.layers.GlobalAveragePooling2D(),
                tf.keras.layers.Dense(3, activation='softmax')
            ])
            
            model.compile(optimizer='adam', loss='categorical_crossentropy', metrics=['accuracy'])
            
            # 创建测试数据
            import numpy as np
            X_test = np.random.randn(5, 19, 128, 1).astype(np.float32)
            y_test = tf.keras.utils.to_categorical(np.random.randint(0, 3, 5), 3)
            
            # 测试训练
            history = model.fit(X_test, y_test, epochs=1, verbose=0)
            
            # 测试预测
            predictions = model.predict(X_test, verbose=0)
            
            print("🎉 cuDNN完整测试成功!")
            print(f"📊 模型参数: {model.count_params():,}")
            print(f"📈 训练损失: {history.history['loss'][0]:.4f}")
            
            return True
        else:
            print("❌ 未检测到GPU")
            return False
            
    except Exception as e:
        print(f"❌ cuDNN测试失败: {e}")
        if "DNN library is not found" in str(e):
            print("💡 这是cuDNN库缺失问题")
            print("🔧 建议使用CPU训练器")
        elif "Physical devices cannot be modified" in str(e):
            print("💡 这是GPU设备已初始化问题")
            print("🔄 请重启Jupyter内核后重试")
        return False

def quick_gpu_test():
    """快速GPU测试"""
    print("⚡ 快速GPU测试")
    print("=" * 30)
    
    try:
        import tensorflow as tf
        
        # 简单测试，不设置GPU内存
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 GPU数量: {len(gpus)}")
        
        if gpus:
            # 不设置内存增长，直接测试
            x = tf.constant([[1.0, 2.0], [3.0, 4.0]])
            y = tf.matmul(x, x)
            print(f"✅ 基础GPU计算成功: {y.numpy()}")
            
            # 测试卷积
            x = tf.random.normal((1, 10, 10, 1))
            conv = tf.keras.layers.Conv2D(1, 3)
            y = conv(x)
            print(f"✅ 卷积计算成功: {y.shape}")
            
            return True
        else:
            print("❌ 未检测到GPU")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔄 cuDNN重启内核测试")
    print("=" * 50)
    
    print("💡 如果您看到'Physical devices cannot be modified'错误:")
    print("   1. 在Jupyter中点击 Kernel -> Restart Kernel")
    print("   2. 重新运行这个脚本")
    print()
    
    # 先尝试快速测试
    if quick_gpu_test():
        print("\n🎯 快速测试通过，尝试完整测试...")
        if test_cudnn_fresh():
            print("\n🎉 cuDNN完全正常!")
            print("🚀 可以运行GPU训练: python complete_deep_eeg_trainer.py")
        else:
            print("\n⚠️ 完整测试失败，但基础GPU功能正常")
            print("💡 建议使用CPU训练器: python cpu_only_eeg_trainer.py")
    else:
        print("\n❌ GPU基础功能测试失败")
        print("💻 建议使用CPU训练器: python cpu_only_eeg_trainer.py")

if __name__ == "__main__":
    main()
