"""
📊 患者分布分析器
详细分析每个数据集中的患者类型分布
"""

import os
from collections import Counter

def analyze_split_distribution():
    """分析每个数据集的患者分布"""
    print("📊 EEG数据集患者分布详细分析")
    print("=" * 60)
    
    # 标签映射
    label_mapping = {
        'A': '阿尔茨海默病患者 (AD)',
        'C': '健康对照组 (正常人)',
        'F': '额颞叶痴呆患者 (FTD)'
    }
    
    splits = ['train', 'val', 'test']
    
    for split_name in splits:
        print(f"\n🔍 {split_name.upper()}集详细分析:")
        print("-" * 40)
        
        # 读取患者列表
        patient_file = f"EEG_splits/{split_name}/patient_list.txt"
        
        if not os.path.exists(patient_file):
            print(f"❌ 文件不存在: {patient_file}")
            continue
        
        patients_by_label = {'A': [], 'C': [], 'F': []}
        
        with open(patient_file, 'r', encoding='utf-8') as f:
            lines = f.read().strip().split('\n')
            
            for line in lines[1:]:  # 跳过表头
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        subject_id = parts[0].strip()
                        label = parts[1].strip()
                        if label in patients_by_label:
                            patients_by_label[label].append(subject_id)
        
        # 统计和显示
        total_patients = sum(len(patients) for patients in patients_by_label.values())
        
        print(f"📋 总患者数: {total_patients}")
        print()
        
        for label, patients in patients_by_label.items():
            if patients:
                count = len(patients)
                percentage = (count / total_patients) * 100
                label_name = label_mapping[label]
                
                print(f"🏷️ {label} - {label_name}:")
                print(f"   数量: {count} 人 ({percentage:.1f}%)")
                
                # 按编号排序
                sorted_patients = sorted(patients, key=lambda x: int(x.split('-')[1]))
                
                # 显示患者ID范围
                if sorted_patients:
                    first_num = int(sorted_patients[0].split('-')[1])
                    last_num = int(sorted_patients[-1].split('-')[1])
                    print(f"   范围: {sorted_patients[0]} 到 {sorted_patients[-1]}")
                
                # 显示所有患者ID (分行显示，每行10个)
                print(f"   患者列表:")
                for i in range(0, len(sorted_patients), 10):
                    batch = sorted_patients[i:i+10]
                    print(f"      {', '.join(batch)}")
                
                print()

def create_summary_by_category():
    """按类别汇总所有数据集"""
    print("\n📈 按类别汇总分析")
    print("=" * 60)
    
    label_mapping = {
        'A': '阿尔茨海默病患者 (AD)',
        'C': '健康对照组 (正常人)',
        'F': '额颞叶痴呆患者 (FTD)'
    }
    
    # 收集所有数据
    all_data = {'A': {'train': [], 'val': [], 'test': []},
                'C': {'train': [], 'val': [], 'test': []},
                'F': {'train': [], 'val': [], 'test': []}}
    
    splits = ['train', 'val', 'test']
    
    for split_name in splits:
        patient_file = f"EEG_splits/{split_name}/patient_list.txt"
        
        if os.path.exists(patient_file):
            with open(patient_file, 'r', encoding='utf-8') as f:
                lines = f.read().strip().split('\n')
                
                for line in lines[1:]:  # 跳过表头
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            subject_id = parts[0].strip()
                            label = parts[1].strip()
                            if label in all_data:
                                all_data[label][split_name].append(subject_id)
    
    # 按类别显示
    for label, label_name in label_mapping.items():
        print(f"\n🏷️ {label} - {label_name}:")
        print("-" * 30)
        
        total_count = sum(len(all_data[label][split]) for split in splits)
        print(f"总数: {total_count} 人")
        
        for split_name in splits:
            count = len(all_data[label][split_name])
            percentage = (count / total_count) * 100 if total_count > 0 else 0
            print(f"  {split_name.upper()}集: {count} 人 ({percentage:.1f}%)")
        
        print()

def create_training_reference():
    """创建训练参考文件"""
    print("\n📝 生成训练参考文件")
    print("=" * 60)
    
    # 创建训练参考文档
    reference_content = """# EEG数据集训练参考

## 患者类别说明

### A组 - 阿尔茨海默病患者 (Alzheimer Disease Group)
- 标签编码: 1
- 特征: 记忆力衰退、认知功能下降
- 脑电特征: Alpha波减少、Theta波增加

### C组 - 健康对照组 (Healthy Group)  
- 标签编码: 0
- 特征: 认知功能正常
- 脑电特征: 正常的Alpha、Beta波模式

### F组 - 额颞叶痴呆患者 (Frontotemporal Dementia Group)
- 标签编码: 2  
- 特征: 行为异常、语言障碍
- 脑电特征: 前额叶区域异常

## 训练建议

1. **数据预处理**: 
   - 滤波: 1-40Hz带通滤波
   - 去噪: 去除眼电、肌电干扰
   - 标准化: Z-score标准化

2. **特征提取**:
   - 频域特征: Delta, Theta, Alpha, Beta, Gamma功率
   - 时域特征: 均值、方差、偏度、峰度
   - 连接性特征: 通道间相关性

3. **模型架构**:
   - 输入层: 特征维度
   - 隐藏层: 256 -> 128 -> 64
   - 输出层: 3类分类 (softmax)

4. **训练参数**:
   - 学习率: 0.001
   - 批大小: 16
   - 轮数: 100-150
   - 早停: 验证损失不下降20轮

## 与双模型联用

- EEG模型输出: [健康, AD, FTD]
- MRI模型输出: [无痴呆, 轻度, 中度, 非常轻度]
- 融合策略: 加权平均 (EEG权重0.6, MRI权重0.4)
"""
    
    with open("EEG_splits/training_reference.md", 'w', encoding='utf-8') as f:
        f.write(reference_content)
    
    print("✅ 训练参考文件已生成: EEG_splits/training_reference.md")

def main():
    """主函数"""
    # 1. 详细分析每个数据集
    analyze_split_distribution()
    
    # 2. 按类别汇总
    create_summary_by_category()
    
    # 3. 生成训练参考
    create_training_reference()
    
    print("\n🎉 患者分布分析完成!")
    print("📋 现在您可以清楚地知道每个数据集中的患者类型分布")

if __name__ == "__main__":
    main()
