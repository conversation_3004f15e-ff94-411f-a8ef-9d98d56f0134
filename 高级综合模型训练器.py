"""
高级综合模型训练器
结合深度学习、集成学习、超参数优化等方法
目标准确率: ≥90%
"""

import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model, Sequential
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input, LSTM, Conv1D, GlobalMaxPooling1D, Concatenate
from tensorflow.keras.optimizers import Adam, AdamW
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.regularizers import l1_l2
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler
import optuna
import os
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

# 抑制TensorFlow警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class AdvancedDementiaTrainer:
    def __init__(self, data_path=r"D:\模型开发\audio\processed_datasets"):
        self.data_path = data_path
        self.target_accuracy = 0.90
        self.best_models = {}
        self.best_accuracy = 0
        
        # 设置GPU
        self.setup_gpu()
        
    def setup_gpu(self):
        """设置GPU优化"""
        print("🔧 设置GPU优化...")
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print("✅ GPU优化完成")
            except:
                print("⚠️ GPU设置失败，使用CPU")
        else:
            print("⚠️ 使用CPU训练")
    
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载和准备数据...")
        
        # 加载数据
        train_data = pd.read_csv(os.path.join(self.data_path, "train_set_scaled.csv"))
        val_data = pd.read_csv(os.path.join(self.data_path, "validation_set_scaled.csv"))
        test_data = pd.read_csv(os.path.join(self.data_path, "test_set_scaled.csv"))
        
        with open(os.path.join(self.data_path, "feature_info.json"), 'r', encoding='utf-8') as f:
            self.feature_info = json.load(f)
        
        with open(os.path.join(self.data_path, "label_encoder.pkl"), 'rb') as f:
            self.label_encoder = pickle.load(f)
        
        # 获取特征列
        feature_cols = []
        for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
            if category in self.feature_info:
                feature_cols.extend(self.feature_info[category])
        
        # 准备数据
        self.X_train = train_data[feature_cols].values
        self.y_train = train_data['diagnosis_encoded'].values
        self.X_val = val_data[feature_cols].values
        self.y_val = val_data['diagnosis_encoded'].values
        self.X_test = test_data[feature_cols].values
        self.y_test = test_data['diagnosis_encoded'].values
        
        print(f"   训练集: {self.X_train.shape}")
        print(f"   验证集: {self.X_val.shape}")
        print(f"   测试集: {self.X_test.shape}")
        
        return True
    
    def advanced_data_augmentation(self):
        """高级数据增强"""
        print("🔧 应用高级数据增强...")
        
        # 原始数据
        X_aug = [self.X_train]
        y_aug = [self.y_train]
        
        # 1. 高斯噪声增强
        for noise_level in [0.01, 0.02]:
            noise = np.random.normal(0, noise_level, self.X_train.shape)
            X_aug.append(self.X_train + noise)
            y_aug.append(self.y_train)
        
        # 2. 特征缩放增强
        for scale_range in [(0.95, 1.05), (0.9, 1.1)]:
            scale = np.random.uniform(scale_range[0], scale_range[1], self.X_train.shape)
            X_aug.append(self.X_train * scale)
            y_aug.append(self.y_train)
        
        # 3. 特征混合增强 (Mixup)
        alpha = 0.2
        for _ in range(2):
            indices = np.random.permutation(len(self.X_train))
            lam = np.random.beta(alpha, alpha, (len(self.X_train), 1))
            mixed_X = lam * self.X_train + (1 - lam) * self.X_train[indices]
            X_aug.append(mixed_X)
            y_aug.append(self.y_train)  # 使用原始标签
        
        # 合并增强数据
        self.X_train_aug = np.vstack(X_aug)
        self.y_train_aug = np.hstack(y_aug)
        
        print(f"   增强后训练集: {self.X_train_aug.shape} (原始: {self.X_train.shape})")
    
    def build_cnn_model(self, trial=None):
        """构建CNN模型"""
        
        # 超参数
        if trial:
            units1 = trial.suggest_int('cnn_units1', 64, 256, step=32)
            units2 = trial.suggest_int('cnn_units2', 32, 128, step=16)
            dropout1 = trial.suggest_float('cnn_dropout1', 0.2, 0.5)
            dropout2 = trial.suggest_float('cnn_dropout2', 0.1, 0.4)
            filters = trial.suggest_int('cnn_filters', 32, 128, step=16)
        else:
            units1, units2 = 128, 64
            dropout1, dropout2 = 0.3, 0.2
            filters = 64
        
        # 重塑数据为CNN输入格式
        input_layer = Input(shape=(self.X_train.shape[1], 1))
        
        # CNN层
        x = Conv1D(filters=filters, kernel_size=3, activation='relu', padding='same')(input_layer)
        x = BatchNormalization()(x)
        x = Dropout(dropout1)(x)
        
        x = Conv1D(filters=filters//2, kernel_size=3, activation='relu', padding='same')(x)
        x = BatchNormalization()(x)
        x = GlobalMaxPooling1D()(x)
        
        # 全连接层
        x = Dense(units1, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(dropout1)(x)
        
        x = Dense(units2, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(dropout2)(x)
        
        output = Dense(3, activation='softmax')(x)
        
        model = Model(inputs=input_layer, outputs=output)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def build_lstm_model(self, trial=None):
        """构建LSTM模型"""
        
        # 超参数
        if trial:
            lstm_units = trial.suggest_int('lstm_units', 32, 128, step=16)
            dense_units = trial.suggest_int('lstm_dense_units', 32, 128, step=16)
            dropout = trial.suggest_float('lstm_dropout', 0.2, 0.5)
        else:
            lstm_units, dense_units = 64, 64
            dropout = 0.3
        
        # 重塑数据为LSTM输入格式
        input_layer = Input(shape=(self.X_train.shape[1], 1))
        
        # LSTM层
        x = LSTM(lstm_units, return_sequences=True, dropout=dropout, recurrent_dropout=dropout)(input_layer)
        x = LSTM(lstm_units//2, dropout=dropout, recurrent_dropout=dropout)(x)
        
        # 全连接层
        x = Dense(dense_units, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = BatchNormalization()(x)
        x = Dropout(dropout)(x)
        
        output = Dense(3, activation='softmax')(x)
        
        model = Model(inputs=input_layer, outputs=output)
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    def build_advanced_dnn(self, trial=None):
        """构建高级深度神经网络"""
        
        # 超参数
        if trial:
            layers = trial.suggest_int('dnn_layers', 3, 6)
            base_units = trial.suggest_int('dnn_base_units', 128, 512, step=64)
            dropout = trial.suggest_float('dnn_dropout', 0.2, 0.5)
        else:
            layers, base_units, dropout = 4, 256, 0.3
        
        model = Sequential()
        model.add(Dense(base_units, activation='relu', input_shape=(self.X_train.shape[1],),
                       kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)))
        model.add(BatchNormalization())
        model.add(Dropout(dropout))
        
        # 动态添加层
        for i in range(layers - 1):
            units = max(base_units // (2 ** i), 32)
            model.add(Dense(units, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)))
            model.add(BatchNormalization())
            model.add(Dropout(dropout * (0.8 ** i)))
        
        model.add(Dense(3, activation='softmax'))
        
        model.compile(
            optimizer=AdamW(learning_rate=0.001, weight_decay=1e-4),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model

    def train_ensemble_ml_models(self):
        """训练集成机器学习模型"""
        print("🌲 训练集成机器学习模型...")

        # 随机森林 - 网格搜索优化
        print("   优化随机森林...")
        rf_params = {
            'n_estimators': [100, 200],
            'max_depth': [10, 15, None],
            'min_samples_split': [2, 5],
            'min_samples_leaf': [1, 2]
        }

        rf = RandomForestClassifier(random_state=42, n_jobs=-1)
        rf_grid = RandomizedSearchCV(rf, rf_params, cv=3, n_iter=10, scoring='accuracy', random_state=42, n_jobs=-1)
        rf_grid.fit(self.X_train_aug, self.y_train_aug)

        rf_best = rf_grid.best_estimator_
        rf_acc = accuracy_score(self.y_test, rf_best.predict(self.X_test))
        print(f"   随机森林准确率: {rf_acc:.4f}")

        # 梯度提升 - 网格搜索优化
        print("   优化梯度提升...")
        gb_params = {
            'n_estimators': [100, 200],
            'learning_rate': [0.05, 0.1],
            'max_depth': [3, 5],
            'subsample': [0.8, 1.0]
        }

        gb = GradientBoostingClassifier(random_state=42)
        gb_grid = RandomizedSearchCV(gb, gb_params, cv=3, n_iter=10, scoring='accuracy', random_state=42, n_jobs=-1)
        gb_grid.fit(self.X_train_aug, self.y_train_aug)

        gb_best = gb_grid.best_estimator_
        gb_acc = accuracy_score(self.y_test, gb_best.predict(self.X_test))
        print(f"   梯度提升准确率: {gb_acc:.4f}")

        # 集成投票
        voting_clf = VotingClassifier(
            estimators=[('rf', rf_best), ('gb', gb_best)],
            voting='soft'
        )
        voting_clf.fit(self.X_train_aug, self.y_train_aug)
        voting_acc = accuracy_score(self.y_test, voting_clf.predict(self.X_test))
        print(f"   投票集成准确率: {voting_acc:.4f}")

        # 保存最佳ML模型
        best_ml_acc = max(rf_acc, gb_acc, voting_acc)
        if best_ml_acc == rf_acc:
            self.best_models['RandomForest'] = (rf_best, rf_acc)
        elif best_ml_acc == gb_acc:
            self.best_models['GradientBoosting'] = (gb_best, gb_acc)
        else:
            self.best_models['VotingEnsemble'] = (voting_clf, voting_acc)

        return best_ml_acc

    def train_deep_learning_models(self):
        """训练深度学习模型"""
        print("🧠 训练深度学习模型...")

        dl_results = {}

        # CNN模型
        print("   训练CNN模型...")
        cnn_model = self.build_cnn_model()
        X_train_cnn = self.X_train_aug.reshape(self.X_train_aug.shape[0], self.X_train_aug.shape[1], 1)
        X_val_cnn = self.X_val.reshape(self.X_val.shape[0], self.X_val.shape[1], 1)
        X_test_cnn = self.X_test.reshape(self.X_test.shape[0], self.X_test.shape[1], 1)

        callbacks = [
            EarlyStopping(monitor='val_accuracy', patience=15, restore_best_weights=True),
            ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=8, min_lr=1e-7)
        ]

        cnn_model.fit(X_train_cnn, self.y_train_aug, validation_data=(X_val_cnn, self.y_val),
                     epochs=50, batch_size=32, callbacks=callbacks, verbose=0)

        cnn_pred = np.argmax(cnn_model.predict(X_test_cnn), axis=1)
        cnn_acc = accuracy_score(self.y_test, cnn_pred)
        dl_results['CNN'] = (cnn_model, cnn_acc)
        print(f"   CNN准确率: {cnn_acc:.4f}")

        # LSTM模型
        print("   训练LSTM模型...")
        lstm_model = self.build_lstm_model()

        lstm_model.fit(X_train_cnn, self.y_train_aug, validation_data=(X_val_cnn, self.y_val),
                      epochs=50, batch_size=32, callbacks=callbacks, verbose=0)

        lstm_pred = np.argmax(lstm_model.predict(X_test_cnn), axis=1)
        lstm_acc = accuracy_score(self.y_test, lstm_pred)
        dl_results['LSTM'] = (lstm_model, lstm_acc)
        print(f"   LSTM准确率: {lstm_acc:.4f}")

        # DNN模型
        print("   训练DNN模型...")
        dnn_model = self.build_advanced_dnn()

        dnn_model.fit(self.X_train_aug, self.y_train_aug, validation_data=(self.X_val, self.y_val),
                     epochs=50, batch_size=32, callbacks=callbacks, verbose=0)

        dnn_pred = np.argmax(dnn_model.predict(self.X_test), axis=1)
        dnn_acc = accuracy_score(self.y_test, dnn_pred)
        dl_results['DNN'] = (dnn_model, dnn_acc)
        print(f"   DNN准确率: {dnn_acc:.4f}")

        # 保存最佳深度学习模型
        best_dl_name = max(dl_results, key=lambda x: dl_results[x][1])
        best_dl_model, best_dl_acc = dl_results[best_dl_name]
        self.best_models[f'DeepLearning_{best_dl_name}'] = (best_dl_model, best_dl_acc)

        return best_dl_acc

    def save_best_model(self):
        """保存最佳模型"""
        print("💾 保存最佳模型...")

        # 找到最佳模型
        best_name = max(self.best_models, key=lambda x: self.best_models[x][1])
        best_model, best_accuracy = self.best_models[best_name]

        print(f"   最佳模型: {best_name}")
        print(f"   最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")

        # 只有达到90%才保存
        if best_accuracy >= self.target_accuracy:
            # 创建输出目录
            os.makedirs("w", exist_ok=True)

            # 保存模型
            if hasattr(best_model, 'save'):  # 深度学习模型
                best_model.save("w/final_dementia_audio_model.h5")
                model_type = "deep_learning"
            else:  # 机器学习模型
                import joblib
                joblib.dump(best_model, "w/final_dementia_audio_model.pkl")
                model_type = "machine_learning"

            # 保存模型信息
            model_info = {
                'model_name': best_name,
                'model_type': model_type,
                'accuracy': float(best_accuracy),
                'target_achieved': True,
                'classes': list(self.label_encoder.classes_),
                'feature_count': self.X_train.shape[1]
            }

            with open("w/model_info.json", "w", encoding='utf-8') as f:
                json.dump(model_info, f, indent=2, ensure_ascii=False)

            # 复制预处理器
            import shutil
            shutil.copy(os.path.join(self.data_path, "scaler.pkl"), "w/scaler.pkl")
            shutil.copy(os.path.join(self.data_path, "label_encoder.pkl"), "w/label_encoder.pkl")

            print(f"✅ 模型已保存到 w/ 目录")
            return True
        else:
            print(f"❌ 准确率 {best_accuracy*100:.2f}% 未达到90%要求，不保存模型")
            return False

    def run_comprehensive_training(self):
        """运行综合训练"""
        print("🎵 高级综合模型训练器")
        print("🎯 目标准确率: ≥90%")
        print("=" * 60)

        try:
            # 1. 加载和准备数据
            self.load_and_prepare_data()

            # 2. 数据增强
            self.advanced_data_augmentation()

            # 3. 训练集成机器学习模型
            ml_acc = self.train_ensemble_ml_models()

            # 4. 训练深度学习模型
            dl_acc = self.train_deep_learning_models()

            # 5. 显示所有结果
            print(f"\n📊 所有模型结果:")
            for name, (model, acc) in self.best_models.items():
                status = "🎯" if acc >= 0.90 else "📈"
                print(f"   {name}: {acc:.4f} ({acc*100:.2f}%) {status}")

            # 6. 保存最佳模型
            saved = self.save_best_model()

            # 7. 最终总结
            best_accuracy = max(acc for _, acc in self.best_models.values())

            if saved:
                print(f"\n🎉 训练成功! 达到90%+准确率")
                print(f"🏆 最终准确率: {best_accuracy*100:.2f}%")
                print("✅ 模型已保存到 w/ 目录")
            else:
                print(f"\n📈 训练完成，最佳准确率: {best_accuracy*100:.2f}%")
                print("💡 建议继续优化以达到90%目标")

            return best_accuracy

        except Exception as e:
            print(f"❌ 训练失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

if __name__ == "__main__":
    # 创建训练器
    trainer = AdvancedDementiaTrainer()

    # 运行训练
    final_accuracy = trainer.run_comprehensive_training()

    if final_accuracy:
        if final_accuracy >= 0.90:
            print(f"\n🏆 恭喜! 成功达到90%+准确率: {final_accuracy*100:.2f}%")
        else:
            print(f"\n📊 当前最佳准确率: {final_accuracy*100:.2f}%")
            print("💡 可以尝试更多优化方法:")
            print("   - 更多数据增强技术")
            print("   - 特征选择和工程")
            print("   - 更复杂的模型架构")
            print("   - 更长的训练时间")
    else:
        print("\n❌ 训练失败")
