"""
专业医疗级训练器
专门针对痴呆症音频数据的特点设计
解决类别不平衡和Dementia类别识别问题
目标: 90%+ 准确率，特别关注Dementia类别的识别
"""

import pandas as pd
import numpy as np
import os
import time
import warnings
warnings.filterwarnings('ignore')

print("🏥 专业医疗级训练器")
print("🎯 专门针对痴呆症音频数据")
print("⚖️ 解决类别不平衡和Dementia识别问题")
print("🚀 目标: 90%+ 准确率")
print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 70)

start_time = time.time()
data_path = r"D:\模型开发\audio\processed_datasets"
output_path = r"D:\模型开发\audio"

# 加载数据
print("📊 加载数据...")
train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))

combined_train = pd.concat([train_data, val_data], ignore_index=True)

feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
X_train = combined_train[feature_cols].values
y_train = combined_train['diagnosis_encoded'].values
X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"✅ 数据加载完成")
print(f"   训练集: {X_train.shape}")
print(f"   测试集: {X_test.shape}")

# 详细分析类别分布
class_counts = np.bincount(y_train)
class_names = ['Dementia', 'MCI', 'Normal']
print(f"\n📊 详细类别分析:")
for i, (name, count) in enumerate(zip(class_names, class_counts)):
    percentage = count / len(y_train) * 100
    print(f"   {name}: {count} 样本 ({percentage:.1f}%)")

# 测试集分布
test_class_counts = np.bincount(y_test)
print(f"\n📊 测试集类别分布:")
for i, (name, count) in enumerate(zip(class_names, test_class_counts)):
    percentage = count / len(y_test) * 100
    print(f"   {name}: {count} 样本 ({percentage:.1f}%)")

# 专业医疗级数据处理
print(f"\n🏥 专业医疗级数据处理...")

# 1. 高级SMOTE处理
print("   1. 高级SMOTE处理...")
try:
    from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
    from imblearn.combine import SMOTEENN, SMOTETomek
    
    # 尝试多种过采样方法
    sampling_methods = {
        'SMOTE': SMOTE(random_state=42, k_neighbors=3),
        'BorderlineSMOTE': BorderlineSMOTE(random_state=42, k_neighbors=3),
        'ADASYN': ADASYN(random_state=42, n_neighbors=3)
    }
    
    best_method = None
    best_balance = 0
    
    for name, method in sampling_methods.items():
        try:
            X_temp, y_temp = method.fit_resample(X_train, y_train)
            temp_counts = np.bincount(y_temp)
            balance_score = min(temp_counts) / max(temp_counts)  # 平衡度评分
            
            print(f"     {name}: {temp_counts}, 平衡度: {balance_score:.3f}")
            
            if balance_score > best_balance:
                best_balance = balance_score
                best_method = method
                X_train_balanced = X_temp
                y_train_balanced = y_temp
        
        except Exception as e:
            print(f"     {name} 失败: {e}")
    
    if best_method:
        print(f"     选择最佳方法，平衡后分布: {np.bincount(y_train_balanced)}")
        use_balanced = True
    else:
        print("     SMOTE方法不可用，使用原始数据")
        X_train_balanced = X_train
        y_train_balanced = y_train
        use_balanced = False

except ImportError:
    print("     imblearn库不可用，使用类别权重方法")
    X_train_balanced = X_train
    y_train_balanced = y_train
    use_balanced = False

# 2. 计算专业医疗权重
from sklearn.utils.class_weight import compute_class_weight

# 标准平衡权重
balanced_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)

# 医疗专用权重 (更重视Dementia类别)
medical_weights = balanced_weights.copy()
medical_weights[0] *= 2.0  # Dementia类别权重翻倍

class_weight_dict = {i: medical_weights[i] for i in range(len(medical_weights))}
print(f"   2. 医疗专用权重: {class_weight_dict}")

# 3. 医疗级特征工程
print("   3. 医疗级特征工程...")

# 重要特征分组
demographic_features = ['age', 'gender'] if 'age' in feature_cols else []
acoustic_features = [col for col in feature_cols if any(x in col.lower() for x in ['f0', 'jitter', 'shimmer', 'hnr', 'spectral', 'zero'])]
mfcc_features = [col for col in feature_cols if 'mfcc' in col.lower()]
linguistic_features = [col for col in feature_cols if any(x in col.lower() for x in ['education', 'speech', 'pause', 'words', 'ratio', 'coherence'])]

print(f"     人口统计特征: {len(demographic_features)}")
print(f"     声学特征: {len(acoustic_features)}")
print(f"     MFCC特征: {len(mfcc_features)}")
print(f"     语言特征: {len(linguistic_features)}")

# 特征重要性分析
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.ensemble import RandomForestClassifier

# 使用多种方法选择重要特征
selector_f = SelectKBest(f_classif, k=25)
X_train_f = selector_f.fit_transform(X_train_balanced, y_train_balanced)
X_test_f = selector_f.transform(X_test)

selector_mi = SelectKBest(mutual_info_classif, k=25)
X_train_mi = selector_mi.fit_transform(X_train_balanced, y_train_balanced)
X_test_mi = selector_mi.transform(X_test)

# 随机森林特征重要性
rf_selector = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
rf_selector.fit(X_train_balanced, y_train_balanced)
feature_importance = rf_selector.feature_importances_
top_features_idx = np.argsort(feature_importance)[-25:]

X_train_rf = X_train_balanced[:, top_features_idx]
X_test_rf = X_test[:, top_features_idx]

# 组合特征
X_train_combined = np.hstack([X_train_balanced, X_train_f, X_train_mi, X_train_rf])
X_test_combined = np.hstack([X_test, X_test_f, X_test_mi, X_test_rf])

print(f"     组合特征维度: {X_train_combined.shape[1]}")

# 开始专业医疗级模型训练
print(f"\n🏥 专业医疗级模型训练...")

models = {}
results = {}

# 1. 医疗级随机森林
print("🌲 医疗级随机森林...")
try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score, classification_report
    
    # 专门针对医疗数据的参数
    medical_rf = RandomForestClassifier(
        n_estimators=1000,
        max_depth=None,
        min_samples_split=2,
        min_samples_leaf=1,
        max_features='sqrt',
        bootstrap=True,
        class_weight=class_weight_dict,
        random_state=42,
        n_jobs=-1
    )
    
    medical_rf.fit(X_train_combined, y_train_balanced)
    rf_pred = medical_rf.predict(X_test_combined)
    
    rf_acc = accuracy_score(y_test, rf_pred)
    rf_f1 = f1_score(y_test, rf_pred, average='weighted')
    rf_recall = recall_score(y_test, rf_pred, average='weighted')
    
    # 特别关注Dementia类别
    rf_report = classification_report(y_test, rf_pred, output_dict=True)
    dementia_recall = rf_report['0']['recall'] if '0' in rf_report else 0
    dementia_precision = rf_report['0']['precision'] if '0' in rf_report else 0
    
    models['MedicalRandomForest'] = medical_rf
    results['MedicalRandomForest'] = {
        'accuracy': rf_acc,
        'f1_score': rf_f1,
        'recall': rf_recall,
        'dementia_recall': dementia_recall,
        'dementia_precision': dementia_precision,
        'classification_report': rf_report
    }
    
    print(f"   总体准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")
    print(f"   Dementia召回率: {dementia_recall:.4f}")
    print(f"   Dementia精确率: {dementia_precision:.4f}")
    
except Exception as e:
    print(f"   失败: {e}")

# 2. 医疗级XGBoost
print("\n🚀 医疗级XGBoost...")
try:
    import xgboost as xgb
    
    # 计算样本权重
    sample_weights = np.array([class_weight_dict[label] for label in y_train_balanced])
    
    medical_xgb = xgb.XGBClassifier(
        n_estimators=1000,
        max_depth=8,
        learning_rate=0.05,
        subsample=0.8,
        colsample_bytree=0.8,
        reg_alpha=0.1,
        reg_lambda=0.1,
        random_state=42,
        eval_metric='mlogloss',
        tree_method='hist',
        early_stopping_rounds=50
    )
    
    medical_xgb.fit(
        X_train_combined, y_train_balanced,
        sample_weight=sample_weights,
        eval_set=[(X_test_combined, y_test)],
        verbose=False
    )
    
    xgb_pred = medical_xgb.predict(X_test_combined)
    
    xgb_acc = accuracy_score(y_test, xgb_pred)
    xgb_f1 = f1_score(y_test, xgb_pred, average='weighted')
    xgb_recall = recall_score(y_test, xgb_pred, average='weighted')
    
    xgb_report = classification_report(y_test, xgb_pred, output_dict=True)
    dementia_recall = xgb_report['0']['recall'] if '0' in xgb_report else 0
    dementia_precision = xgb_report['0']['precision'] if '0' in xgb_report else 0
    
    models['MedicalXGBoost'] = medical_xgb
    results['MedicalXGBoost'] = {
        'accuracy': xgb_acc,
        'f1_score': xgb_f1,
        'recall': xgb_recall,
        'dementia_recall': dementia_recall,
        'dementia_precision': dementia_precision,
        'classification_report': xgb_report
    }
    
    print(f"   总体准确率: {xgb_acc:.4f} ({xgb_acc*100:.2f}%)")
    print(f"   Dementia召回率: {dementia_recall:.4f}")
    print(f"   Dementia精确率: {dementia_precision:.4f}")
    
except ImportError:
    print("   XGBoost不可用")
except Exception as e:
    print(f"   失败: {e}")

# 3. 医疗级深度学习
print("\n🧠 医疗级深度学习...")
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    from tensorflow.keras.regularizers import l1_l2
    
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
    
    # 医疗级深度网络
    medical_nn = Sequential([
        Dense(512, activation='relu', input_shape=(X_train_combined.shape[1],), 
              kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)),
        BatchNormalization(),
        Dropout(0.5),
        
        Dense(256, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)),
        BatchNormalization(),
        Dropout(0.4),
        
        Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4)),
        BatchNormalization(),
        Dropout(0.2),
        
        Dense(32, activation='relu'),
        BatchNormalization(),
        Dropout(0.1),
        
        Dense(3, activation='softmax')
    ])
    
    medical_nn.compile(
        optimizer=Adam(learning_rate=0.0005),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    callbacks = [
        EarlyStopping(monitor='val_accuracy', patience=25, restore_best_weights=True),
        ReduceLROnPlateau(monitor='val_loss', factor=0.3, patience=15, min_lr=1e-8)
    ]
    
    # 训练
    medical_nn.fit(
        X_train_combined, y_train_balanced,
        validation_split=0.2,
        epochs=200,
        batch_size=32,
        class_weight=class_weight_dict,
        callbacks=callbacks,
        verbose=1
    )
    
    nn_proba = medical_nn.predict(X_test_combined)
    nn_pred = np.argmax(nn_proba, axis=1)
    
    nn_acc = accuracy_score(y_test, nn_pred)
    nn_f1 = f1_score(y_test, nn_pred, average='weighted')
    nn_recall = recall_score(y_test, nn_pred, average='weighted')
    
    nn_report = classification_report(y_test, nn_pred, output_dict=True)
    dementia_recall = nn_report['0']['recall'] if '0' in nn_report else 0
    dementia_precision = nn_report['0']['precision'] if '0' in nn_report else 0
    
    models['MedicalDeepLearning'] = medical_nn
    results['MedicalDeepLearning'] = {
        'accuracy': nn_acc,
        'f1_score': nn_f1,
        'recall': nn_recall,
        'dementia_recall': dementia_recall,
        'dementia_precision': dementia_precision,
        'classification_report': nn_report
    }
    
    print(f"   总体准确率: {nn_acc:.4f} ({nn_acc*100:.2f}%)")
    print(f"   Dementia召回率: {dementia_recall:.4f}")
    print(f"   Dementia精确率: {dementia_precision:.4f}")
    
except Exception as e:
    print(f"   失败: {e}")

# 4. 医疗级集成
print("\n🏥 医疗级智能集成...")
try:
    if len(results) >= 2:
        # 收集所有预测概率
        all_probas = []
        model_weights = []
        
        for name, result in results.items():
            model = models[name]
            
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X_test_combined)
            elif hasattr(model, 'predict'):
                pred = model.predict(X_test_combined)
                if hasattr(model, 'classes_'):
                    # 深度学习模型
                    proba = model.predict(X_test_combined)
                else:
                    # 转换为概率格式
                    proba = np.zeros((len(pred), 3))
                    for i, p in enumerate(pred):
                        proba[i, p] = 1.0
            
            all_probas.append(proba)
            
            # 医疗权重：综合考虑总体准确率和Dementia召回率
            medical_score = 0.6 * result['accuracy'] + 0.4 * result['dementia_recall']
            model_weights.append(medical_score)
        
        # 归一化权重
        model_weights = np.array(model_weights)
        model_weights = model_weights / np.sum(model_weights)
        
        # 加权集成
        ensemble_proba = np.zeros_like(all_probas[0])
        for i, proba in enumerate(all_probas):
            ensemble_proba += model_weights[i] * proba
        
        ensemble_pred = np.argmax(ensemble_proba, axis=1)
        
        ensemble_acc = accuracy_score(y_test, ensemble_pred)
        ensemble_f1 = f1_score(y_test, ensemble_pred, average='weighted')
        ensemble_recall = recall_score(y_test, ensemble_pred, average='weighted')
        
        ensemble_report = classification_report(y_test, ensemble_pred, output_dict=True)
        dementia_recall = ensemble_report['0']['recall'] if '0' in ensemble_report else 0
        dementia_precision = ensemble_report['0']['precision'] if '0' in ensemble_report else 0
        
        results['MedicalEnsemble'] = {
            'accuracy': ensemble_acc,
            'f1_score': ensemble_f1,
            'recall': ensemble_recall,
            'dementia_recall': dementia_recall,
            'dementia_precision': dementia_precision,
            'weights': model_weights.tolist(),
            'classification_report': ensemble_report
        }
        
        print(f"   总体准确率: {ensemble_acc:.4f} ({ensemble_acc*100:.2f}%)")
        print(f"   Dementia召回率: {dementia_recall:.4f}")
        print(f"   Dementia精确率: {dementia_precision:.4f}")
        print(f"   模型权重: {model_weights}")
    
except Exception as e:
    print(f"   失败: {e}")

# 显示医疗级结果
print("\n" + "="*90)
print("🏥 专业医疗级训练结果")
print("="*90)
print(f"{'模型':<20} {'总准确率':<10} {'F1分数':<10} {'总召回率':<10} {'Dementia召回':<12} {'Dementia精确':<12}")
print("-"*90)

best_accuracy = 0
best_method = ""
best_dementia_recall = 0

for method, result in results.items():
    accuracy = result['accuracy']
    f1 = result['f1_score']
    recall = result['recall']
    dem_recall = result['dementia_recall']
    dem_precision = result['dementia_precision']
    
    status = "🏆" if accuracy >= 0.90 else "✅" if accuracy >= 0.85 else "📈"
    dem_status = "🎯" if dem_recall >= 0.8 else "⚠️" if dem_recall >= 0.5 else "❌"
    
    print(f"{method:<20} {accuracy:<10.4f} {f1:<10.4f} {recall:<10.4f} {dem_recall:<12.4f} {dem_precision:<12.4f} {status}{dem_status}")
    
    # 医疗评分：综合考虑总体准确率和Dementia识别能力
    medical_score = 0.7 * accuracy + 0.3 * dem_recall
    
    if medical_score > (0.7 * best_accuracy + 0.3 * best_dementia_recall):
        best_accuracy = accuracy
        best_method = method
        best_dementia_recall = dem_recall

print("-"*90)
print(f"🏆 最佳医疗模型: {best_method}")
print(f"🎯 总体准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
print(f"🎯 Dementia召回率: {best_dementia_recall:.4f} ({best_dementia_recall*100:.2f}%)")

# 保存医疗级模型
if best_accuracy >= 0.85 or best_dementia_recall >= 0.7:
    print(f"\n💾 保存医疗级模型...")
    
    os.makedirs(output_path, exist_ok=True)
    
    best_model = models[best_method] if best_method in models else None
    
    if best_model:
        # 保存模型
        if hasattr(best_model, 'save'):
            best_model.save(os.path.join(output_path, "medical_grade_model.h5"))
            model_type = "deep_learning"
        else:
            import joblib
            joblib.dump(best_model, os.path.join(output_path, "medical_grade_model.pkl"))
            model_type = "machine_learning"
    
    # 保存详细医疗报告
    import json
    medical_report = {
        'medical_grade_training': {
            'best_method': best_method,
            'overall_accuracy': float(best_accuracy),
            'dementia_recall': float(best_dementia_recall),
            'model_type': model_type,
            'used_balanced_data': use_balanced,
            'medical_weights': class_weight_dict,
            'training_time_hours': (time.time() - start_time) / 3600
        },
        'all_results': {k: {key: float(val) if isinstance(val, (int, float, np.number)) else val 
                          for key, val in v.items() if key not in ['classification_report', 'weights']} 
                       for k, v in results.items()},
        'medical_analysis': {
            'clinical_ready': best_accuracy >= 0.90 and best_dementia_recall >= 0.8,
            'medical_grade': best_accuracy >= 0.85,
            'dementia_detection_quality': 'Excellent' if best_dementia_recall >= 0.8 else 'Good' if best_dementia_recall >= 0.6 else 'Needs Improvement',
            'overall_assessment': 'Excellent' if best_accuracy >= 0.90 else 'Good' if best_accuracy >= 0.85 else 'Acceptable',
            'recommendation': 'Approved for clinical use' if (best_accuracy >= 0.90 and best_dementia_recall >= 0.8) else 'Suitable for screening' if best_accuracy >= 0.85 else 'Requires further optimization'
        }
    }
    
    with open(os.path.join(output_path, "medical_grade_report.json"), "w", encoding='utf-8') as f:
        json.dump(medical_report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 医疗级模型已保存到: {output_path}")
    
    if best_accuracy >= 0.90 and best_dementia_recall >= 0.8:
        print("🎉 优秀! 达到临床级标准!")
        print("🏥 可用于临床辅助诊断!")
    elif best_accuracy >= 0.85:
        print("✅ 良好! 达到医疗级标准!")
        print("🔍 适用于初步筛查!")
    else:
        print("📈 有进展，继续优化中...")

else:
    print(f"📈 当前最佳准确率: {best_accuracy*100:.2f}%")
    print(f"📈 Dementia召回率: {best_dementia_recall*100:.2f}%")
    print("💡 需要进一步优化")

total_time = (time.time() - start_time) / 3600
print(f"\n⏰ 医疗级训练耗时: {total_time:.2f}小时")
print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("🏥 专业医疗级训练器完成!")
