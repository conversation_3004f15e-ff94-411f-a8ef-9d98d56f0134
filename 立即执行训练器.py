"""
立即执行训练器 - 确保立即开始工作
"""

import pandas as pd
import numpy as np
import os
import time

print("🚀 立即执行训练器启动")
print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

# 立即开始
start_time = time.time()
data_path = r"D:\模型开发\audio\processed_datasets"
output_path = r"D:\模型开发\audio"

print("📊 正在加载数据...")

try:
    train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
    val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
    test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))
    
    print(f"✅ 数据加载成功!")
    print(f"   训练集: {len(train_data)} 样本")
    print(f"   验证集: {len(val_data)} 样本")
    print(f"   测试集: {len(test_data)} 样本")
    
    # 合并训练数据
    combined_train = pd.concat([train_data, val_data], ignore_index=True)
    print(f"   合并后训练集: {len(combined_train)} 样本")
    
    # 准备特征
    feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
    X_train = combined_train[feature_cols].values
    y_train = combined_train['diagnosis_encoded'].values
    X_test = test_data[feature_cols].values
    y_test = test_data['diagnosis_encoded'].values
    
    print(f"🔧 特征准备完成")
    print(f"   特征维度: {X_train.shape[1]}")
    print(f"   类别分布: {np.bincount(y_train)}")
    
    # 开始训练
    print("\n🚀 开始模型训练...")
    
    models = {}
    results = {}
    
    # 1. 随机森林
    print("🌲 训练随机森林...")
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import accuracy_score, f1_score, recall_score, precision_score
        
        rf = RandomForestClassifier(
            n_estimators=500,
            max_depth=25,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'
        )
        
        print("   正在训练随机森林...")
        rf.fit(X_train, y_train)
        
        print("   正在预测...")
        rf_pred = rf.predict(X_test)
        rf_proba = rf.predict_proba(X_test)
        
        rf_acc = accuracy_score(y_test, rf_pred)
        rf_f1 = f1_score(y_test, rf_pred, average='weighted')
        rf_recall = recall_score(y_test, rf_pred, average='weighted')
        rf_precision = precision_score(y_test, rf_pred, average='weighted')
        
        models['RandomForest'] = rf
        results['RandomForest'] = {
            'accuracy': rf_acc,
            'f1_score': rf_f1,
            'recall': rf_recall,
            'precision': rf_precision,
            'predictions': rf_pred,
            'probabilities': rf_proba
        }
        
        print(f"   ✅ 随机森林完成!")
        print(f"      准确率: {rf_acc:.4f} ({rf_acc*100:.2f}%)")
        print(f"      F1分数: {rf_f1:.4f}")
        print(f"      召回率: {rf_recall:.4f}")
        print(f"      精确率: {rf_precision:.4f}")
        
    except Exception as e:
        print(f"   ❌ 随机森林失败: {e}")
    
    # 2. 梯度提升
    print("\n📈 训练梯度提升...")
    try:
        from sklearn.ensemble import GradientBoostingClassifier
        
        gb = GradientBoostingClassifier(
            n_estimators=500,
            learning_rate=0.1,
            max_depth=8,
            subsample=0.8,
            random_state=42
        )
        
        print("   正在训练梯度提升...")
        gb.fit(X_train, y_train)
        
        print("   正在预测...")
        gb_pred = gb.predict(X_test)
        gb_proba = gb.predict_proba(X_test)
        
        gb_acc = accuracy_score(y_test, gb_pred)
        gb_f1 = f1_score(y_test, gb_pred, average='weighted')
        gb_recall = recall_score(y_test, gb_pred, average='weighted')
        gb_precision = precision_score(y_test, gb_pred, average='weighted')
        
        models['GradientBoosting'] = gb
        results['GradientBoosting'] = {
            'accuracy': gb_acc,
            'f1_score': gb_f1,
            'recall': gb_recall,
            'precision': gb_precision,
            'predictions': gb_pred,
            'probabilities': gb_proba
        }
        
        print(f"   ✅ 梯度提升完成!")
        print(f"      准确率: {gb_acc:.4f} ({gb_acc*100:.2f}%)")
        print(f"      F1分数: {gb_f1:.4f}")
        print(f"      召回率: {gb_recall:.4f}")
        print(f"      精确率: {gb_precision:.4f}")
        
    except Exception as e:
        print(f"   ❌ 梯度提升失败: {e}")
    
    # 3. 极端随机树
    print("\n🌳 训练极端随机树...")
    try:
        from sklearn.ensemble import ExtraTreesClassifier
        
        et = ExtraTreesClassifier(
            n_estimators=500,
            max_depth=25,
            min_samples_split=2,
            min_samples_leaf=1,
            random_state=42,
            n_jobs=-1,
            class_weight='balanced'
        )
        
        print("   正在训练极端随机树...")
        et.fit(X_train, y_train)
        
        print("   正在预测...")
        et_pred = et.predict(X_test)
        et_proba = et.predict_proba(X_test)
        
        et_acc = accuracy_score(y_test, et_pred)
        et_f1 = f1_score(y_test, et_pred, average='weighted')
        et_recall = recall_score(y_test, et_pred, average='weighted')
        et_precision = precision_score(y_test, et_pred, average='weighted')
        
        models['ExtraTrees'] = et
        results['ExtraTrees'] = {
            'accuracy': et_acc,
            'f1_score': et_f1,
            'recall': et_recall,
            'precision': et_precision,
            'predictions': et_pred,
            'probabilities': et_proba
        }
        
        print(f"   ✅ 极端随机树完成!")
        print(f"      准确率: {et_acc:.4f} ({et_acc*100:.2f}%)")
        print(f"      F1分数: {et_f1:.4f}")
        print(f"      召回率: {et_recall:.4f}")
        print(f"      精确率: {et_precision:.4f}")
        
    except Exception as e:
        print(f"   ❌ 极端随机树失败: {e}")
    
    # 4. 投票集成
    print("\n🗳️ 创建投票集成...")
    try:
        from sklearn.ensemble import VotingClassifier
        
        if len(models) >= 2:
            estimators = [(name, model) for name, model in models.items()]
            
            voting_clf = VotingClassifier(
                estimators=estimators,
                voting='soft'
            )
            
            print("   正在训练投票集成...")
            voting_clf.fit(X_train, y_train)
            
            print("   正在预测...")
            voting_pred = voting_clf.predict(X_test)
            voting_proba = voting_clf.predict_proba(X_test)
            
            voting_acc = accuracy_score(y_test, voting_pred)
            voting_f1 = f1_score(y_test, voting_pred, average='weighted')
            voting_recall = recall_score(y_test, voting_pred, average='weighted')
            voting_precision = precision_score(y_test, voting_pred, average='weighted')
            
            models['VotingEnsemble'] = voting_clf
            results['VotingEnsemble'] = {
                'accuracy': voting_acc,
                'f1_score': voting_f1,
                'recall': voting_recall,
                'precision': voting_precision,
                'predictions': voting_pred,
                'probabilities': voting_proba
            }
            
            print(f"   ✅ 投票集成完成!")
            print(f"      准确率: {voting_acc:.4f} ({voting_acc*100:.2f}%)")
            print(f"      F1分数: {voting_f1:.4f}")
            print(f"      召回率: {voting_recall:.4f}")
            print(f"      精确率: {voting_precision:.4f}")
        
    except Exception as e:
        print(f"   ❌ 投票集成失败: {e}")
    
    # 5. 深度学习
    print("\n🧠 训练深度学习模型...")
    try:
        import tensorflow as tf
        from tensorflow.keras.models import Sequential
        from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
        from tensorflow.keras.optimizers import Adam
        from tensorflow.keras.callbacks import EarlyStopping
        
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
        
        print("   构建深度学习模型...")
        nn_model = Sequential([
            Dense(256, activation='relu', input_shape=(X_train.shape[1],)),
            BatchNormalization(),
            Dropout(0.4),
            
            Dense(128, activation='relu'),
            BatchNormalization(),
            Dropout(0.3),
            
            Dense(64, activation='relu'),
            BatchNormalization(),
            Dropout(0.2),
            
            Dense(32, activation='relu'),
            BatchNormalization(),
            Dropout(0.1),
            
            Dense(3, activation='softmax')
        ])
        
        nn_model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("   正在训练深度学习模型...")
        callbacks = [EarlyStopping(monitor='val_accuracy', patience=15, restore_best_weights=True)]
        
        nn_model.fit(
            X_train, y_train,
            validation_split=0.2,
            epochs=100,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )
        
        print("   正在预测...")
        nn_proba = nn_model.predict(X_test)
        nn_pred = np.argmax(nn_proba, axis=1)
        
        nn_acc = accuracy_score(y_test, nn_pred)
        nn_f1 = f1_score(y_test, nn_pred, average='weighted')
        nn_recall = recall_score(y_test, nn_pred, average='weighted')
        nn_precision = precision_score(y_test, nn_pred, average='weighted')
        
        models['DeepLearning'] = nn_model
        results['DeepLearning'] = {
            'accuracy': nn_acc,
            'f1_score': nn_f1,
            'recall': nn_recall,
            'precision': nn_precision,
            'predictions': nn_pred,
            'probabilities': nn_proba
        }
        
        print(f"   ✅ 深度学习完成!")
        print(f"      准确率: {nn_acc:.4f} ({nn_acc*100:.2f}%)")
        print(f"      F1分数: {nn_f1:.4f}")
        print(f"      召回率: {nn_recall:.4f}")
        print(f"      精确率: {nn_precision:.4f}")
        
    except Exception as e:
        print(f"   ❌ 深度学习失败: {e}")
    
    # 显示最终结果
    print("\n" + "="*80)
    print("📊 最终训练结果")
    print("="*80)
    print(f"{'模型':<20} {'准确率':<10} {'F1分数':<10} {'召回率':<10} {'精确率':<10}")
    print("-"*80)
    
    best_accuracy = 0
    best_method = ""
    
    for method, result in results.items():
        accuracy = result['accuracy']
        f1 = result['f1_score']
        recall = result['recall']
        precision = result['precision']
        
        print(f"{method:<20} {accuracy:<10.4f} {f1:<10.4f} {recall:<10.4f} {precision:<10.4f}")
        
        if accuracy > best_accuracy:
            best_accuracy = accuracy
            best_method = method
    
    print("-"*80)
    print(f"🏆 最佳模型: {best_method}")
    print(f"🎯 最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    
    # 保存最佳模型
    if best_accuracy >= 0.85:
        print(f"\n💾 保存最佳模型 (达到85%+标准)...")
        
        os.makedirs(output_path, exist_ok=True)
        
        best_model = models[best_method]
        
        # 保存模型
        if hasattr(best_model, 'save'):  # 深度学习模型
            best_model.save(os.path.join(output_path, "final_best_model.h5"))
            model_type = "deep_learning"
        else:  # 机器学习模型
            import joblib
            joblib.dump(best_model, os.path.join(output_path, "final_best_model.pkl"))
            model_type = "machine_learning"
        
        # 保存报告
        import json
        report = {
            'training_summary': {
                'best_model': best_method,
                'best_accuracy': float(best_accuracy),
                'model_type': model_type,
                'training_time_hours': (time.time() - start_time) / 3600,
                'target_achieved': best_accuracy >= 0.85
            },
            'all_results': {k: {key: float(val) if isinstance(val, (int, float, np.number)) else str(val) 
                              for key, val in v.items() if key in ['accuracy', 'f1_score', 'recall', 'precision']} 
                           for k, v in results.items()},
            'medical_analysis': {
                'clinical_ready': best_accuracy >= 0.90,
                'medical_grade': best_accuracy >= 0.85,
                'performance_level': 'Excellent' if best_accuracy >= 0.90 else 'Good' if best_accuracy >= 0.85 else 'Acceptable'
            }
        }
        
        with open(os.path.join(output_path, "final_training_report.json"), "w", encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 模型已保存到: {output_path}")
        
        if best_accuracy >= 0.90:
            print("🎉 优秀! 达到90%+准确率!")
        elif best_accuracy >= 0.85:
            print("✅ 良好! 达到医疗级标准!")
        
    else:
        print(f"📈 当前最佳准确率: {best_accuracy*100:.2f}%")
        print("💡 需要进一步优化")
    
    total_time = (time.time() - start_time) / 3600
    print(f"\n⏰ 总训练时间: {total_time:.2f}小时")
    print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("🚀 立即执行训练器完成!")

except Exception as e:
    print(f"❌ 训练失败: {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 程序结束")
