"""
🧠 AutoDL科学EEG数据集创建器
专门为autodl云端环境设计，基于EEG_extracted创建科学合理的训练数据集
"""

import os
import json
import pandas as pd
import numpy as np
from collections import Counter
import shutil
from sklearn.model_selection import train_test_split
import glob

class AutoDLScientificEEGCreator:
    """AutoDL科学EEG数据集创建器"""
    
    def __init__(self):
        self.base_path = "/root/EEG_extracted"  # autodl云端路径
        self.output_path = "/root/Scientific_EEG_Datasets"
        
        # 删除旧的输出目录并重新创建
        if os.path.exists(self.output_path):
            shutil.rmtree(self.output_path)
        os.makedirs(self.output_path, exist_ok=True)
        
        print("🧠 AutoDL科学EEG数据集创建器")
        print("=" * 60)
        print(f"📁 源数据路径: {self.base_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def discover_eeg_data(self):
        """发现EEG数据"""
        print("\n🔍 第一步：发现EEG数据...")
        
        if not os.path.exists(self.base_path):
            print(f"❌ 源数据目录不存在: {self.base_path}")
            return None
        
        # 查找所有.set文件
        set_files = []
        for root, dirs, files in os.walk(self.base_path):
            for file in files:
                if file.endswith('.set'):
                    full_path = os.path.join(root, file)
                    set_files.append(full_path)
        
        print(f"📁 找到 {len(set_files)} 个.set文件")
        
        # 分析文件结构
        subjects_info = {}
        for set_file in set_files:
            # 从文件路径提取subject信息
            filename = os.path.basename(set_file)
            
            # 提取subject ID (假设格式为sub-XXX_task-eyesclosed_eeg.set)
            if filename.startswith('sub-'):
                subject_id = filename.split('_')[0]  # sub-XXX
                
                if subject_id not in subjects_info:
                    subjects_info[subject_id] = {
                        'id': subject_id,
                        'files': [],
                        'paths': []
                    }
                
                subjects_info[subject_id]['files'].append(filename)
                subjects_info[subject_id]['paths'].append(set_file)
        
        print(f"📊 识别出 {len(subjects_info)} 个受试者")
        
        # 显示前几个样本
        for i, (subject_id, info) in enumerate(list(subjects_info.items())[:5]):
            print(f"   {subject_id}: {len(info['files'])} 个文件")
        
        return subjects_info
    
    def extract_labels_from_metadata(self):
        """从元数据文件提取标签"""
        print("\n📋 第二步：提取标签信息...")
        
        # 查找可能的元数据文件
        metadata_files = []
        possible_names = [
            'participants.tsv', 'participants.csv', 'participants.txt',
            'labels.txt', 'labels.csv', 'labels.tsv',
            'dataset_description.json', 'README.txt'
        ]
        
        for root, dirs, files in os.walk(self.base_path):
            for file in files:
                if file.lower() in [name.lower() for name in possible_names]:
                    metadata_files.append(os.path.join(root, file))
        
        print(f"📄 找到 {len(metadata_files)} 个可能的元数据文件:")
        for file in metadata_files:
            print(f"   {file}")
        
        # 尝试读取标签信息
        labels_df = None
        
        for metadata_file in metadata_files:
            try:
                if metadata_file.endswith('.tsv'):
                    df = pd.read_csv(metadata_file, sep='\t')
                elif metadata_file.endswith('.csv'):
                    df = pd.read_csv(metadata_file)
                elif metadata_file.endswith('.txt'):
                    # 尝试不同分隔符
                    try:
                        df = pd.read_csv(metadata_file, sep='\t')
                    except:
                        df = pd.read_csv(metadata_file, sep=',')
                else:
                    continue
                
                print(f"\n✅ 成功读取: {os.path.basename(metadata_file)}")
                print(f"   形状: {df.shape}")
                print(f"   列名: {list(df.columns)}")
                print(f"   前3行:")
                print(df.head(3))
                
                # 检查是否包含subject信息
                subject_columns = [col for col in df.columns if 'subject' in col.lower() or 'participant' in col.lower() or 'id' in col.lower()]
                label_columns = [col for col in df.columns if any(keyword in col.lower() for keyword in ['group', 'label', 'class', 'diagnosis', 'condition'])]
                
                if subject_columns and label_columns:
                    labels_df = df
                    print(f"   🎯 找到标签信息!")
                    print(f"   受试者列: {subject_columns}")
                    print(f"   标签列: {label_columns}")
                    break
                    
            except Exception as e:
                print(f"   ❌ 读取失败 {os.path.basename(metadata_file)}: {e}")
        
        return labels_df

    def process_metadata_labels(self, metadata_df, subjects_info):
        """处理元数据标签，确保格式正确"""
        print("\n🔧 处理元数据标签...")

        # 确定标签列
        label_column = None
        possible_label_columns = ['Group', 'group', 'Label', 'label', 'Class', 'class', 'Diagnosis', 'diagnosis']

        for col in possible_label_columns:
            if col in metadata_df.columns:
                label_column = col
                break

        if label_column is None:
            print("❌ 未找到标签列，使用文件名推断")
            return self.create_labels_from_filenames(subjects_info)

        print(f"📋 使用标签列: {label_column}")

        # 重命名列以保持一致性
        processed_df = metadata_df.copy()
        if 'participant_id' not in processed_df.columns:
            # 查找ID列
            id_columns = [col for col in processed_df.columns if 'id' in col.lower() or 'subject' in col.lower() or 'participant' in col.lower()]
            if id_columns:
                processed_df = processed_df.rename(columns={id_columns[0]: 'participant_id'})

        # 重命名标签列为统一的'label'
        processed_df = processed_df.rename(columns={label_column: 'label'})

        # 只保留需要的列
        processed_df = processed_df[['participant_id', 'label']].copy()

        # 添加文件路径信息
        processed_df['paths'] = processed_df['participant_id'].apply(
            lambda x: subjects_info.get(x, {}).get('paths', [])
        )
        processed_df['file_count'] = processed_df['paths'].apply(len)

        # 移除没有对应文件的受试者
        processed_df = processed_df[processed_df['file_count'] > 0].copy()

        print(f"📊 处理后的标签分布:")
        label_counts = processed_df['label'].value_counts()
        for label, count in label_counts.items():
            print(f"   {label}: {count} 个受试者")

        print(f"✅ 处理完成: {len(processed_df)} 个有效受试者")

        return processed_df

    def create_labels_from_filenames(self, subjects_info):
        """从文件名创建标签"""
        print("\n🏷️ 第三步：从文件名推断标签...")
        
        subjects_data = []
        
        for subject_id, info in subjects_info.items():
            # 从subject ID推断标签
            label = self.infer_label_from_id(subject_id)
            
            subjects_data.append({
                'participant_id': subject_id,
                'label': label,
                'file_count': len(info['files']),
                'files': info['files'],
                'paths': info['paths']
            })
        
        df = pd.DataFrame(subjects_data)
        
        # 显示标签分布
        if 'label' in df.columns:
            label_counts = df['label'].value_counts()
            print(f"📊 推断的标签分布:")
            for label, count in label_counts.items():
                print(f"   {label}: {count} 个受试者")
        
        return df
    
    def infer_label_from_id(self, subject_id):
        """从subject ID推断标签"""
        subject_upper = subject_id.upper()
        
        # 常见的标签模式
        if any(pattern in subject_upper for pattern in ['AD', 'ALZ', 'ALZHEIMER']):
            return 'AD'  # 阿尔茨海默病
        elif any(pattern in subject_upper for pattern in ['HC', 'CTL', 'CONTROL', 'HEALTHY']):
            return 'HC'  # 健康对照
        elif any(pattern in subject_upper for pattern in ['FTD', 'FRONTOTEMPORAL']):
            return 'FTD'  # 额颞叶痴呆
        elif any(pattern in subject_upper for pattern in ['MCI', 'MILD']):
            return 'MCI'  # 轻度认知障碍
        elif any(pattern in subject_upper for pattern in ['PD', 'PARKINSON']):
            return 'PD'  # 帕金森病
        else:
            # 基于数字范围推断（这需要根据实际数据调整）
            try:
                # 提取数字部分
                num_part = ''.join(filter(str.isdigit, subject_id))
                if num_part:
                    num = int(num_part)
                    # 这里的范围需要根据您的实际数据调整
                    if num <= 30:
                        return 'HC'  # 假设前30个是健康对照
                    elif num <= 60:
                        return 'AD'  # 假设31-60是AD
                    else:
                        return 'FTD'  # 假设61+是FTD
            except:
                pass
            
            return 'Unknown'
    
    def create_scientific_splits(self, df):
        """创建科学的数据集划分"""
        print("\n📊 第四步：创建科学数据集划分...")

        # 确定标签列
        label_column = None
        possible_label_columns = ['label', 'Label', 'Group', 'group', 'Class', 'class']

        for col in possible_label_columns:
            if col in df.columns:
                label_column = col
                break

        if label_column is None:
            print(f"❌ 未找到标签列，可用列: {list(df.columns)}")
            return None

        print(f"📋 使用标签列: {label_column}")
        
        # 移除未知标签
        if 'Unknown' in df[label_column].values:
            unknown_count = (df[label_column] == 'Unknown').sum()
            print(f"⚠️ 移除 {unknown_count} 个未知标签的样本")
            df = df[df[label_column] != 'Unknown'].copy()
        
        if len(df) == 0:
            print("❌ 没有有效的标签数据")
            return None
        
        # 检查标签分布
        label_counts = df[label_column].value_counts()
        print(f"📊 有效标签分布:")
        for label, count in label_counts.items():
            print(f"   {label}: {count} 个样本")
        
        # 检查最小样本数
        min_samples = label_counts.min()
        total_samples = len(df)
        
        print(f"📈 数据集统计:")
        print(f"   总样本数: {total_samples}")
        print(f"   类别数: {len(label_counts)}")
        print(f"   最少类别样本数: {min_samples}")
        
        # 根据样本数选择划分策略
        if min_samples < 3:
            print(f"⚠️ 最少类别只有 {min_samples} 个样本，使用随机划分")
            # 随机划分
            train_df, temp_df = train_test_split(df, test_size=0.4, random_state=42)
            val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)
        else:
            print("✅ 使用分层划分确保类别平衡")
            # 分层划分
            train_df, temp_df = train_test_split(
                df, test_size=0.4, random_state=42, stratify=df[label_column]
            )
            val_df, test_df = train_test_split(
                temp_df, test_size=0.5, random_state=42, stratify=temp_df[label_column]
            )
        
        splits = {
            'train': train_df,
            'val': val_df,
            'test': test_df
        }
        
        # 显示划分结果
        print(f"\n📊 科学数据集划分结果:")
        for split_name, split_df in splits.items():
            percentage = len(split_df) / total_samples * 100
            print(f"   {split_name.upper()}集: {len(split_df)} 个样本 ({percentage:.1f}%)")
            
            split_label_counts = split_df[label_column].value_counts()
            for label, count in split_label_counts.items():
                label_percentage = count / len(split_df) * 100
                print(f"     {label}: {count} 个 ({label_percentage:.1f}%)")
        
        return splits, label_column
    
    def save_datasets(self, splits, label_column):
        """保存数据集"""
        print("\n💾 第五步：保存数据集...")
        
        for split_name, split_df in splits.items():
            split_dir = os.path.join(self.output_path, split_name)
            os.makedirs(split_dir, exist_ok=True)
            
            print(f"\n📂 保存 {split_name.upper()} 集...")
            
            # 保存标签文件
            labels_file = os.path.join(split_dir, 'labels.txt')
            with open(labels_file, 'w', encoding='utf-8') as f:
                f.write("subject_id\tlabel\n")
                for _, row in split_df.iterrows():
                    f.write(f"{row['participant_id']}\t{row[label_column]}\n")
            
            print(f"   ✅ 标签文件: {labels_file}")
            
            # 复制EEG文件（选择每个受试者的第一个文件）
            copied_count = 0
            for _, row in split_df.iterrows():
                if row['paths']:
                    # 选择第一个文件（通常是预处理过的）
                    src_file = row['paths'][0]
                    dst_file = os.path.join(split_dir, os.path.basename(src_file))
                    
                    try:
                        shutil.copy2(src_file, dst_file)
                        copied_count += 1
                    except Exception as e:
                        print(f"     ⚠️ 复制失败 {os.path.basename(src_file)}: {e}")
            
            print(f"   ✅ 复制EEG文件: {copied_count}/{len(split_df)} 个")
            
            # 保存数据集信息
            dataset_info = {
                'split_name': split_name,
                'total_subjects': len(split_df),
                'label_column': label_column,
                'label_distribution': split_df[label_column].value_counts().to_dict(),
                'subjects': split_df['participant_id'].tolist(),
                'creation_method': 'scientific_stratified_split',
                'random_seed': 42,
                'data_source': self.base_path,
                'file_format': 'EEGLAB .set files'
            }
            
            info_file = os.path.join(split_dir, 'dataset_info.json')
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ 信息文件: {info_file}")
        
        # 保存总体描述
        overall_info = {
            'dataset_name': 'AutoDL Scientific EEG Dataset',
            'creation_date': pd.Timestamp.now().isoformat(),
            'total_subjects': len(pd.concat(splits.values())),
            'label_column': label_column,
            'splits': {
                split_name: {
                    'count': len(split_df),
                    'percentage': len(split_df) / len(pd.concat(splits.values())) * 100,
                    'label_distribution': split_df[label_column].value_counts().to_dict()
                }
                for split_name, split_df in splits.items()
            },
            'methodology': {
                'split_method': 'stratified_random_split',
                'random_seed': 42,
                'train_ratio': 0.6,
                'val_ratio': 0.2,
                'test_ratio': 0.2
            },
            'data_source': self.base_path,
            'platform': 'AutoDL Cloud'
        }
        
        overall_file = os.path.join(self.output_path, 'dataset_description.json')
        with open(overall_file, 'w', encoding='utf-8') as f:
            json.dump(overall_info, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 总体描述: {overall_file}")
        
        return overall_info
    
    def run_creation(self):
        """运行完整创建流程"""
        print("🚀 开始AutoDL科学EEG数据集创建")
        print("=" * 60)
        
        try:
            # 1. 发现EEG数据
            subjects_info = self.discover_eeg_data()
            if not subjects_info:
                return False
            
            # 2. 尝试提取元数据标签
            metadata_df = self.extract_labels_from_metadata()
            
            # 3. 创建标签数据
            if metadata_df is not None:
                print("✅ 使用元数据中的标签")
                # 处理元数据，确保格式正确
                df = self.process_metadata_labels(metadata_df, subjects_info)
            else:
                print("⚠️ 未找到元数据，从文件名推断标签")
                df = self.create_labels_from_filenames(subjects_info)

            # 4. 创建科学划分
            result = self.create_scientific_splits(df)
            if result is None:
                return False
            
            splits, label_column = result
            
            # 5. 保存数据集
            overall_info = self.save_datasets(splits, label_column)
            
            print(f"\n🎉 AutoDL科学EEG数据集创建完成!")
            print("=" * 60)
            print(f"📁 输出目录: {self.output_path}")
            print(f"📊 总样本数: {overall_info['total_subjects']}")
            print(f"📋 标签列: {label_column}")
            print(f"🔬 方法: 科学分层划分")
            print(f"☁️ 平台: AutoDL云端")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🧠 AutoDL科学EEG数据集创建系统")
    print("专为autodl云端环境设计")
    print()
    
    creator = AutoDLScientificEEGCreator()
    success = creator.run_creation()
    
    if success:
        print("\n🏆 科学数据集创建成功!")
        print("📋 可以开始训练高质量EEG模型了")
    else:
        print("\n❌ 创建失败")


if __name__ == "__main__":
    main()
