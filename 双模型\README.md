# 🧠 AI痴呆症识别器 - 双模型版(MRI)

## 📋 项目简介

这是一个基于深度学习的医学影像智能分析系统，采用双模型架构：
- **MRI图像检测模型**: 验证输入图像是否为MRI扫描图像
- **痴呆症分析模型**: 进行4类痴呆症分类分析

## 🚀 主要功能

### 🔍 智能图像验证
- 自动检测输入图像是否为MRI扫描图像
- 对非MRI图像显示警告提示
- 支持用户选择是否继续分析

### 🧠 痴呆症分析
- 4类痴呆症分类：
  - MildDemented (轻度痴呆)
  - ModerateDemented (中度痴呆)
  - NonDemented (无痴呆)
  - VeryMildDemented (非常轻度痴呆)
- 显示详细的概率分布
- 提供置信度评估

### 📊 结果展示
- 实时显示分析进度
- 可视化概率分布
- MRI检测结果展示
- 历史记录管理

### 📄 报告生成
- **HTML报告**: 包含原始图像的完整报告
- **PDF报告**: 专业格式的诊断报告
- **JSON数据**: 结构化的分析结果

## 🛠️ 系统要求

### 模型文件
确保以下模型文件存在：
- `D:\模型开发\ct_class.h5` - 痴呆症分析模型
- `D:\模型开发\ct_other_model.h5` - CT图像检测模型

### Python依赖
```bash
pip install tensorflow
pip install customtkinter
pip install pillow
pip install opencv-python
pip install numpy
pip install fpdf2  # 可选，用于PDF报告
```

## 🎯 使用方法

### 启动应用
```bash
python 双模型/双模型
```

### 测试系统
```bash
python 双模型/test_dual_model.py
```

### 操作流程
1. **启动应用**: 运行主程序，等待模型加载完成
2. **选择图像**: 点击"选择影像文件"按钮选择要分析的图像
3. **开始分析**: 点击"开始AI分析"按钮
4. **查看结果**: 
   - 如果是非CT图像，会显示警告对话框
   - 查看痴呆症分析结果和概率分布
5. **生成报告**: 可选择生成HTML或PDF报告

## 🔧 双模型工作流程

```
输入图像 → CT检测模型 → 图像验证
    ↓                    (类别0=CT图像, 类别1=非CT图像)
如果预测为类别1 → 显示警告 → 用户选择是否继续
    ↓
痴呆症分析模型 → 4类分类 → 结果展示
    ↓
报告生成 (HTML/PDF)
```

### 🎯 CT检测模型说明
- **类别0**: CT图像 - 正常进行痴呆症分析
- **类别1**: 非CT图像 - 显示警告但允许继续分析
- **基于你的训练模型**: 使用你实际训练的 `ct_other_model.h5` 的分类结果

## ⚠️ 重要声明

**本软件仅供研究和参考使用，不能替代专业医学诊断。任何医疗决策都应该咨询合格的医疗专业人员。**

## 🎨 界面特色

- **现代化UI**: 使用CustomTkinter构建的美观界面
- **实时反馈**: 进度条显示分析进度
- **状态指示**: 清晰的模型加载状态显示
- **警告系统**: 智能的图像验证警告
- **响应式设计**: 支持窗口大小调整

## 📈 技术特点

- **异步加载**: 模型在后台异步加载，不阻塞界面
- **错误处理**: 完善的异常处理机制
- **内存优化**: 高效的图像处理和模型推理
- **扩展性**: 模块化设计，易于扩展新功能

## 🔍 故障排除

### 模型加载失败
- 检查模型文件路径是否正确
- 确认模型文件完整性
- 检查TensorFlow版本兼容性

### 图像分析错误
- 确认图像格式支持 (JPG, PNG, BMP)
- 检查图像文件是否损坏
- 验证图像尺寸是否合理

### 报告生成失败
- 检查输出路径权限
- 确认fpdf2库已安装 (PDF报告)
- 验证磁盘空间充足

## 📞 技术支持

如遇到问题，请检查：
1. 运行测试脚本确认系统状态
2. 查看控制台错误信息
3. 确认所有依赖已正确安装

---

© 2024 AI Medical Solutions | 双模型智能医学影像分析系统
