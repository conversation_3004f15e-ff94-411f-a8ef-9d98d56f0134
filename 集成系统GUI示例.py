"""
AI痴呆症识别集成系统 - GUI版本
基于现有GUI代码的集成改造示例
"""

import customtkinter as ctk
import tensorflow as tf
import numpy as np
from PIL import Image, ImageTk
import threading
import os
from tkinter import filedialog, messagebox
import warnings

# 抑制警告
warnings.filterwarnings('ignore')
tf.get_logger().setLevel('ERROR')

class IntegratedAIDetectionApp:
    """集成的AI痴呆症识别GUI应用"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("AI痴呆症识别器 - 集成版 v2.0")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 初始化变量
        self.ct_model = None
        self.dementia_model = None
        self.current_image_path = None
        self.results_history = []
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_models_async()
    
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 AI痴呆症识别器 - 集成版",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="CT图像验证 + 痴呆症智能分析",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 状态显示区域
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", pady=(0, 20))
        
        # 模型状态
        self.model_status_frame = ctk.CTkFrame(self.status_frame)
        self.model_status_frame.pack(side="left", padx=10, pady=10)
        
        ctk.CTkLabel(self.model_status_frame, text="模型状态:", font=ctk.CTkFont(weight="bold")).pack()
        self.ct_model_status = ctk.CTkLabel(self.model_status_frame, text="CT识别: 🔴 未加载")
        self.ct_model_status.pack()
        self.dementia_model_status = ctk.CTkLabel(self.model_status_frame, text="痴呆症分析: 🔴 未加载")
        self.dementia_model_status.pack()
        
        # 系统状态
        self.system_status_frame = ctk.CTkFrame(self.status_frame)
        self.system_status_frame.pack(side="right", padx=10, pady=10)
        
        ctk.CTkLabel(self.system_status_frame, text="系统状态:", font=ctk.CTkFont(weight="bold")).pack()
        self.status_label = ctk.CTkLabel(self.system_status_frame, text="⏳ 正在初始化...")
        self.status_label.pack()
        
        # 主要功能区域
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True)
        
        # 左侧：图像显示和操作
        self.left_frame = ctk.CTkFrame(self.content_frame)
        self.left_frame.pack(side="left", fill="both", expand=True, padx=(10, 5), pady=10)
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_frame)
        self.image_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.image_label = ctk.CTkLabel(
            self.image_frame, 
            text="📷 点击下方按钮选择CT图像",
            font=ctk.CTkFont(size=16)
        )
        self.image_label.pack(expand=True)
        
        # 操作按钮
        self.button_frame = ctk.CTkFrame(self.left_frame)
        self.button_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.select_button = ctk.CTkButton(
            self.button_frame,
            text="📁 选择图像",
            command=self.select_image,
            height=40
        )
        self.select_button.pack(pady=5)
        
        self.analyze_button = ctk.CTkButton(
            self.button_frame,
            text="🔍 开始分析",
            command=self.start_analysis,
            height=40,
            state="disabled"
        )
        self.analyze_button.pack(pady=5)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(self.button_frame)
        self.progress.pack(fill="x", pady=5)
        self.progress.set(0)
        
        # 右侧：结果显示
        self.right_frame = ctk.CTkFrame(self.content_frame)
        self.right_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)
        
        # 结果标题
        ctk.CTkLabel(
            self.right_frame, 
            text="📊 分析结果",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(10, 5))
        
        # 结果显示区域
        self.result_text = ctk.CTkTextbox(
            self.right_frame,
            height=400,
            font=ctk.CTkFont(size=12)
        )
        self.result_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 清空结果按钮
        self.clear_button = ctk.CTkButton(
            self.right_frame,
            text="🗑️ 清空结果",
            command=self.clear_results,
            height=30
        )
        self.clear_button.pack(pady=(0, 10))
    
    def load_models_async(self):
        """异步加载模型"""
        def load_models():
            try:
                # 加载CT识别模型
                ct_model_path = "models/best_ct_detection_model.h5"
                if os.path.exists(ct_model_path):
                    self.ct_model = tf.keras.models.load_model(ct_model_path)
                    self.root.after(0, self.update_ct_model_status, True)
                else:
                    self.root.after(0, self.update_ct_model_status, False)
                
                # 加载痴呆症分类模型
                dementia_model_path = r"D:\模型开发\升级model.h5"
                if os.path.exists(dementia_model_path):
                    self.dementia_model = tf.keras.models.load_model(dementia_model_path)
                    self.root.after(0, self.update_dementia_model_status, True)
                else:
                    self.root.after(0, self.update_dementia_model_status, False)
                
                # 检查系统状态
                if self.ct_model and self.dementia_model:
                    self.root.after(0, self.on_system_ready)
                else:
                    self.root.after(0, self.on_system_error)
                    
            except Exception as e:
                self.root.after(0, self.on_model_error, str(e))
        
        threading.Thread(target=load_models, daemon=True).start()
    
    def update_ct_model_status(self, success):
        """更新CT模型状态"""
        if success:
            self.ct_model_status.configure(text="CT识别: 🟢 已加载", text_color="green")
        else:
            self.ct_model_status.configure(text="CT识别: 🔴 未找到", text_color="red")
    
    def update_dementia_model_status(self, success):
        """更新痴呆症模型状态"""
        if success:
            self.dementia_model_status.configure(text="痴呆症分析: 🟢 已加载", text_color="green")
        else:
            self.dementia_model_status.configure(text="痴呆症分析: 🔴 未找到", text_color="red")
    
    def on_system_ready(self):
        """系统就绪回调"""
        self.status_label.configure(text="✅ 系统就绪", text_color="green")
    
    def on_system_error(self):
        """系统错误回调"""
        self.status_label.configure(text="❌ 系统错误", text_color="red")
        messagebox.showerror("错误", "模型加载失败，请检查模型文件路径")
    
    def on_model_error(self, error):
        """模型加载错误回调"""
        self.status_label.configure(text="❌ 加载失败", text_color="red")
        messagebox.showerror("错误", f"模型加载失败：{error}")
    
    def select_image(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择CT图像",
            filetypes=[
                ("图像文件", "*.jpg *.jpeg *.png *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.current_image_path = file_path
            self.display_image(file_path)
            self.analyze_button.configure(state="normal")
    
    def display_image(self, image_path):
        """显示选择的图像"""
        try:
            # 加载和调整图像大小
            image = Image.open(image_path)
            image.thumbnail((300, 300))
            
            # 转换为CTk可用的格式
            photo = ctk.CTkImage(light_image=image, dark_image=image, size=image.size)
            
            # 更新显示
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"无法显示图像：{e}")
    
    def start_analysis(self):
        """开始分析"""
        if not self.current_image_path:
            messagebox.showwarning("警告", "请先选择图像")
            return
        
        if not (self.ct_model and self.dementia_model):
            messagebox.showerror("错误", "模型未加载完成")
            return
        
        # 禁用按钮，显示进度
        self.analyze_button.configure(state="disabled")
        self.progress.set(0)
        
        # 在后台线程中执行分析
        threading.Thread(target=self.perform_integrated_analysis, daemon=True).start()
    
    def perform_integrated_analysis(self):
        """执行集成分析"""
        try:
            # 步骤1: 图像预处理
            self.root.after(0, lambda: self.progress.set(0.2))
            self.root.after(0, self.update_result, "🔄 正在预处理图像...\n")
            
            # 加载和预处理图像
            image = Image.open(self.current_image_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            image = image.resize((150, 150))
            image_array = np.array(image, dtype=np.float32) / 255.0
            image_array = np.expand_dims(image_array, axis=0)
            
            # 步骤2: CT图像验证
            self.root.after(0, lambda: self.progress.set(0.4))
            self.root.after(0, self.update_result, "🔍 正在验证CT图像...\n")
            
            ct_prediction = self.ct_model.predict(image_array, verbose=0)
            ct_confidence = float(ct_prediction[0][0])
            is_ct = ct_confidence > 0.5
            
            self.root.after(0, self.update_result, f"CT验证结果: {'✅ 通过' if is_ct else '❌ 未通过'}\n")
            self.root.after(0, self.update_result, f"CT置信度: {ct_confidence:.4f} ({ct_confidence*100:.2f}%)\n\n")
            
            if not is_ct:
                self.root.after(0, lambda: self.progress.set(1.0))
                self.root.after(0, self.update_result, "⚠️ 分析终止：输入图像不是CT图像\n")
                self.root.after(0, self.update_result, "💡 建议：请上传CT扫描图像\n")
                self.root.after(0, self.analysis_complete)
                return
            
            # 步骤3: 痴呆症分类
            self.root.after(0, lambda: self.progress.set(0.7))
            self.root.after(0, self.update_result, "🧠 正在进行痴呆症分析...\n")
            
            dementia_prediction = self.dementia_model.predict(image_array, verbose=0)
            predicted_class = np.argmax(dementia_prediction, axis=1)[0]
            confidence = float(np.max(dementia_prediction))
            probabilities = dementia_prediction[0].tolist()
            
            # 步骤4: 结果整理和显示
            self.root.after(0, lambda: self.progress.set(0.9))
            
            predicted_class_name = self.class_labels[predicted_class]
            
            result_text = f"🎯 痴呆症分析结果:\n"
            result_text += f"预测类别: {predicted_class_name}\n"
            result_text += f"置信度: {confidence:.4f} ({confidence*100:.2f}%)\n\n"
            
            result_text += f"📊 详细概率分布:\n"
            for i, prob in enumerate(probabilities):
                result_text += f"{self.class_labels[i]}: {prob:.4f} ({prob*100:.2f}%)\n"
            
            result_text += f"\n💡 建议:\n"
            if confidence > 0.8:
                result_text += "• 预测置信度较高，结果可信度良好\n"
            elif confidence > 0.6:
                result_text += "• 预测置信度中等，建议结合其他检查结果\n"
            else:
                result_text += "• 预测置信度较低，建议进行进一步检查\n"
            
            if 'NonDemented' in predicted_class_name:
                result_text += "• 建议定期进行认知功能检查\n"
            else:
                result_text += "• 建议咨询专业医生进行详细诊断\n"
            
            self.root.after(0, self.update_result, result_text)
            self.root.after(0, lambda: self.progress.set(1.0))
            self.root.after(0, self.analysis_complete)
            
        except Exception as e:
            self.root.after(0, self.update_result, f"❌ 分析失败: {e}\n")
            self.root.after(0, self.analysis_complete)
    
    def update_result(self, text):
        """更新结果显示"""
        self.result_text.insert("end", text)
        self.result_text.see("end")
    
    def analysis_complete(self):
        """分析完成回调"""
        self.analyze_button.configure(state="normal")
        self.update_result(f"\n{'='*50}\n✅ 分析完成\n{'='*50}\n\n")
    
    def clear_results(self):
        """清空结果"""
        self.result_text.delete("1.0", "end")
        self.progress.set(0)
    
    def run(self):
        """运行应用"""
        self.root.mainloop()


def main():
    """主函数"""
    app = IntegratedAIDetectionApp()
    app.run()


if __name__ == "__main__":
    main()
