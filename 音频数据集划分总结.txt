🎵 音频模型训练数据集划分总结
============================================================

📊 原始数据集统计:
  acoustic: 10000 样本, 28 特征
    诊断分布: {'MCI': 5434, 'Normal': 4328, 'Dementia': 238}
  cookie_theft: 10000 样本, 18 特征
    诊断分布: {'MCI': 5434, 'Normal': 4328, 'Dementia': 238}
  semantic_fluency: 10000 样本, 12 特征
    诊断分布: {'MCI': 5434, 'Normal': 4328, 'Dementia': 238}

🎯 创建的训练集类型:

📋 acoustic_binary:
  任务类型: binary_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 25
  类别: ['Normal', 'Cognitive_Impairment']

📋 cookie_theft_binary:
  任务类型: binary_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 15
  类别: ['Normal', 'Cognitive_Impairment']

📋 semantic_fluency_binary:
  任务类型: binary_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 9
  类别: ['Normal', 'Cognitive_Impairment']

📋 acoustic_multiclass:
  任务类型: multiclass_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 25
  类别: ['Normal', 'MCI', 'Dementia']

📋 cookie_theft_multiclass:
  任务类型: multiclass_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 15
  类别: ['Normal', 'MCI', 'Dementia']

📋 semantic_fluency_multiclass:
  任务类型: multiclass_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 9
  类别: ['Normal', 'MCI', 'Dementia']

📋 acoustic_f0_mean_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 24
  目标变量: f0_mean

📋 acoustic_f0_std_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 24
  目标变量: f0_std

📋 acoustic_jitter_percent_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 24
  目标变量: jitter_percent

📋 acoustic_shimmer_percent_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 24
  目标变量: shimmer_percent

📋 acoustic_hnr_db_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 24
  目标变量: hnr_db

📋 cookie_theft_speech_rate_wpm_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 14
  目标变量: speech_rate_wpm

📋 cookie_theft_pause_duration_mean_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 14
  目标变量: pause_duration_mean

📋 cookie_theft_efficiency_ratio_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 14
  目标变量: efficiency_ratio

📋 cookie_theft_coherence_score_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 14
  目标变量: coherence_score

📋 semantic_fluency_semantic_fluency_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 8
  目标变量: semantic_fluency

📋 semantic_fluency_phonemic_fluency_regression:
  任务类型: regression
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 8
  目标变量: phonemic_fluency

📋 acoustic_cv:
  任务类型: cross_validation

📋 cookie_theft_cv:
  任务类型: cross_validation

📋 semantic_fluency_cv:
  任务类型: cross_validation

📋 combined_multimodal:
  任务类型: multimodal_classification
  训练集: 7004 样本
  验证集: 1496 样本
  测试集: 1500 样本
  特征数: 49
