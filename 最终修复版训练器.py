import pandas as pd
import numpy as np
import json
import pickle
import warnings
import time
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import classification_report, accuracy_score, f1_score
from sklearn.preprocessing import LabelEncoder
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression

warnings.filterwarnings('ignore')

print("Starting optimized trainer")
print(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

# Configuration - modify these paths
TRAIN_DATA_PATH = "train_set_scaled.csv"
VALIDATION_DATA_PATH = "validation_set_scaled.csv"
FEATURE_INFO_PATH = "feature_info.json"
LABEL_ENCODER_PATH = "label_encoder.pkl"
OUTPUT_MODEL_PATH = "optimized_dementia_model.pkl"
OUTPUT_REPORT_PATH = "training_report.json"

print(f"Train data: {TRAIN_DATA_PATH}")
print(f"Validation data: {VALIDATION_DATA_PATH}")
print(f"Output model: {OUTPUT_MODEL_PATH}")

# Data loading
print("Loading data...")
try:
    train_data = pd.read_csv(TRAIN_DATA_PATH)
    validation_data = pd.read_csv(VALIDATION_DATA_PATH)
    print(f"Train data: {train_data.shape}")
    print(f"Validation data: {validation_data.shape}")
except Exception as e:
    print(f"Data loading failed: {e}")
    exit(1)

try:
    with open(FEATURE_INFO_PATH, 'r') as f:
        feature_info = json.load(f)
    print("Feature info loaded")
except Exception as e:
    print(f"Feature info loading failed: {e}")
    feature_info = {}

# Create new label encoder to avoid version issues
label_encoder = LabelEncoder()
print("Created new label encoder")

# Data preprocessing
print("Data preprocessing...")

# Comprehensive target column detection
possible_target_cols = [
    'diagnosis_encoded', 'target', 'label', 'class', 'y', 'outcome',
    'diagnosis', 'prediction', 'category', 'classification'
]

target_column = None

# First try exact matches
for col in possible_target_cols:
    if col in train_data.columns:
        target_column = col
        break

# If no exact match, try partial matches
if target_column is None:
    for col in train_data.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['target', 'label', 'class', 'diagnosis', 'encoded']):
            target_column = col
            break

# If still no match, use the last column
if target_column is None:
    target_column = train_data.columns[-1]
    print(f"No standard target column found, using last column: {target_column}")
else:
    print(f"Target column: {target_column}")

print(f"Available columns: {list(train_data.columns)}")
print(f"Total columns: {len(train_data.columns)}")

# Filter out non-numeric columns
numeric_columns = []
non_numeric_columns = []

for col in train_data.columns:
    if col != target_column:
        # Check if column is numeric
        if train_data[col].dtype in ['int64', 'float64', 'int32', 'float32']:
            numeric_columns.append(col)
        else:
            non_numeric_columns.append(col)

feature_columns = numeric_columns
print(f"Numeric features: {len(feature_columns)}")
if non_numeric_columns:
    print(f"Excluded non-numeric columns: {non_numeric_columns}")

# Ensure all feature columns are numeric
X_train = train_data[feature_columns].select_dtypes(include=[np.number])
y_train = train_data[target_column]
X_val = validation_data[feature_columns].select_dtypes(include=[np.number])
y_val = validation_data[target_column]

print(f"Final feature shape - Train: {X_train.shape}, Validation: {X_val.shape}")

# Label encoding
print("Encoding labels...")
y_train_encoded = label_encoder.fit_transform(y_train)
y_val_encoded = label_encoder.transform(y_val)

class_counts = np.bincount(y_train_encoded)
class_names = label_encoder.classes_

print("Class distribution:")
for i, (name, count) in enumerate(zip(class_names, class_counts)):
    percentage = count / len(y_train_encoded) * 100
    print(f"  {name}: {count} ({percentage:.1f}%)")

imbalance_ratio = max(class_counts) / min(class_counts)
print(f"Imbalance ratio: {imbalance_ratio:.2f}:1")

use_smote = imbalance_ratio > 5
if use_smote:
    print("Severe class imbalance detected, will apply oversampling")
else:
    print("Relatively balanced classes")

# Manual oversampling function
def manual_oversample(X, y, target_count_per_class):
    """Manual oversampling when SMOTE is not available"""
    X_resampled = []
    y_resampled = []
    
    for class_label in np.unique(y):
        class_indices = np.where(y == class_label)[0]
        class_X = X.iloc[class_indices]
        class_y = y[class_indices]
        
        current_count = len(class_indices)
        target_count = target_count_per_class
        
        if current_count < target_count:
            repeat_factor = target_count // current_count
            remainder = target_count % current_count
            
            for _ in range(repeat_factor):
                X_resampled.append(class_X)
                y_resampled.append(class_y)
            
            if remainder > 0:
                random_indices = np.random.choice(class_indices, remainder, replace=False)
                X_resampled.append(X.iloc[random_indices])
                y_resampled.append(y[random_indices])
        else:
            X_resampled.append(class_X)
            y_resampled.append(class_y)
    
    X_balanced = pd.concat(X_resampled, ignore_index=True)
    y_balanced = np.concatenate(y_resampled)
    
    return X_balanced, y_balanced

# Class imbalance handling
if use_smote:
    print("Applying oversampling...")
    try:
        # Try SMOTE first
        from imblearn.over_sampling import SMOTE
        
        target_count = max(class_counts)
        sampling_strategy = {i: target_count for i in range(len(class_counts))}
        
        smote = SMOTE(sampling_strategy=sampling_strategy, random_state=42, k_neighbors=min(3, min(class_counts)-1))
        X_train_balanced, y_train_balanced = smote.fit_resample(X_train, y_train_encoded)
        
        balanced_counts = np.bincount(y_train_balanced)
        print(f"SMOTE completed, new distribution: {balanced_counts}")
        
    except ImportError:
        print("imblearn not available, using manual oversampling")
        target_count = max(class_counts)
        X_train_balanced, y_train_balanced = manual_oversample(X_train, y_train_encoded, target_count)
        balanced_counts = np.bincount(y_train_balanced)
        print(f"Manual oversampling completed, new distribution: {balanced_counts}")
        
    except Exception as e:
        print(f"SMOTE failed: {e}, using manual oversampling")
        target_count = max(class_counts)
        X_train_balanced, y_train_balanced = manual_oversample(X_train, y_train_encoded, target_count)
        balanced_counts = np.bincount(y_train_balanced)
        print(f"Manual oversampling completed, new distribution: {balanced_counts}")
else:
    X_train_balanced = X_train
    y_train_balanced = y_train_encoded

# Feature engineering
print("Feature engineering...")

def enhance_features(X):
    # Ensure X contains only numeric data
    X_numeric = X.select_dtypes(include=[np.number])
    X_enhanced = X_numeric.copy()
    
    # Check if we have any numeric columns
    if X_numeric.empty:
        print("Warning: No numeric columns found for feature engineering")
        return X
    
    try:
        # Statistical features
        X_enhanced['feature_mean'] = X_numeric.mean(axis=1)
        X_enhanced['feature_std'] = X_numeric.std(axis=1)
        X_enhanced['feature_max'] = X_numeric.max(axis=1)
        X_enhanced['feature_min'] = X_numeric.min(axis=1)
        X_enhanced['feature_range'] = X_enhanced['feature_max'] - X_enhanced['feature_min']
        
        # Limited feature interactions to avoid memory issues
        important_features = X_numeric.columns[:min(5, len(X_numeric.columns))]
        interaction_count = 0
        
        for i in range(len(important_features)):
            for j in range(i+1, min(i+3, len(important_features))):
                if interaction_count >= 3:  # Reduced to 3 interactions
                    break
                col1, col2 = important_features[i], important_features[j]
                X_enhanced[f'interact_{i}_{j}'] = X_numeric[col1] * X_numeric[col2]
                interaction_count += 1
            if interaction_count >= 3:
                break
        
    except Exception as e:
        print(f"Feature engineering error: {e}")
        return X_numeric
    
    return X_enhanced

X_train_enhanced = enhance_features(X_train_balanced)
X_val_enhanced = enhance_features(X_val)

print(f"Feature enhancement completed: {X_train_enhanced.shape[1]} features")

# Class weights
from sklearn.utils.class_weight import compute_class_weight

class_weights = compute_class_weight('balanced', classes=np.unique(y_train_encoded), y=y_train_encoded)
class_weight_dict = {i: class_weights[i] for i in range(len(class_weights))}

print(f"Class weights: {class_weight_dict}")

# Try to import XGBoost, use alternative if not available
try:
    import xgboost as xgb
    use_xgb = True
    print("XGBoost available")
except ImportError:
    use_xgb = False
    print("XGBoost not available, using RandomForest only")

# Model training
print("Model training...")

models_to_train = {}

# Always train RandomForest
rf_model = RandomForestClassifier(
    n_estimators=300,
    max_depth=15,
    class_weight='balanced',
    random_state=42,
    n_jobs=1  # Changed from -1 to 1 to avoid serialization issues
)

print("Training random forest...")
rf_model.fit(X_train_enhanced, y_train_balanced)
models_to_train['RandomForest'] = rf_model

# Train Logistic Regression
lr_model = LogisticRegression(
    class_weight='balanced',
    random_state=42,
    max_iter=1000,
    n_jobs=1  # Changed from -1 to 1 to avoid serialization issues
)

print("Training logistic regression...")
lr_model.fit(X_train_enhanced, y_train_balanced)
models_to_train['LogisticRegression'] = lr_model

# Train XGBoost if available
if use_xgb:
    xgb_model = xgb.XGBClassifier(
        objective='multi:softmax',
        num_class=len(np.unique(y_train_encoded)),
        random_state=42,
        eval_metric='mlogloss',
        tree_method='hist',
        n_jobs=1,  # Changed from -1 to 1 to avoid serialization issues
        n_estimators=200,
        max_depth=10,
        learning_rate=0.1
    )

    print("Training XGBoost...")
    xgb_model.fit(X_train_enhanced, y_train_balanced)
    models_to_train['XGBoost'] = xgb_model

print("Model training completed")

# Medical evaluation
def medical_evaluation(model, X_test, y_test, model_name="Model"):
    print(f"{model_name} evaluation:")

    y_pred = model.predict(X_test)

    accuracy = accuracy_score(y_test, y_pred)
    f1_weighted = f1_score(y_test, y_pred, average='weighted')

    print(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"  F1 score: {f1_weighted:.4f}")

    report = classification_report(y_test, y_pred, output_dict=True)

    print(f"  Class recall rates:")
    dementia_recall = 0

    for i, class_name in enumerate(class_names):
        if str(i) in report:
            recall = report[str(i)]['recall']
            precision = report[str(i)]['precision']
            print(f"    {class_name}: recall={recall:.3f}, precision={precision:.3f}")

            # Safe check for dementia class
            class_name_str = str(class_name).lower()
            if 'dementia' in class_name_str or i == 0:
                dementia_recall = recall

    if dementia_recall >= 0.8:
        safety_level = "Excellent"
    elif dementia_recall >= 0.6:
        safety_level = "Good"
    elif dementia_recall >= 0.3:
        safety_level = "Fair"
    else:
        safety_level = "Poor"

    print(f"  Medical safety: {safety_level}")
    print(f"  False negative risk: {1-dementia_recall:.3f}")

    return {
        'accuracy': accuracy,
        'f1_weighted': f1_weighted,
        'dementia_recall': dementia_recall,
        'safety_level': safety_level,
        'classification_report': report
    }

# Model evaluation
print("Model evaluation:")

all_results = {}
for name, model in models_to_train.items():
    results = medical_evaluation(model, X_val_enhanced, y_val_encoded, name)
    all_results[name] = results

# Create ensemble if we have multiple models
if len(models_to_train) > 1:
    print("Building ensemble...")
    ensemble_models = [(name, model) for name, model in models_to_train.items()]
    ensemble_model = VotingClassifier(ensemble_models, voting='soft')

    print("Training ensemble...")
    ensemble_model.fit(X_train_enhanced, y_train_balanced)

    ensemble_results = medical_evaluation(ensemble_model, X_val_enhanced, y_val_encoded, "Ensemble")
    all_results['Ensemble'] = ensemble_results
    models_to_train['Ensemble'] = ensemble_model

# Combined score: 70% accuracy + 30% dementia recall
best_score = 0
best_model_name = ""
best_model = None

for name, results in all_results.items():
    score = 0.7 * results['accuracy'] + 0.3 * results['dementia_recall']
    print(f"{name} combined score: {score:.4f}")

    if score > best_score:
        best_score = score
        best_model_name = name
        best_model = models_to_train[name]

print(f"Best model: {best_model_name} (score: {best_score:.4f})")

# Save model and results
print("Saving model and results...")

try:
    with open(OUTPUT_MODEL_PATH, 'wb') as f:
        pickle.dump({
            'model': best_model,
            'label_encoder': label_encoder,
            'feature_columns': list(X_train_enhanced.columns),
            'model_name': best_model_name,
            'training_info': {
                'use_smote': use_smote,
                'feature_engineering': True,
                'class_weights': class_weight_dict
            }
        }, f)
    print(f"Model saved: {OUTPUT_MODEL_PATH}")
except Exception as e:
    print(f"Model save failed: {e}")

training_report = {
    'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
    'best_model': best_model_name,
    'best_score': float(best_score),
    'data_info': {
        'train_samples': len(X_train),
        'validation_samples': len(X_val),
        'features': len(feature_columns),
        'enhanced_features': X_train_enhanced.shape[1],
        'classes': len(class_names),
        'class_distribution': {name: int(count) for name, count in zip(class_names, class_counts)}
    },
    'preprocessing': {
        'used_smote': use_smote,
        'feature_engineering': True,
        'class_weights': {str(k): float(v) for k, v in class_weight_dict.items()}
    },
    'results': {name: {k: float(v) if isinstance(v, (int, float, np.number)) else v
                     for k, v in results.items() if k != 'classification_report'}
               for name, results in all_results.items()},
    'medical_assessment': {
        'best_accuracy': float(all_results[best_model_name]['accuracy']),
        'best_dementia_recall': float(all_results[best_model_name]['dementia_recall']),
        'safety_level': all_results[best_model_name]['safety_level'],
        'clinical_ready': all_results[best_model_name]['dementia_recall'] >= 0.8,
        'recommendation': 'Clinical use approved' if all_results[best_model_name]['dementia_recall'] >= 0.8 else 'Research only'
    }
}

try:
    with open(OUTPUT_REPORT_PATH, 'w', encoding='utf-8') as f:
        json.dump(training_report, f, indent=2, ensure_ascii=False)
    print(f"Training report saved: {OUTPUT_REPORT_PATH}")
except Exception as e:
    print(f"Report save failed: {e}")

# Final summary
print("Training completed!")
print(f"Best model: {best_model_name}")
print(f"Combined score: {best_score:.4f}")
print(f"Accuracy: {all_results[best_model_name]['accuracy']:.4f} ({all_results[best_model_name]['accuracy']*100:.2f}%)")
print(f"Dementia recall: {all_results[best_model_name]['dementia_recall']:.4f} ({all_results[best_model_name]['dementia_recall']*100:.2f}%)")
print(f"Medical safety: {all_results[best_model_name]['safety_level']}")
print(f"Model file: {OUTPUT_MODEL_PATH}")
print(f"Report file: {OUTPUT_REPORT_PATH}")

if all_results[best_model_name]['accuracy'] >= 0.90:
    print("Target achieved: 90% accuracy!")
elif all_results[best_model_name]['accuracy'] >= 0.85:
    print("Medical standard achieved: 85% accuracy!")
else:
    print("Continuing optimization...")

if all_results[best_model_name]['dementia_recall'] >= 0.8:
    print("Dementia detection problem solved!")
elif all_results[best_model_name]['dementia_recall'] >= 0.5:
    print("Dementia detection significantly improved!")
else:
    print("Dementia detection needs further optimization")

print(f"End time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("Optimized trainer completed!")
