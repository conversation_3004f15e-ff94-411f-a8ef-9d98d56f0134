EEG数据集划分总结报告
==================================================

🎯 针对双模型联用的优化划分方案

📊 划分结果:
  TRAIN集: 60 人 (68.2%)
    A (Alzheimer Disease Group): 24 人
    C (Healthy Group): 20 人
    F (Frontotemporal Dementia Group): 16 人

  VAL集: 14 人 (15.9%)
    A (Alzheimer Disease Group): 6 人
    C (Healthy Group): 5 人
    F (Frontotemporal Dementia Group): 3 人

  TEST集: 14 人 (15.9%)
    A (Alzheimer Disease Group): 6 人
    C (Healthy Group): 4 人
    F (Frontotemporal Dementia Group): 4 人

🔗 双模型联用集成建议:
  1. EEG模型: 3分类 (健康/AD/FTD)
  2. 保持现有MRI模型: 4分类 (无痴呆/轻度/中度/非常轻度)
  3. 标签映射: MRI的痴呆类别统一映射到AD
  4. 多模态融合: 加权平均或投票机制
  5. 验证策略: 单模态+多模态双重验证

📁 文件说明:
  train/patient_list.txt - 训练集患者列表
  val/patient_list.txt - 验证集患者列表
  test/patient_list.txt - 测试集患者列表
  split_metadata.json - 完整划分元数据
  train_eeg_template.py - 训练脚本模板
