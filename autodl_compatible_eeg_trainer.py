"""
🧠 AutoDL兼容EEG训练器
专门解决AutoDL环境兼容性问题
"""

import os
import sys
import numpy as np
import pandas as pd

# 检查并安装必要的包
def check_and_install_packages():
    """检查并安装必要的包"""
    required_packages = ['mne', 'scikit-learn']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            os.system(f"pip install {package} -q")

# 运行检查
check_and_install_packages()

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import mne
import warnings
warnings.filterwarnings('ignore')

# 设置TensorFlow日志级别
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

# GPU配置
def setup_gpu():
    """配置GPU"""
    try:
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"🚀 GPU配置成功: {len(gpus)} 个GPU")
            return True
        else:
            print("⚠️ 未检测到GPU，将使用CPU")
            return False
    except Exception as e:
        print(f"⚠️ GPU配置失败: {e}")
        return False

class AutoDLEEGTrainer:
    """AutoDL兼容EEG训练器"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.label_encoder = LabelEncoder()
        
        # EEG参数
        self.sampling_rate = 128
        self.n_channels = 19
        self.n_samples = 128
        
        print("🧠 AutoDL兼容EEG训练器")
        print(f"📁 数据路径: {self.data_path}")
        
        # 检查数据路径
        if not os.path.exists(self.data_path):
            print(f"❌ 数据路径不存在: {self.data_path}")
            print("请确保已运行数据集创建脚本")
            sys.exit(1)
    
    def load_eeg_data(self, split_name):
        """加载EEG数据"""
        print(f"\n📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            print(f"❌ 标签文件不存在: {labels_file}")
            return None, None
        
        # 读取标签
        try:
            labels_df = pd.read_csv(labels_file, sep='\t')
            print(f"📋 找到 {len(labels_df)} 个受试者")
        except Exception as e:
            print(f"❌ 读取标签文件失败: {e}")
            return None, None
        
        # 加载EEG数据
        X_data = []
        y_labels = []
        success_count = 0
        
        for idx, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找.set文件
            set_files = []
            try:
                files = os.listdir(split_dir)
                set_files = [f for f in files if f.startswith(subject_id) and f.endswith('.set')]
            except Exception as e:
                print(f"❌ 读取目录失败: {e}")
                continue
            
            if not set_files:
                print(f"⚠️ 未找到{subject_id}的.set文件")
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                # 使用MNE读取
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                data = raw.get_data()
                
                # 创建epochs
                epochs = self.create_epochs(data)
                
                if len(epochs) > 0:
                    for epoch in epochs:
                        X_data.append(epoch)
                        y_labels.append(label)
                    success_count += 1
                    
                    if success_count % 10 == 0:
                        print(f"✅ 已处理 {success_count} 个受试者")
                
            except Exception as e:
                print(f"❌ 处理{subject_id}失败: {e}")
                continue
        
        if len(X_data) == 0:
            print("❌ 没有成功加载任何数据")
            return None, None
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        print(f"📊 {split_name}数据加载完成:")
        print(f"   数据形状: {X.shape}")
        print(f"   标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    
    def create_epochs(self, data):
        """创建epochs"""
        try:
            n_channels, n_timepoints = data.shape
            n_epochs = max(1, n_timepoints // self.n_samples)
            
            epochs = []
            for i in range(n_epochs):
                start_idx = i * self.n_samples
                end_idx = min(start_idx + self.n_samples, n_timepoints)
                
                if end_idx - start_idx < self.n_samples:
                    # 如果最后一个epoch不够长，跳过
                    break
                
                epoch = data[:, start_idx:end_idx]
                
                # 调整通道数
                if epoch.shape[0] > self.n_channels:
                    epoch = epoch[:self.n_channels, :]
                elif epoch.shape[0] < self.n_channels:
                    padded_epoch = np.zeros((self.n_channels, self.n_samples))
                    padded_epoch[:epoch.shape[0], :] = epoch
                    epoch = padded_epoch
                
                epochs.append(epoch)
            
            return epochs
        except Exception as e:
            print(f"❌ 创建epochs失败: {e}")
            return []
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("🔧 预处理数据...")
        
        try:
            # 标准化
            X_processed = np.zeros_like(X, dtype=np.float32)
            for i in range(X.shape[0]):
                for ch in range(X.shape[1]):
                    channel_data = X[i, ch, :].astype(np.float32)
                    mean = np.mean(channel_data)
                    std = np.std(channel_data)
                    if std > 1e-8:  # 避免除零
                        X_processed[i, ch, :] = (channel_data - mean) / std
                    else:
                        X_processed[i, ch, :] = channel_data
            
            # 编码标签
            if fit_encoder:
                y_encoded = self.label_encoder.fit_transform(y)
                print(f"📋 标签编码: {dict(zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_))))}")
            else:
                y_encoded = self.label_encoder.transform(y)
            
            # 转换为分类标签
            n_classes = len(self.label_encoder.classes_)
            y_categorical = keras.utils.to_categorical(y_encoded, n_classes)
            
            print(f"✅ 预处理完成: {X_processed.shape} -> {y_categorical.shape}")
            return X_processed, y_categorical
            
        except Exception as e:
            print(f"❌ 预处理失败: {e}")
            return None, None
    
    def build_simple_model(self, n_classes):
        """构建简化模型"""
        print(f"🏗️ 构建简化模型 (类别数: {n_classes})...")
        
        try:
            # 输入层
            input_layer = layers.Input(shape=(self.n_channels, self.n_samples, 1))
            
            # 简化的卷积层
            x = layers.Conv2D(16, (1, 5), padding='same', activation='relu')(input_layer)
            x = layers.BatchNormalization()(x)
            x = layers.MaxPooling2D((1, 2))(x)
            x = layers.Dropout(0.2)(x)
            
            x = layers.Conv2D(32, (1, 5), padding='same', activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.MaxPooling2D((1, 2))(x)
            x = layers.Dropout(0.2)(x)
            
            # 全局池化
            x = layers.GlobalAveragePooling2D()(x)
            
            # 分类层
            x = layers.Dense(64, activation='relu')(x)
            x = layers.Dropout(0.3)(x)
            
            # 输出层
            output = layers.Dense(n_classes, activation='softmax')(x)
            
            # 创建模型
            model = keras.Model(inputs=input_layer, outputs=output)
            
            # 编译模型
            model.compile(
                optimizer='adam',
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
            
            print(f"✅ 模型构建完成，参数量: {model.count_params():,}")
            return model
            
        except Exception as e:
            print(f"❌ 模型构建失败: {e}")
            return None
    
    def train_model(self, epochs=50, batch_size=16):
        """训练模型"""
        print(f"🚀 开始训练...")
        
        try:
            # 加载数据
            X_train, y_train = self.load_eeg_data('train')
            X_val, y_val = self.load_eeg_data('val')
            
            if X_train is None or X_val is None:
                print("❌ 数据加载失败")
                return None
            
            # 预处理
            X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
            X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
            
            if X_train is None or X_val is None:
                print("❌ 数据预处理失败")
                return None
            
            # 添加通道维度
            X_train = np.expand_dims(X_train, axis=-1)
            X_val = np.expand_dims(X_val, axis=-1)
            
            # 构建模型
            n_classes = y_train.shape[1]
            self.model = self.build_simple_model(n_classes)
            
            if self.model is None:
                print("❌ 模型构建失败")
                return None
            
            # 简化的回调
            callbacks = [
                keras.callbacks.EarlyStopping(
                    monitor='val_accuracy',
                    patience=10,
                    restore_best_weights=True
                )
            ]
            
            # 训练
            print("🎯 开始训练...")
            self.history = self.model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
            
            # 保存模型
            self.model.save('eeg_model.h5')
            print("💾 模型已保存: eeg_model.h5")
            
            return self.history
            
        except Exception as e:
            print(f"❌ 训练失败: {e}")
            return None
    
    def evaluate_model(self):
        """评估模型"""
        print("📊 评估模型...")
        
        try:
            # 加载测试数据
            X_test, y_test = self.load_eeg_data('test')
            
            if X_test is None:
                print("❌ 测试数据加载失败")
                return None
            
            X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
            
            if X_test is None:
                print("❌ 测试数据预处理失败")
                return None
            
            X_test = np.expand_dims(X_test, axis=-1)
            
            # 预测
            y_pred_prob = self.model.predict(X_test, verbose=0)
            y_pred = np.argmax(y_pred_prob, axis=1)
            y_true = np.argmax(y_test_cat, axis=1)
            
            # 计算指标
            test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
            
            print(f"📈 测试结果:")
            print(f"   准确率: {test_acc:.4f}")
            print(f"   损失: {test_loss:.4f}")
            
            # 分类报告
            class_names = self.label_encoder.classes_
            report = classification_report(y_true, y_pred, target_names=class_names)
            print(f"\n📋 分类报告:")
            print(report)
            
            return {
                'accuracy': test_acc,
                'loss': test_loss
            }
            
        except Exception as e:
            print(f"❌ 评估失败: {e}")
            return None


def main():
    """主函数"""
    print("🧠 AutoDL兼容EEG训练系统")
    print("=" * 50)
    
    # 设置GPU
    gpu_available = setup_gpu()
    
    # 创建训练器
    trainer = AutoDLEEGTrainer()
    
    # 训练模型
    history = trainer.train_model(epochs=50, batch_size=16)
    
    if history is not None:
        # 评估模型
        results = trainer.evaluate_model()
        
        if results is not None:
            print(f"\n🎉 训练完成! 准确率: {results['accuracy']:.4f}")
        else:
            print("\n⚠️ 评估失败，但模型已保存")
    else:
        print("\n❌ 训练失败")


if __name__ == "__main__":
    main()
