# -*- coding: utf-8 -*-
"""
安装屏幕截图功能所需的依赖
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ {package} 安装失败")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ {package} 已安装")
        return True
    except ImportError:
        print(f"⚠️ {package} 未安装")
        return False

def main():
    """主函数"""
    print("🔧 检查屏幕截图功能依赖...")
    print("=" * 40)
    
    # 需要的包
    packages = {
        'pyautogui': 'pyautogui',
        'pillow': 'PIL'  # PIL是Pillow的导入名
    }
    
    need_install = []
    
    # 检查已安装的包
    for package_name, import_name in packages.items():
        if not check_package(import_name):
            need_install.append(package_name)
    
    # 安装缺失的包
    if need_install:
        print(f"\n📦 需要安装以下包: {', '.join(need_install)}")
        print("正在安装...")
        
        for package in need_install:
            install_package(package)
    else:
        print("\n✅ 所有依赖都已安装")
    
    print("\n" + "=" * 40)
    print("🧪 测试屏幕截图功能...")
    
    try:
        import pyautogui
        
        # 测试基本功能
        screen_size = pyautogui.size()
        print(f"✅ 屏幕尺寸: {screen_size}")
        
        # 测试鼠标位置获取
        mouse_pos = pyautogui.position()
        print(f"✅ 鼠标位置: {mouse_pos}")
        
        print("✅ 屏幕截图功能测试通过")
        
    except Exception as e:
        print(f"❌ 屏幕截图功能测试失败: {e}")
    
    print("\n🎯 安装完成！现在可以使用屏幕截图功能了。")

if __name__ == "__main__":
    main()
