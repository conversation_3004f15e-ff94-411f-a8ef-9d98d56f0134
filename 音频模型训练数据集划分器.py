"""
Audio Model Training Dataset Splitter
基于音频数据集创建多种不同类型的训练/验证/测试集
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
import os
import warnings
warnings.filterwarnings('ignore')

class AudioDatasetSplitter:
    def __init__(self, audio_path=r"D:\模型开发\audio"):
        self.audio_path = audio_path
        self.datasets = {}
        self.splits = {}
        
    def load_datasets(self):
        """加载所有CSV数据集"""
        
        print("🔍 加载音频数据集...")
        
        # 加载三个主要数据集
        csv_files = {
            'acoustic': 'acoustic_features_dataset.csv',
            'cookie_theft': 'cookie_theft_dataset.csv', 
            'semantic_fluency': 'semantic_fluency_dataset.csv'
        }
        
        for name, filename in csv_files.items():
            file_path = os.path.join(self.audio_path, filename)
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                self.datasets[name] = df
                print(f"✅ 加载 {name}: {df.shape[0]} 样本, {df.shape[1]} 特征")
                print(f"   诊断分布: {df['diagnosis_name'].value_counts().to_dict()}")
            else:
                print(f"❌ 未找到文件: {filename}")
        
        return self.datasets
    
    def create_binary_classification_splits(self):
        """创建二分类任务 (Normal vs Dementia/MCI)"""
        
        print("\n🎯 创建二分类数据集 (Normal vs Cognitive Impairment)")
        
        splits = {}
        
        for dataset_name, df in self.datasets.items():
            print(f"\n📊 处理数据集: {dataset_name}")
            
            # 创建二分类标签
            df_binary = df.copy()
            df_binary['binary_label'] = df_binary['diagnosis_name'].apply(
                lambda x: 0 if x == 'Normal' else 1
            )
            
            # 准备特征和标签
            feature_cols = [col for col in df_binary.columns 
                          if col not in ['subject_id', 'diagnosis', 'diagnosis_name', 'binary_label']]
            
            X = df_binary[feature_cols]
            y = df_binary['binary_label']
            
            # 数据分割 70% 训练, 15% 验证, 15% 测试
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=0.15, random_state=42, stratify=y
            )
            
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp  # 0.176 * 0.85 ≈ 0.15
            )
            
            splits[f'{dataset_name}_binary'] = {
                'X_train': X_train, 'y_train': y_train,
                'X_val': X_val, 'y_val': y_val,
                'X_test': X_test, 'y_test': y_test,
                'feature_names': feature_cols,
                'task_type': 'binary_classification',
                'classes': ['Normal', 'Cognitive_Impairment']
            }
            
            print(f"   训练集: {X_train.shape[0]} 样本")
            print(f"   验证集: {X_val.shape[0]} 样本") 
            print(f"   测试集: {X_test.shape[0]} 样本")
            print(f"   特征数: {len(feature_cols)}")
        
        return splits
    
    def create_multiclass_classification_splits(self):
        """创建多分类任务 (Normal, MCI, Dementia等)"""
        
        print("\n🎯 创建多分类数据集 (详细诊断分类)")
        
        splits = {}
        
        for dataset_name, df in self.datasets.items():
            print(f"\n📊 处理数据集: {dataset_name}")
            
            # 编码标签
            le = LabelEncoder()
            df_multi = df.copy()
            df_multi['encoded_label'] = le.fit_transform(df_multi['diagnosis_name'])
            
            # 准备特征和标签
            feature_cols = [col for col in df_multi.columns 
                          if col not in ['subject_id', 'diagnosis', 'diagnosis_name', 'encoded_label']]
            
            X = df_multi[feature_cols]
            y = df_multi['encoded_label']
            
            # 检查类别数量
            unique_classes = df_multi['diagnosis_name'].unique()
            if len(unique_classes) < 2:
                print(f"   ⚠️ 跳过 {dataset_name}: 类别数不足")
                continue
            
            # 数据分割
            X_temp, X_test, y_temp, y_test = train_test_split(
                X, y, test_size=0.15, random_state=42, stratify=y
            )
            
            X_train, X_val, y_train, y_val = train_test_split(
                X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp
            )
            
            splits[f'{dataset_name}_multiclass'] = {
                'X_train': X_train, 'y_train': y_train,
                'X_val': X_val, 'y_val': y_val,
                'X_test': X_test, 'y_test': y_test,
                'feature_names': feature_cols,
                'task_type': 'multiclass_classification',
                'classes': list(unique_classes),
                'label_encoder': le
            }
            
            print(f"   训练集: {X_train.shape[0]} 样本")
            print(f"   验证集: {X_val.shape[0]} 样本")
            print(f"   测试集: {X_test.shape[0]} 样本")
            print(f"   类别数: {len(unique_classes)}")
            print(f"   类别: {list(unique_classes)}")
    
        return splits
    
    def create_regression_splits(self):
        """创建回归任务 (预测连续值指标)"""
        
        print("\n🎯 创建回归数据集 (预测连续指标)")
        
        splits = {}
        
        # 定义回归目标
        regression_targets = {
            'acoustic': ['f0_mean', 'f0_std', 'jitter_percent', 'shimmer_percent', 'hnr_db'],
            'cookie_theft': ['speech_rate_wpm', 'pause_duration_mean', 'efficiency_ratio', 'coherence_score'],
            'semantic_fluency': ['semantic_fluency', 'phonemic_fluency']
        }
        
        for dataset_name, df in self.datasets.items():
            if dataset_name not in regression_targets:
                continue
                
            print(f"\n📊 处理数据集: {dataset_name}")
            
            targets = regression_targets[dataset_name]
            available_targets = [t for t in targets if t in df.columns]
            
            if not available_targets:
                print(f"   ⚠️ 跳过 {dataset_name}: 无可用回归目标")
                continue
            
            for target in available_targets:
                # 准备特征和目标
                feature_cols = [col for col in df.columns 
                              if col not in ['subject_id', 'diagnosis', 'diagnosis_name', target]]
                
                X = df[feature_cols].select_dtypes(include=[np.number])  # 只选择数值特征
                y = df[target]
                
                # 移除缺失值
                mask = ~(X.isnull().any(axis=1) | y.isnull())
                X = X[mask]
                y = y[mask]
                
                if len(X) < 10:  # 样本太少
                    continue
                
                # 数据分割
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.15, random_state=42
                )

                X_train, X_val, y_train, y_val = train_test_split(
                    X_train, y_train, test_size=0.176, random_state=42
                )
                
                splits[f'{dataset_name}_{target}_regression'] = {
                    'X_train': X_train, 'y_train': y_train,
                    'X_val': X_val, 'y_val': y_val,
                    'X_test': X_test, 'y_test': y_test,
                    'feature_names': list(X.columns),
                    'target_name': target,
                    'task_type': 'regression'
                }
                
                print(f"   {target} 回归:")
                print(f"     训练集: {X_train.shape[0]} 样本")
                print(f"     验证集: {X_val.shape[0]} 样本")
                print(f"     测试集: {X_test.shape[0]} 样本")

        return splits

    def create_cross_validation_splits(self):
        """创建交叉验证数据集"""

        print("\n🎯 创建交叉验证数据集 (5-Fold)")

        cv_splits = {}

        for dataset_name, df in self.datasets.items():
            print(f"\n📊 处理数据集: {dataset_name}")

            # 准备特征和标签
            feature_cols = [col for col in df.columns
                          if col not in ['subject_id', 'diagnosis', 'diagnosis_name']]

            X = df[feature_cols].select_dtypes(include=[np.number])
            y = df['diagnosis_name']

            # 移除缺失值
            mask = ~(X.isnull().any(axis=1) | y.isnull())
            X = X[mask]
            y = y[mask]

            if len(X) < 10:
                continue

            # 创建5折交叉验证
            skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

            folds = []
            for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X, y)):
                X_train_fold = X.iloc[train_idx]
                X_val_fold = X.iloc[val_idx]
                y_train_fold = y.iloc[train_idx]
                y_val_fold = y.iloc[val_idx]

                folds.append({
                    'fold': fold_idx + 1,
                    'X_train': X_train_fold,
                    'X_val': X_val_fold,
                    'y_train': y_train_fold,
                    'y_val': y_val_fold
                })

            cv_splits[f'{dataset_name}_cv'] = {
                'folds': folds,
                'feature_names': list(X.columns),
                'task_type': 'cross_validation',
                'n_folds': 5
            }

            print(f"   创建 5 折交叉验证")
            print(f"   每折训练集: ~{len(train_idx)} 样本")
            print(f"   每折验证集: ~{len(val_idx)} 样本")

        return cv_splits

    def create_combined_dataset_splits(self):
        """创建组合数据集 (融合多个数据集)"""

        print("\n🎯 创建组合数据集 (多数据集融合)")

        if len(self.datasets) < 2:
            print("   ⚠️ 数据集数量不足，无法创建组合数据集")
            return {}

        # 找到共同的subject_id
        common_subjects = None
        for name, df in self.datasets.items():
            if 'subject_id' in df.columns:
                subjects = set(df['subject_id'])
                if common_subjects is None:
                    common_subjects = subjects
                else:
                    common_subjects = common_subjects.intersection(subjects)

        if not common_subjects or len(common_subjects) < 10:
            print("   ⚠️ 共同样本数量不足")
            return {}

        print(f"   找到 {len(common_subjects)} 个共同样本")

        # 合并数据集
        combined_df = None
        for name, df in self.datasets.items():
            if 'subject_id' not in df.columns:
                continue

            # 筛选共同样本
            df_filtered = df[df['subject_id'].isin(common_subjects)].copy()

            # 重命名特征列以避免冲突
            feature_cols = [col for col in df_filtered.columns
                          if col not in ['subject_id', 'diagnosis', 'diagnosis_name']]

            rename_dict = {col: f"{name}_{col}" for col in feature_cols}
            df_filtered = df_filtered.rename(columns=rename_dict)

            if combined_df is None:
                combined_df = df_filtered
            else:
                combined_df = combined_df.merge(df_filtered, on=['subject_id', 'diagnosis', 'diagnosis_name'])

        if combined_df is None or len(combined_df) < 10:
            print("   ⚠️ 合并后样本数量不足")
            return {}

        # 准备特征和标签
        feature_cols = [col for col in combined_df.columns
                      if col not in ['subject_id', 'diagnosis', 'diagnosis_name']]

        X = combined_df[feature_cols]
        y = combined_df['diagnosis_name']

        # 数据分割
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y, test_size=0.15, random_state=42, stratify=y
        )

        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp
        )

        combined_splits = {
            'combined_multimodal': {
                'X_train': X_train, 'y_train': y_train,
                'X_val': X_val, 'y_val': y_val,
                'X_test': X_test, 'y_test': y_test,
                'feature_names': feature_cols,
                'task_type': 'multimodal_classification',
                'source_datasets': list(self.datasets.keys())
            }
        }

        print(f"   组合数据集创建成功:")
        print(f"     训练集: {X_train.shape[0]} 样本")
        print(f"     验证集: {X_val.shape[0]} 样本")
        print(f"     测试集: {X_test.shape[0]} 样本")
        print(f"     总特征数: {len(feature_cols)}")

        return combined_splits

    def save_splits_summary(self, all_splits):
        """保存数据集划分总结"""

        summary_path = "音频数据集划分总结.txt"

        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("🎵 音频模型训练数据集划分总结\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"📊 原始数据集统计:\n")
            for name, df in self.datasets.items():
                f.write(f"  {name}: {df.shape[0]} 样本, {df.shape[1]} 特征\n")
                f.write(f"    诊断分布: {df['diagnosis_name'].value_counts().to_dict()}\n")
            f.write("\n")

            f.write(f"🎯 创建的训练集类型:\n")
            for split_name, split_data in all_splits.items():
                f.write(f"\n📋 {split_name}:\n")
                f.write(f"  任务类型: {split_data.get('task_type', 'unknown')}\n")

                if 'X_train' in split_data:
                    f.write(f"  训练集: {split_data['X_train'].shape[0]} 样本\n")
                    f.write(f"  验证集: {split_data['X_val'].shape[0]} 样本\n")
                    f.write(f"  测试集: {split_data['X_test'].shape[0]} 样本\n")
                    f.write(f"  特征数: {len(split_data['feature_names'])}\n")

                if 'classes' in split_data:
                    f.write(f"  类别: {split_data['classes']}\n")

                if 'target_name' in split_data:
                    f.write(f"  目标变量: {split_data['target_name']}\n")

        print(f"\n💾 数据集划分总结已保存到: {summary_path}")

    def run_all_splits(self):
        """运行所有数据集划分"""

        print("🚀 开始创建音频模型训练数据集...")

        # 加载数据
        self.load_datasets()

        if not self.datasets:
            print("❌ 没有找到可用的数据集")
            return None

        # 创建各种类型的数据集划分
        all_splits = {}

        # 1. 二分类
        binary_splits = self.create_binary_classification_splits()
        all_splits.update(binary_splits)

        # 2. 多分类
        multiclass_splits = self.create_multiclass_classification_splits()
        all_splits.update(multiclass_splits)

        # 3. 回归
        regression_splits = self.create_regression_splits()
        all_splits.update(regression_splits)

        # 4. 交叉验证
        cv_splits = self.create_cross_validation_splits()
        all_splits.update(cv_splits)

        # 5. 组合数据集
        combined_splits = self.create_combined_dataset_splits()
        all_splits.update(combined_splits)

        # 保存总结
        self.save_splits_summary(all_splits)

        print(f"\n✅ 数据集划分完成! 总共创建了 {len(all_splits)} 种不同的训练集")

        return all_splits

if __name__ == "__main__":
    print("🎵 音频模型训练数据集划分器")
    print("=" * 60)

    # 创建数据集划分器
    splitter = AudioDatasetSplitter()

    # 运行所有划分
    all_splits = splitter.run_all_splits()

    if all_splits:
        print("\n🎯 可用的训练集类型:")
        for i, (name, split_data) in enumerate(all_splits.items(), 1):
            task_type = split_data.get('task_type', 'unknown')
            print(f"  {i}. {name} ({task_type})")

        print("\n💡 接下来可以选择任意数据集进行模型训练!")
    else:
        print("\n❌ 数据集划分失败")
