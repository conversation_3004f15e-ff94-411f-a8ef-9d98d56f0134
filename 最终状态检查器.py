"""
最终状态检查器
汇总所有训练结果，找出最佳模型
"""

import os
import json
import glob
import time

print("📊 最终状态检查器")
print("🔍 汇总所有训练结果")
print(f"⏰ 检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

# 检查所有可能的输出目录
output_dirs = [
    r"D:\模型开发\audio",
    "w",
    "trained_audio_models",
    "."
]

all_results = []

print("📁 扫描输出目录...")

for output_dir in output_dirs:
    if os.path.exists(output_dir):
        print(f"\n📂 检查目录: {output_dir}")
        
        # 查找所有JSON报告文件
        json_files = []
        for pattern in ["*.json", "*report*.json", "*model*.json", "*training*.json"]:
            json_files.extend(glob.glob(os.path.join(output_dir, pattern)))
        
        print(f"   发现 {len(json_files)} 个JSON文件")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                result = {
                    'file': json_file,
                    'directory': output_dir,
                    'data': data
                }
                
                # 提取关键信息
                if 'training_summary' in data:
                    summary = data['training_summary']
                    result['type'] = 'Standard Training'
                    result['method'] = summary.get('best_model', 'Unknown')
                    result['accuracy'] = summary.get('best_accuracy', 0)
                    result['training_time'] = summary.get('training_time_hours', 0)
                
                elif 'innovation_training' in data:
                    innovation = data['innovation_training']
                    result['type'] = 'Innovation Training'
                    result['method'] = innovation.get('best_method', 'Unknown')
                    result['accuracy'] = innovation.get('best_accuracy', 0)
                    result['training_time'] = innovation.get('training_time_hours', 0)
                
                elif 'medical_grade_training' in data:
                    medical = data['medical_grade_training']
                    result['type'] = 'Medical Grade Training'
                    result['method'] = medical.get('best_method', 'Unknown')
                    result['accuracy'] = medical.get('overall_accuracy', 0)
                    result['dementia_recall'] = medical.get('dementia_recall', 0)
                    result['training_time'] = medical.get('training_time_hours', 0)
                
                elif 'super_ensemble_training' in data:
                    super_ens = data['super_ensemble_training']
                    result['type'] = 'Super Ensemble Training'
                    result['method'] = super_ens.get('best_method', 'Unknown')
                    result['accuracy'] = super_ens.get('best_accuracy', 0)
                    result['training_time'] = super_ens.get('training_time_hours', 0)
                
                elif 'imbalanced_data_training' in data:
                    imbalanced = data['imbalanced_data_training']
                    result['type'] = 'Imbalanced Data Training'
                    result['method'] = imbalanced.get('best_method', 'Unknown')
                    result['accuracy'] = imbalanced.get('best_accuracy', 0)
                    result['training_time'] = imbalanced.get('training_time_hours', 0)
                
                elif 'model_info' in data:
                    model_info = data['model_info']
                    result['type'] = 'Model Info'
                    result['method'] = model_info.get('model_name', 'Unknown')
                    result['accuracy'] = data.get('performance', {}).get('accuracy', 0)
                    result['training_time'] = model_info.get('training_duration_hours', 0)
                
                else:
                    # 尝试从其他字段提取信息
                    result['type'] = 'Other'
                    result['method'] = 'Unknown'
                    result['accuracy'] = 0
                    result['training_time'] = 0
                
                if 'accuracy' in result and result['accuracy'] > 0:
                    all_results.append(result)
                    print(f"     ✅ {os.path.basename(json_file)}: {result['type']}, 准确率: {result['accuracy']:.4f}")
                else:
                    print(f"     ⚠️ {os.path.basename(json_file)}: 无法提取准确率信息")
            
            except Exception as e:
                print(f"     ❌ {os.path.basename(json_file)}: 读取失败 ({e})")
        
        # 查找模型文件
        model_files = []
        for pattern in ["*.h5", "*.pkl"]:
            model_files.extend(glob.glob(os.path.join(output_dir, pattern)))
        
        if model_files:
            print(f"   发现 {len(model_files)} 个模型文件:")
            for model_file in model_files:
                file_size = os.path.getsize(model_file) / (1024*1024)  # MB
                mod_time = time.ctime(os.path.getmtime(model_file))
                print(f"     📦 {os.path.basename(model_file)} ({file_size:.2f}MB, {mod_time})")

# 分析结果
print(f"\n📊 训练结果汇总:")
print("=" * 80)

if all_results:
    # 按准确率排序
    all_results.sort(key=lambda x: x.get('accuracy', 0), reverse=True)
    
    print(f"{'排名':<4} {'类型':<25} {'方法':<25} {'准确率':<10} {'训练时间':<10}")
    print("-" * 80)
    
    for i, result in enumerate(all_results, 1):
        accuracy = result.get('accuracy', 0)
        training_time = result.get('training_time', 0)
        
        # 状态标识
        if accuracy >= 0.90:
            status = "🏆"
        elif accuracy >= 0.85:
            status = "✅"
        elif accuracy >= 0.80:
            status = "📈"
        else:
            status = "⚠️"
        
        print(f"{i:<4} {result['type']:<25} {result['method']:<25} {accuracy:<10.4f} {training_time:<10.2f} {status}")
    
    # 最佳结果分析
    best_result = all_results[0]
    print(f"\n🏆 最佳训练结果:")
    print(f"   类型: {best_result['type']}")
    print(f"   方法: {best_result['method']}")
    print(f"   准确率: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
    print(f"   训练时间: {best_result['training_time']:.2f}小时")
    print(f"   文件: {best_result['file']}")
    
    # 特殊分析医疗级结果
    medical_results = [r for r in all_results if r['type'] == 'Medical Grade Training']
    if medical_results:
        print(f"\n🏥 医疗级训练结果:")
        for result in medical_results:
            dementia_recall = result.get('dementia_recall', 0)
            print(f"   方法: {result['method']}")
            print(f"   总体准确率: {result['accuracy']:.4f}")
            print(f"   Dementia召回率: {dementia_recall:.4f}")
    
    # 成就分析
    print(f"\n🎯 成就分析:")
    
    excellent_results = [r for r in all_results if r.get('accuracy', 0) >= 0.90]
    good_results = [r for r in all_results if 0.85 <= r.get('accuracy', 0) < 0.90]
    acceptable_results = [r for r in all_results if 0.80 <= r.get('accuracy', 0) < 0.85]
    
    print(f"   🏆 优秀级 (≥90%): {len(excellent_results)} 个")
    print(f"   ✅ 良好级 (85-90%): {len(good_results)} 个")
    print(f"   📈 可接受级 (80-85%): {len(acceptable_results)} 个")
    
    if excellent_results:
        print(f"\n🎉 恭喜! 达到优秀级标准!")
        print(f"🚀 最高准确率: {best_result['accuracy']*100:.2f}%")
        print(f"🏆 成功方法: {best_result['method']}")
        
        if best_result['accuracy'] >= 0.95:
            print("🌟 突破性成果! 达到95%+准确率!")
        elif best_result['accuracy'] >= 0.92:
            print("🎯 卓越成果! 达到92%+准确率!")
        
    elif good_results:
        print(f"\n✅ 达到良好级标准!")
        print(f"📊 最高准确率: {best_result['accuracy']*100:.2f}%")
        print(f"🔬 适用于医疗辅助诊断!")
        
    elif acceptable_results:
        print(f"\n📈 达到可接受级标准!")
        print(f"📊 最高准确率: {best_result['accuracy']*100:.2f}%")
        print(f"💡 可用于初步筛查!")
        
    else:
        print(f"\n⚠️ 尚未达到理想标准")
        print(f"📊 最高准确率: {best_result['accuracy']*100:.2f}%")
        print(f"🔄 建议继续优化")
    
    # 训练方法效果分析
    print(f"\n📈 训练方法效果分析:")
    
    method_types = {}
    for result in all_results:
        method_type = result['type']
        if method_type not in method_types:
            method_types[method_type] = []
        method_types[method_type].append(result['accuracy'])
    
    for method_type, accuracies in method_types.items():
        avg_acc = sum(accuracies) / len(accuracies)
        max_acc = max(accuracies)
        print(f"   {method_type}: 平均 {avg_acc:.4f}, 最高 {max_acc:.4f}")
    
    # 推荐最佳模型
    print(f"\n💡 推荐使用:")
    print(f"   最佳模型: {best_result['method']}")
    print(f"   训练方法: {best_result['type']}")
    print(f"   模型文件: 查看 {best_result['directory']} 目录")
    
    if best_result['accuracy'] >= 0.85:
        print(f"   🏥 医疗建议: 可用于临床辅助诊断")
    else:
        print(f"   🔍 医疗建议: 适用于研究和初步筛查")

else:
    print("❌ 未发现有效的训练结果")
    print("💡 建议检查训练进程是否正常运行")

print(f"\n⏰ 检查完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("📊 最终状态检查器结束")
