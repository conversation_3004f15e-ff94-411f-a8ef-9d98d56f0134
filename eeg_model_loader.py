"""
🧠 EEG模型加载器
简单易用的EEG模型加载和预测库
"""

import os
import numpy as np
import tensorflow as tf
import joblib
import mne
import warnings
warnings.filterwarnings('ignore')

class EEGModelLoader:
    """EEG模型加载器"""
    
    def __init__(self, model_path="final_complete_eeg_model.h5", 
                 encoder_path="eeg_label_encoder.pkl"):
        """
        初始化EEG模型加载器
        
        Args:
            model_path: 模型文件路径
            encoder_path: 标签编码器路径
        """
        self.model_path = model_path
        self.encoder_path = encoder_path
        self.model = None
        self.label_encoder = None
        self.is_loaded = False
        
        print("🧠 EEG模型加载器初始化")
        
    def load_model(self):
        """加载训练好的模型"""
        try:
            # 加载深度学习模型
            if os.path.exists(self.model_path):
                self.model = tf.keras.models.load_model(self.model_path)
                print(f"✅ 模型加载成功: {self.model_path}")
            else:
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
            # 加载标签编码器
            if os.path.exists(self.encoder_path):
                self.label_encoder = joblib.load(self.encoder_path)
                print(f"✅ 标签编码器加载成功: {self.encoder_path}")
            else:
                raise FileNotFoundError(f"编码器文件不存在: {self.encoder_path}")
            
            self.is_loaded = True
            print(f"🎯 模型信息:")
            print(f"   输入形状: {self.model.input_shape}")
            print(f"   输出类别: {len(self.label_encoder.classes_)}")
            print(f"   类别名称: {list(self.label_encoder.classes_)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def preprocess_eeg_data(self, eeg_data):
        """
        预处理EEG数据
        
        Args:
            eeg_data: EEG数据，形状为 (n_channels, n_timepoints) 或 (n_epochs, n_channels, n_timepoints)
            
        Returns:
            预处理后的数据，形状为 (n_epochs, 19, 128, 1)
        """
        try:
            # 确保数据是numpy数组
            if not isinstance(eeg_data, np.ndarray):
                eeg_data = np.array(eeg_data)
            
            # 处理不同的输入形状
            if len(eeg_data.shape) == 2:
                # 单个样本: (n_channels, n_timepoints)
                eeg_data = eeg_data[np.newaxis, :, :]  # 添加batch维度
            elif len(eeg_data.shape) == 3:
                # 多个样本: (n_epochs, n_channels, n_timepoints)
                pass
            else:
                raise ValueError(f"不支持的数据形状: {eeg_data.shape}")
            
            n_epochs, n_channels, n_timepoints = eeg_data.shape
            
            # 创建标准化的epochs
            processed_epochs = []
            target_channels = 19
            target_samples = 128
            
            for epoch_idx in range(n_epochs):
                epoch = eeg_data[epoch_idx]
                
                # 调整通道数
                if epoch.shape[0] > target_channels:
                    epoch = epoch[:target_channels, :]
                elif epoch.shape[0] < target_channels:
                    padded_epoch = np.zeros((target_channels, epoch.shape[1]))
                    padded_epoch[:epoch.shape[0], :] = epoch
                    epoch = padded_epoch
                
                # 调整时间点数
                if epoch.shape[1] > target_samples:
                    epoch = epoch[:, :target_samples]
                elif epoch.shape[1] < target_samples:
                    padded_epoch = np.zeros((target_channels, target_samples))
                    padded_epoch[:, :epoch.shape[1]] = epoch
                    epoch = padded_epoch
                
                # Z-score标准化
                for ch in range(target_channels):
                    channel_data = epoch[ch, :]
                    mean = np.mean(channel_data)
                    std = np.std(channel_data)
                    if std > 1e-8:
                        epoch[ch, :] = (channel_data - mean) / std
                
                processed_epochs.append(epoch)
            
            # 转换为模型输入格式
            X = np.array(processed_epochs)  # (n_epochs, 19, 128)
            X = np.expand_dims(X, axis=-1)  # (n_epochs, 19, 128, 1)
            
            return X
            
        except Exception as e:
            print(f"❌ 数据预处理失败: {e}")
            return None
    
    def predict(self, eeg_data, return_probabilities=False):
        """
        预测EEG数据的类别
        
        Args:
            eeg_data: EEG数据
            return_probabilities: 是否返回概率分布
            
        Returns:
            预测结果
        """
        if not self.is_loaded:
            print("❌ 模型未加载，请先调用load_model()")
            return None
        
        try:
            # 预处理数据
            X = self.preprocess_eeg_data(eeg_data)
            if X is None:
                return None
            
            # 预测
            predictions = self.model.predict(X, verbose=0)
            
            # 解码预测结果
            predicted_classes = np.argmax(predictions, axis=1)
            predicted_labels = self.label_encoder.inverse_transform(predicted_classes)
            
            if return_probabilities:
                return {
                    'labels': predicted_labels,
                    'probabilities': predictions,
                    'classes': predicted_classes
                }
            else:
                return predicted_labels
                
        except Exception as e:
            print(f"❌ 预测失败: {e}")
            return None
    
    def predict_from_file(self, file_path, return_probabilities=False):
        """
        从.set文件预测
        
        Args:
            file_path: .set文件路径
            return_probabilities: 是否返回概率分布
            
        Returns:
            预测结果
        """
        try:
            # 读取.set文件
            raw = mne.io.read_raw_eeglab(file_path, preload=True, verbose=False)
            data = raw.get_data()
            
            # 预测
            return self.predict(data, return_probabilities)
            
        except Exception as e:
            print(f"❌ 从文件预测失败: {e}")
            return None
    
    def get_model_info(self):
        """获取模型信息"""
        if not self.is_loaded:
            return None
        
        return {
            'model_path': self.model_path,
            'input_shape': self.model.input_shape,
            'output_classes': len(self.label_encoder.classes_),
            'class_names': list(self.label_encoder.classes_),
            'total_parameters': self.model.count_params()
        }


# 简单使用示例
def example_usage():
    """使用示例"""
    print("📖 EEG模型使用示例")
    print("=" * 40)
    
    # 1. 创建加载器
    loader = EEGModelLoader()
    
    # 2. 加载模型
    if loader.load_model():
        print("\n🎯 模型加载成功!")
        
        # 3. 获取模型信息
        info = loader.get_model_info()
        print(f"📊 模型信息: {info}")
        
        # 4. 示例预测（使用随机数据）
        print("\n🔮 示例预测...")
        
        # 创建示例数据 (19通道, 128时间点)
        sample_data = np.random.randn(19, 128)
        
        # 预测
        result = loader.predict(sample_data, return_probabilities=True)
        
        if result is not None:
            print(f"预测标签: {result['labels']}")
            print(f"预测概率: {result['probabilities']}")
        
        # 5. 从文件预测示例
        print("\n📁 从文件预测示例:")
        print("loader.predict_from_file('path/to/your/file.set')")
        
    else:
        print("❌ 模型加载失败")


# 快速预测函数
def quick_predict(eeg_data, model_path="final_complete_eeg_model.h5"):
    """
    快速预测函数
    
    Args:
        eeg_data: EEG数据或文件路径
        model_path: 模型路径
        
    Returns:
        预测结果
    """
    loader = EEGModelLoader(model_path)
    
    if loader.load_model():
        if isinstance(eeg_data, str) and eeg_data.endswith('.set'):
            # 文件路径
            return loader.predict_from_file(eeg_data)
        else:
            # 数据数组
            return loader.predict(eeg_data)
    
    return None


if __name__ == "__main__":
    # 运行使用示例
    example_usage()
