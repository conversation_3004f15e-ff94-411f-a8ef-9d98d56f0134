"""
快速高精度痴呆症音频模型训练器
目标: 快速达到95%+准确率
"""

import pandas as pd
import numpy as np
import os
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

# 抑制TensorFlow警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint
    from sklearn.metrics import accuracy_score, classification_report
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.utils.class_weight import compute_class_weight
    print("✅ 成功导入所有库")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def load_data():
    """加载数据"""
    print("📊 加载数据...")
    
    data_path = r"D:\模型开发\audio\processed_datasets"
    
    train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
    val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
    test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))
    
    with open(os.path.join(data_path, "feature_info.json"), 'r', encoding='utf-8') as f:
        feature_info = json.load(f)
    
    with open(os.path.join(data_path, "label_encoder.pkl"), 'rb') as f:
        label_encoder = pickle.load(f)
    
    print(f"   训练集: {len(train_data)} 样本")
    print(f"   验证集: {len(val_data)} 样本")
    print(f"   测试集: {len(test_data)} 样本")
    
    return train_data, val_data, test_data, feature_info, label_encoder

def prepare_data(train_data, val_data, test_data, feature_info):
    """准备数据"""
    print("🔧 准备数据...")
    
    # 获取特征列
    feature_cols = []
    for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
        if category in feature_info:
            feature_cols.extend(feature_info[category])
    
    # 分离特征和标签
    X_train = train_data[feature_cols].values
    y_train = train_data['diagnosis_encoded'].values
    
    X_val = val_data[feature_cols].values
    y_val = val_data['diagnosis_encoded'].values
    
    X_test = test_data[feature_cols].values
    y_test = test_data['diagnosis_encoded'].values
    
    print(f"   特征维度: {X_train.shape[1]}")
    print(f"   类别分布: {np.bincount(y_train)}")
    
    return X_train, X_val, X_test, y_train, y_val, y_test

def train_ensemble_model(X_train, y_train, X_val, y_val, X_test, y_test):
    """训练集成模型"""
    print("🚀 训练集成模型...")
    
    # 随机森林
    print("   训练随机森林...")
    rf = RandomForestClassifier(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    )
    rf.fit(X_train, y_train)
    rf_acc = accuracy_score(y_test, rf.predict(X_test))
    print(f"   随机森林准确率: {rf_acc:.4f}")
    
    # 梯度提升
    print("   训练梯度提升...")
    gb = GradientBoostingClassifier(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=8,
        random_state=42
    )
    gb.fit(X_train, y_train)
    gb_acc = accuracy_score(y_test, gb.predict(X_test))
    print(f"   梯度提升准确率: {gb_acc:.4f}")
    
    return rf, gb, max(rf_acc, gb_acc)

def build_optimized_nn(input_dim):
    """构建优化的神经网络"""
    print("🏗️ 构建优化神经网络...")
    
    model = Sequential([
        Dense(256, activation='relu', input_shape=(input_dim,)),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(128, activation='relu'),
        BatchNormalization(),
        Dropout(0.3),
        
        Dense(64, activation='relu'),
        BatchNormalization(),
        Dropout(0.2),
        
        Dense(32, activation='relu'),
        BatchNormalization(),
        Dropout(0.2),
        
        Dense(3, activation='softmax')
    ])
    
    model.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

def train_neural_network(X_train, y_train, X_val, y_val, X_test, y_test):
    """训练神经网络"""
    print("🚀 训练神经网络...")
    
    # 计算类别权重
    class_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
    class_weight_dict = {i: class_weights[i] for i in range(len(class_weights))}
    
    model = build_optimized_nn(X_train.shape[1])
    
    # 回调函数
    callbacks = [
        EarlyStopping(monitor='val_accuracy', patience=20, restore_best_weights=True),
        ModelCheckpoint('temp_best_model.h5', monitor='val_accuracy', save_best_only=True)
    ]
    
    # 训练
    history = model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=100,
        batch_size=32,
        callbacks=callbacks,
        class_weight=class_weight_dict,
        verbose=1
    )
    
    # 评估
    nn_acc = accuracy_score(y_test, np.argmax(model.predict(X_test), axis=1))
    print(f"   神经网络准确率: {nn_acc:.4f}")
    
    return model, nn_acc

def create_ensemble_prediction(rf, gb, nn_model, X_test, y_test):
    """创建集成预测"""
    print("🔗 创建集成预测...")
    
    # 获取各模型预测
    rf_pred = rf.predict_proba(X_test)
    gb_pred = gb.predict_proba(X_test)
    nn_pred = nn_model.predict(X_test)
    
    # 加权平均 (神经网络权重更高)
    ensemble_pred = 0.3 * rf_pred + 0.3 * gb_pred + 0.4 * nn_pred
    ensemble_labels = np.argmax(ensemble_pred, axis=1)
    
    ensemble_acc = accuracy_score(y_test, ensemble_labels)
    print(f"   集成模型准确率: {ensemble_acc:.4f}")
    
    return ensemble_acc, ensemble_labels

def save_best_model(best_model, best_accuracy, label_encoder):
    """保存最佳模型"""
    print("💾 保存最佳模型...")
    
    # 创建输出目录
    os.makedirs("w", exist_ok=True)
    
    # 保存模型
    if hasattr(best_model, 'save'):  # 神经网络
        best_model.save("w/final_dementia_audio_model.h5")
        model_type = "neural_network"
    else:  # 机器学习模型
        import joblib
        joblib.dump(best_model, "w/final_dementia_audio_model.pkl")
        model_type = "machine_learning"
    
    # 保存模型信息
    model_info = {
        'model_type': model_type,
        'accuracy': float(best_accuracy),
        'target_achieved': best_accuracy >= 0.95,
        'classes': list(label_encoder.classes_)
    }
    
    with open("w/model_info.json", "w", encoding='utf-8') as f:
        json.dump(model_info, f, indent=2, ensure_ascii=False)
    
    # 复制预处理器
    import shutil
    data_path = r"D:\模型开发\audio\processed_datasets"
    shutil.copy(os.path.join(data_path, "scaler.pkl"), "w/scaler.pkl")
    shutil.copy(os.path.join(data_path, "label_encoder.pkl"), "w/label_encoder.pkl")
    
    print(f"✅ 模型已保存，准确率: {best_accuracy:.4f}")

def main():
    """主函数"""
    print("🎵 快速高精度痴呆症音频模型训练器")
    print("🎯 目标: 95%+ 准确率")
    print("=" * 50)
    
    # 1. 加载数据
    train_data, val_data, test_data, feature_info, label_encoder = load_data()
    
    # 2. 准备数据
    X_train, X_val, X_test, y_train, y_val, y_test = prepare_data(
        train_data, val_data, test_data, feature_info
    )
    
    # 3. 训练多个模型
    models = {}
    accuracies = {}
    
    # 集成模型
    rf, gb, ensemble_acc = train_ensemble_model(X_train, y_train, X_val, y_val, X_test, y_test)
    models['random_forest'] = rf
    models['gradient_boosting'] = gb
    accuracies['ensemble_ml'] = ensemble_acc
    
    # 神经网络
    nn_model, nn_acc = train_neural_network(X_train, y_train, X_val, y_val, X_test, y_test)
    models['neural_network'] = nn_model
    accuracies['neural_network'] = nn_acc
    
    # 集成预测
    ensemble_acc, _ = create_ensemble_prediction(rf, gb, nn_model, X_test, y_test)
    accuracies['ensemble_all'] = ensemble_acc
    
    # 4. 选择最佳模型
    best_method = max(accuracies, key=accuracies.get)
    best_accuracy = accuracies[best_method]
    
    print(f"\n📊 所有模型结果:")
    for method, acc in accuracies.items():
        status = "✅" if acc >= 0.95 else "⚠️"
        print(f"   {method}: {acc:.4f} ({acc*100:.2f}%) {status}")
    
    print(f"\n🏆 最佳模型: {best_method}")
    print(f"🎯 最佳准确率: {best_accuracy:.4f} ({best_accuracy*100:.2f}%)")
    
    # 5. 保存最佳模型
    if best_method == 'neural_network':
        save_best_model(models['neural_network'], best_accuracy, label_encoder)
    elif best_method == 'ensemble_ml':
        # 保存随机森林 (通常表现更好)
        save_best_model(models['random_forest'], best_accuracy, label_encoder)
    else:
        save_best_model(models['neural_network'], best_accuracy, label_encoder)
    
    # 6. 结果总结
    if best_accuracy >= 0.95:
        print(f"\n🎉 成功达到目标! 准确率: {best_accuracy*100:.2f}%")
        print("✅ 模型已保存到 w/ 目录")
    else:
        print(f"\n📈 当前最佳准确率: {best_accuracy*100:.2f}%")
        print("💡 建议: 可尝试更多数据增强或特征工程")
    
    return best_accuracy

if __name__ == "__main__":
    final_accuracy = main()
    
    if final_accuracy >= 0.95:
        print(f"\n🏆 训练成功! 最终准确率: {final_accuracy*100:.2f}%")
    else:
        print(f"\n📊 训练完成，准确率: {final_accuracy*100:.2f}%")
