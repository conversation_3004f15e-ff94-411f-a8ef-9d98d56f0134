"""
High Performance Dementia Audio Model Trainer
高性能痴呆症音频模型训练器 - 目标准确率95%+
"""

import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model, load_model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input, Concatenate, LayerNormalization
from tensorflow.keras.optimizers import Adam, AdamW
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint, LearningRateScheduler
from tensorflow.keras.regularizers import l1_l2
from sklearn.metrics import classification_report, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
import os
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

# 抑制TensorFlow警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class HighPerformanceDementiaTrainer:
    def __init__(self, data_path=r"D:\模型开发\audio\processed_datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.target_accuracy = 0.95
        
        # 设置GPU优化
        self.setup_gpu()
        
    def setup_gpu(self):
        """优化GPU设置"""
        print("🔧 优化GPU设置...")
        
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                # 启用混合精度训练
                tf.keras.mixed_precision.set_global_policy('mixed_float16')
                print(f"✅ GPU优化完成，启用混合精度训练")
            except RuntimeError as e:
                print(f"⚠️ GPU设置失败: {e}")
        else:
            print("⚠️ 使用CPU训练")
    
    def load_data(self):
        """加载数据"""
        print("📊 加载训练数据...")
        
        try:
            self.train_data = pd.read_csv(os.path.join(self.data_path, "train_set_scaled.csv"))
            self.val_data = pd.read_csv(os.path.join(self.data_path, "validation_set_scaled.csv"))
            self.test_data = pd.read_csv(os.path.join(self.data_path, "test_set_scaled.csv"))
            
            with open(os.path.join(self.data_path, "feature_info.json"), 'r', encoding='utf-8') as f:
                self.feature_info = json.load(f)
            
            with open(os.path.join(self.data_path, "label_encoder.pkl"), 'rb') as f:
                self.label_encoder = pickle.load(f)
            
            print(f"   训练集: {len(self.train_data)} 样本")
            print(f"   验证集: {len(self.val_data)} 样本")
            print(f"   测试集: {len(self.test_data)} 样本")
            
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def prepare_data_with_augmentation(self):
        """准备数据并进行数据增强"""
        print("🔧 准备数据并进行增强...")
        
        # 获取特征列
        feature_cols = []
        for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
            if category in self.feature_info:
                feature_cols.extend(self.feature_info[category])
        
        # 原始数据
        X_train = self.train_data[feature_cols].values
        y_train = self.train_data['diagnosis_encoded'].values
        
        X_val = self.val_data[feature_cols].values
        y_val = self.val_data['diagnosis_encoded'].values
        
        X_test = self.test_data[feature_cols].values
        y_test = self.test_data['diagnosis_encoded'].values
        
        # 数据增强 - 添加噪声
        print("   应用数据增强...")
        noise_factor = 0.01
        X_train_augmented = []
        y_train_augmented = []
        
        for i in range(len(X_train)):
            # 原始样本
            X_train_augmented.append(X_train[i])
            y_train_augmented.append(y_train[i])
            
            # 增强样本1: 添加高斯噪声
            noise = np.random.normal(0, noise_factor, X_train[i].shape)
            X_train_augmented.append(X_train[i] + noise)
            y_train_augmented.append(y_train[i])
            
            # 增强样本2: 特征缩放
            scale = np.random.uniform(0.95, 1.05, X_train[i].shape)
            X_train_augmented.append(X_train[i] * scale)
            y_train_augmented.append(y_train[i])
        
        X_train_aug = np.array(X_train_augmented)
        y_train_aug = np.array(y_train_augmented)
        
        print(f"   增强后训练集: {len(X_train_aug)} 样本 (原始: {len(X_train)})")
        
        # 转换为分类标签
        self.X_train = X_train_aug
        self.y_train = y_train_aug
        self.X_val = X_val
        self.y_val = y_val
        self.X_test = X_test
        self.y_test = y_test
        
        self.y_train_cat = tf.keras.utils.to_categorical(self.y_train, num_classes=3)
        self.y_val_cat = tf.keras.utils.to_categorical(self.y_val, num_classes=3)
        self.y_test_cat = tf.keras.utils.to_categorical(self.y_test, num_classes=3)
        
        # 计算类别权重
        self.class_weights = compute_class_weight(
            'balanced',
            classes=np.unique(self.y_train),
            y=self.y_train
        )
        self.class_weight_dict = {i: self.class_weights[i] for i in range(len(self.class_weights))}
        
        print(f"   类别权重: {self.class_weight_dict}")
    
    def build_advanced_model(self):
        """构建高级深度学习模型"""
        print("🏗️ 构建高性能模型...")
        
        input_dim = self.X_train.shape[1]
        input_layer = Input(shape=(input_dim,), name='features_input')
        
        # 特征分组处理
        demo_size = len(self.feature_info['demographic'])
        acoustic_size = len(self.feature_info['acoustic'])
        mfcc_size = len(self.feature_info['mfcc'])
        linguistic_size = len(self.feature_info['linguistic'])
        
        # 分离特征
        demo_features = input_layer[:, :demo_size]
        acoustic_features = input_layer[:, demo_size:demo_size+acoustic_size]
        mfcc_features = input_layer[:, demo_size+acoustic_size:demo_size+acoustic_size+mfcc_size]
        linguistic_features = input_layer[:, demo_size+acoustic_size+mfcc_size:]
        
        # 人口学特征分支 - 简单处理
        demo_branch = Dense(32, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(demo_features)
        demo_branch = LayerNormalization()(demo_branch)
        demo_branch = Dropout(0.2)(demo_branch)
        
        # 声学特征分支 - 深度处理
        acoustic_branch = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(acoustic_features)
        acoustic_branch = LayerNormalization()(acoustic_branch)
        acoustic_branch = Dropout(0.3)(acoustic_branch)
        acoustic_branch = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(acoustic_branch)
        acoustic_branch = LayerNormalization()(acoustic_branch)
        acoustic_branch = Dropout(0.2)(acoustic_branch)
        
        # MFCC特征分支 - 深度处理
        mfcc_branch = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(mfcc_features)
        mfcc_branch = LayerNormalization()(mfcc_branch)
        mfcc_branch = Dropout(0.3)(mfcc_branch)
        mfcc_branch = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(mfcc_branch)
        mfcc_branch = LayerNormalization()(mfcc_branch)
        mfcc_branch = Dropout(0.2)(mfcc_branch)
        
        # 语言特征分支 - 深度处理
        linguistic_branch = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(linguistic_features)
        linguistic_branch = LayerNormalization()(linguistic_branch)
        linguistic_branch = Dropout(0.3)(linguistic_branch)
        linguistic_branch = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(linguistic_branch)
        linguistic_branch = LayerNormalization()(linguistic_branch)
        linguistic_branch = Dropout(0.2)(linguistic_branch)
        
        # 特征融合
        merged = Concatenate(name='feature_fusion')([demo_branch, acoustic_branch, mfcc_branch, linguistic_branch])
        
        # 深度融合网络
        x = Dense(256, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(merged)
        x = LayerNormalization()(x)
        x = Dropout(0.4)(x)
        
        x = Dense(128, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = LayerNormalization()(x)
        x = Dropout(0.3)(x)
        
        x = Dense(64, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = LayerNormalization()(x)
        x = Dropout(0.2)(x)
        
        x = Dense(32, activation='relu', kernel_regularizer=l1_l2(l1=1e-5, l2=1e-4))(x)
        x = LayerNormalization()(x)
        x = Dropout(0.1)(x)
        
        # 输出层
        output = Dense(3, activation='softmax', name='classification_output', dtype='float32')(x)
        
        self.model = Model(inputs=input_layer, outputs=output, name='HighPerformanceDementiaClassifier')
        
        # 使用AdamW优化器
        optimizer = AdamW(learning_rate=0.001, weight_decay=1e-4)
        
        self.model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        print(f"   模型参数: {self.model.count_params():,}")
        return self.model

    def create_advanced_callbacks(self):
        """创建高级回调函数"""

        # 学习率调度
        def lr_schedule(epoch, lr):
            if epoch < 20:
                return lr
            elif epoch < 50:
                return lr * 0.5
            elif epoch < 80:
                return lr * 0.1
            else:
                return lr * 0.01

        callbacks = [
            EarlyStopping(
                monitor='val_accuracy',
                patience=25,
                restore_best_weights=True,
                verbose=1,
                min_delta=0.001
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.3,
                patience=10,
                min_lr=1e-8,
                verbose=1
            ),
            ModelCheckpoint(
                filepath='best_high_performance_dementia_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1,
                save_weights_only=False
            ),
            LearningRateScheduler(lr_schedule, verbose=1)
        ]

        return callbacks

    def train_until_target_accuracy(self, max_epochs=200, batch_size=16):
        """训练直到达到目标准确率"""
        print(f"🚀 开始训练，目标准确率: {self.target_accuracy*100:.1f}%")

        callbacks = self.create_advanced_callbacks()

        # 检查是否有已保存的模型
        if os.path.exists('best_high_performance_dementia_model.h5'):
            print("🔄 发现已保存的模型，继续训练...")
            try:
                self.model = load_model('best_high_performance_dementia_model.h5')
                print("✅ 成功加载已保存的模型")
            except:
                print("⚠️ 加载模型失败，使用新模型")

        best_accuracy = 0
        training_round = 1

        while best_accuracy < self.target_accuracy and training_round <= 5:
            print(f"\n🔄 第 {training_round} 轮训练...")

            # 训练模型
            history = self.model.fit(
                self.X_train, self.y_train_cat,
                validation_data=(self.X_val, self.y_val_cat),
                epochs=max_epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                class_weight=self.class_weight_dict,
                verbose=1
            )

            # 评估当前性能
            val_accuracy = max(history.history['val_accuracy'])
            print(f"   第 {training_round} 轮最佳验证准确率: {val_accuracy:.4f}")

            if val_accuracy > best_accuracy:
                best_accuracy = val_accuracy

            # 如果达到目标，在测试集上验证
            if best_accuracy >= self.target_accuracy:
                test_accuracy = self.evaluate_on_test_set()
                if test_accuracy >= self.target_accuracy:
                    print(f"🎯 达到目标！测试准确率: {test_accuracy:.4f}")
                    break
                else:
                    print(f"⚠️ 测试准确率 {test_accuracy:.4f} 未达标，继续训练...")
                    best_accuracy = test_accuracy

            training_round += 1

            # 如果没达到目标，调整学习率重新训练
            if best_accuracy < self.target_accuracy:
                print("🔧 调整模型参数，准备下一轮训练...")
                # 降低学习率
                current_lr = float(tf.keras.backend.get_value(self.model.optimizer.learning_rate))
                new_lr = current_lr * 0.5
                tf.keras.backend.set_value(self.model.optimizer.learning_rate, new_lr)
                print(f"   学习率调整: {current_lr:.2e} -> {new_lr:.2e}")

        return best_accuracy

    def evaluate_on_test_set(self):
        """在测试集上评估"""
        print("📊 在测试集上评估...")

        y_pred_proba = self.model.predict(self.X_test, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)

        accuracy = accuracy_score(self.y_test, y_pred)
        print(f"   测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

        # 详细报告
        class_names = self.label_encoder.classes_
        report = classification_report(
            self.y_test, y_pred,
            target_names=class_names,
            output_dict=True
        )

        for class_name in class_names:
            metrics = report[class_name]
            print(f"   {class_name}: P={metrics['precision']:.3f}, R={metrics['recall']:.3f}, F1={metrics['f1-score']:.3f}")

        return accuracy

    def save_final_model(self):
        """保存最终模型"""
        print("💾 保存最终模型...")

        # 保存到指定路径
        final_model_path = "w/final_dementia_audio_model.h5"
        os.makedirs("w", exist_ok=True)

        self.model.save(final_model_path)

        # 保存模型信息
        model_info = {
            'model_path': final_model_path,
            'target_accuracy': self.target_accuracy,
            'feature_info': self.feature_info,
            'class_names': list(self.label_encoder.classes_),
            'model_parameters': int(self.model.count_params())
        }

        with open("w/model_info.json", "w", encoding='utf-8') as f:
            json.dump(model_info, f, indent=2, ensure_ascii=False)

        # 保存预处理器
        import shutil
        shutil.copy(os.path.join(self.data_path, "scaler.pkl"), "w/scaler.pkl")
        shutil.copy(os.path.join(self.data_path, "label_encoder.pkl"), "w/label_encoder.pkl")

        print(f"✅ 模型已保存到: {final_model_path}")
        print("📁 相关文件:")
        print("   - w/final_dementia_audio_model.h5 (主模型)")
        print("   - w/model_info.json (模型信息)")
        print("   - w/scaler.pkl (特征标准化器)")
        print("   - w/label_encoder.pkl (标签编码器)")

    def run_high_performance_training(self):
        """运行高性能训练流程"""
        print("🎵 高性能痴呆症音频模型训练器")
        print("=" * 60)
        print(f"🎯 目标准确率: {self.target_accuracy*100:.1f}%")

        try:
            # 1. 加载数据
            if not self.load_data():
                return None

            # 2. 准备数据和增强
            self.prepare_data_with_augmentation()

            # 3. 构建高级模型
            self.build_advanced_model()

            # 4. 训练直到达到目标
            final_accuracy = self.train_until_target_accuracy()

            # 5. 最终评估
            test_accuracy = self.evaluate_on_test_set()

            # 6. 保存模型
            if test_accuracy >= self.target_accuracy:
                self.save_final_model()
                print(f"\n🎉 训练成功完成!")
                print(f"🎯 最终测试准确率: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
                print(f"✅ 达到目标准确率 {self.target_accuracy*100:.1f}%")
            else:
                print(f"\n⚠️ 未完全达到目标准确率")
                print(f"🎯 最终测试准确率: {test_accuracy:.4f} ({test_accuracy*100:.2f}%)")
                print(f"📊 目标准确率: {self.target_accuracy*100:.1f}%")

                # 仍然保存模型
                self.save_final_model()
                print("💾 模型已保存，可继续优化")

            return test_accuracy

        except Exception as e:
            print(f"❌ 训练失败: {str(e)}")
            return None

if __name__ == "__main__":
    # 创建高性能训练器
    trainer = HighPerformanceDementiaTrainer()

    # 运行训练
    final_accuracy = trainer.run_high_performance_training()

    if final_accuracy:
        if final_accuracy >= 0.95:
            print(f"\n🏆 恭喜！成功达到95%+准确率: {final_accuracy*100:.2f}%")
        else:
            print(f"\n📈 当前准确率: {final_accuracy*100:.2f}%，可继续优化")
    else:
        print("\n❌ 训练失败")
