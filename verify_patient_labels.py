"""
🔍 精确验证患者标签信息
确保我们正确理解了数据集中每个患者的真实标签
"""

import os
import zipfile
import json
import pandas as pd
from collections import Counter

class PatientLabelVerifier:
    """患者标签验证器"""
    
    def __init__(self, zip_path="D:/模型开发/EEG.zip"):
        self.zip_path = zip_path
        self.patient_labels = {}
        self.label_definitions = {}
        
    def extract_exact_labels(self):
        """精确提取患者标签"""
        print("🔍 精确验证患者标签信息")
        print("=" * 60)
        
        if not os.path.exists(self.zip_path):
            print(f"❌ 数据集文件不存在: {self.zip_path}")
            return False
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                # 1. 首先读取标签定义
                self.read_label_definitions(zip_ref)
                
                # 2. 读取患者标签数据
                self.read_patient_data(zip_ref)
                
                # 3. 验证和显示结果
                self.verify_and_display_results()
                
                return True
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def read_label_definitions(self, zip_ref):
        """读取标签定义"""
        print("📋 读取标签定义...")
        
        try:
            # 读取participants.json获取标签定义
            with zip_ref.open('dataset/participants.json') as f:
                participants_json = json.load(f)
            
            if 'Group' in participants_json:
                group_info = participants_json['Group']
                if 'Levels' in group_info:
                    self.label_definitions = group_info['Levels']
                    print("✅ 找到标签定义:")
                    for code, description in self.label_definitions.items():
                        print(f"   {code}: {description}")
                else:
                    print("⚠️ 未找到Levels定义")
            else:
                print("⚠️ 未找到Group定义")
                
        except Exception as e:
            print(f"⚠️ 读取标签定义失败: {e}")
    
    def read_patient_data(self, zip_ref):
        """读取患者数据"""
        print(f"\n📊 读取患者数据...")
        
        try:
            # 读取participants.tsv获取患者标签
            with zip_ref.open('dataset/participants.tsv') as f:
                content = f.read().decode('utf-8')
            
            # 解析TSV数据
            lines = content.strip().split('\n')
            header = lines[0].split('\t')
            
            print(f"📋 数据列: {header}")
            
            # 找到关键列的索引
            participant_col = header.index('participant_id') if 'participant_id' in header else 0
            group_col = header.index('Group') if 'Group' in header else -1
            
            if group_col == -1:
                print("❌ 未找到Group列")
                return
            
            # 解析每行数据
            for line in lines[1:]:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) > max(participant_col, group_col):
                        participant_id = parts[participant_col].strip()
                        group_label = parts[group_col].strip()
                        self.patient_labels[participant_id] = group_label
            
            print(f"✅ 成功读取 {len(self.patient_labels)} 个患者的标签")
            
        except Exception as e:
            print(f"❌ 读取患者数据失败: {e}")
    
    def verify_and_display_results(self):
        """验证和显示结果"""
        print(f"\n🎯 患者标签验证结果")
        print("=" * 60)
        
        if not self.patient_labels:
            print("❌ 没有患者标签数据")
            return
        
        # 统计各组人数
        label_counts = Counter(self.patient_labels.values())
        
        print(f"📊 患者分组统计:")
        total_patients = len(self.patient_labels)
        
        for label_code, count in sorted(label_counts.items()):
            if label_code in self.label_definitions:
                description = self.label_definitions[label_code]
                percentage = (count / total_patients) * 100
                print(f"   {label_code} - {description}: {count} 人 ({percentage:.1f}%)")
            else:
                print(f"   {label_code} - 未知标签: {count} 人")
        
        print(f"\n📋 总患者数: {total_patients}")
        
        # 显示每个患者的具体标签
        print(f"\n👥 每个患者的标签分配:")
        print("-" * 60)
        
        # 按组分类显示
        for label_code in sorted(label_counts.keys()):
            if label_code in self.label_definitions:
                description = self.label_definitions[label_code]
                print(f"\n🏷️ {label_code} - {description}:")
                
                patients_in_group = [pid for pid, label in self.patient_labels.items() if label == label_code]
                patients_in_group.sort()
                
                # 每行显示10个患者ID
                for i in range(0, len(patients_in_group), 10):
                    batch = patients_in_group[i:i+10]
                    print(f"   {', '.join(batch)}")
        
        # 验证数据完整性
        self.validate_data_integrity()
    
    def validate_data_integrity(self):
        """验证数据完整性"""
        print(f"\n🔍 数据完整性验证:")
        
        # 检查是否有88个患者
        expected_count = 88
        actual_count = len(self.patient_labels)
        
        if actual_count == expected_count:
            print(f"✅ 患者数量正确: {actual_count}/{expected_count}")
        else:
            print(f"⚠️ 患者数量不匹配: {actual_count}/{expected_count}")
        
        # 检查患者ID连续性
        patient_numbers = []
        for pid in self.patient_labels.keys():
            if pid.startswith('sub-'):
                try:
                    num = int(pid.split('-')[1])
                    patient_numbers.append(num)
                except:
                    print(f"⚠️ 异常患者ID: {pid}")
        
        patient_numbers.sort()
        
        if patient_numbers:
            min_num = min(patient_numbers)
            max_num = max(patient_numbers)
            expected_range = list(range(min_num, max_num + 1))
            
            if patient_numbers == expected_range:
                print(f"✅ 患者ID连续: sub-{min_num:03d} 到 sub-{max_num:03d}")
            else:
                missing = set(expected_range) - set(patient_numbers)
                if missing:
                    print(f"⚠️ 缺失患者ID: {sorted(missing)}")
        
        # 检查标签有效性
        valid_labels = set(self.label_definitions.keys()) if self.label_definitions else set()
        actual_labels = set(self.patient_labels.values())
        
        invalid_labels = actual_labels - valid_labels
        if invalid_labels:
            print(f"⚠️ 无效标签: {invalid_labels}")
        else:
            print(f"✅ 所有标签都有效")
    
    def generate_training_split_recommendation(self):
        """生成训练集划分建议 - 针对双模型联用优化"""
        print(f"\n📊 EEG模型训练集划分建议 (双模型联用优化)")
        print("=" * 60)

        if not self.patient_labels:
            print("❌ 没有患者标签数据")
            return

        label_counts = Counter(self.patient_labels.values())
        total_patients = len(self.patient_labels)

        print(f"🎯 针对双模型联用的优化划分方案 (训练:验证:测试 = 7:1.5:1.5):")
        print(f"💡 理由: EEG信号复杂度高，需要更多训练数据以确保与MRI模型良好融合")
        print()

        total_train = 0
        total_val = 0
        total_test = 0

        for label_code, count in sorted(label_counts.items()):
            if label_code in self.label_definitions:
                description = self.label_definitions[label_code]

                # 优化的划分比例：70% 训练，15% 验证，15% 测试
                train_count = int(count * 0.70)
                val_count = int(count * 0.15)
                test_count = count - train_count - val_count

                # 确保每个组至少有最小样本数
                if val_count < 3 and count >= 6:
                    val_count = 3
                    test_count = count - train_count - val_count
                if test_count < 2 and count >= 4:
                    test_count = 2
                    train_count = count - val_count - test_count

                total_train += train_count
                total_val += val_count
                total_test += test_count

                print(f"🏷️ {label_code} - {description} (总计 {count} 人):")
                print(f"   训练集: {train_count} 人 ({train_count/count*100:.1f}%)")
                print(f"   验证集: {val_count} 人 ({val_count/count*100:.1f}%)")
                print(f"   测试集: {test_count} 人 ({test_count/count*100:.1f}%)")
                print()

        print(f"📋 总计:")
        print(f"   训练集: {total_train} 人 ({total_train/total_patients*100:.1f}%)")
        print(f"   验证集: {total_val} 人 ({total_val/total_patients*100:.1f}%)")
        print(f"   测试集: {total_test} 人 ({total_test/total_patients*100:.1f}%)")
        print(f"   总计: {total_patients} 人")

        print(f"\n🔗 双模型联用集成建议:")
        print(f"   1. EEG模型: 3分类 (健康/AD/FTD)")
        print(f"   2. 保持现有MRI模型: 4分类 (无痴呆/轻度/中度/非常轻度)")
        print(f"   3. 标签映射: MRI的痴呆类别统一映射到AD")
        print(f"   4. 多模态融合: 加权平均或投票机制")
        print(f"   5. 验证策略: 单模态+多模态双重验证")
    
    def save_verified_labels(self, save_path="verified_patient_labels.json"):
        """保存验证后的标签"""
        result = {
            'label_definitions': self.label_definitions,
            'patient_labels': self.patient_labels,
            'statistics': dict(Counter(self.patient_labels.values())),
            'total_patients': len(self.patient_labels)
        }
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 验证结果已保存: {save_path}")
        except Exception as e:
            print(f"\n❌ 保存失败: {e}")


def main():
    """主函数"""
    print("🔍 患者标签精确验证器")
    print("=" * 50)
    
    verifier = PatientLabelVerifier()
    
    if verifier.extract_exact_labels():
        verifier.generate_training_split_recommendation()
        verifier.save_verified_labels()
    
    print(f"\n" + "=" * 60)
    print(f"✅ 验证完成!")
    print(f"📋 现在我们有了每个患者的确切标签信息")
    print(f"=" * 60)


if __name__ == "__main__":
    main()
