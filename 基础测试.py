#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础测试 - 不依赖TensorFlow
"""

import os
import sys

def main():
    print("🚀 基础功能测试")
    print("=" * 30)
    
    # 基本信息
    print(f"Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"Python路径: {sys.executable}")
    
    # 检查文件
    model_path = r"D:\模型开发\升级model.h5"
    image_path = r"D:\模型开发\1.jpg"
    
    print(f"\n📁 文件检查:")
    print(f"模型文件存在: {'✅' if os.path.exists(model_path) else '❌'}")
    print(f"图片文件存在: {'✅' if os.path.exists(image_path) else '❌'}")
    
    # 测试模块导入
    print(f"\n📦 模块测试:")
    
    modules = ['os', 'sys', 'warnings']
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")
    
    print("\n🎯 基础测试完成！")
    print("如果这个脚本能正常运行，说明Python环境基本正常。")
    
    input("\n按Enter键退出...")

if __name__ == "__main__":
    main()
