# 双模型联用系统依赖包
# AI痴呆症识别器 - 双模型联用版 v2.0

# 深度学习框架
tensorflow>=2.10.0
keras>=2.10.0

# GUI界面
customtkinter>=5.0.0
tkinter-tooltip>=2.0.0

# 图像处理
Pillow>=9.0.0
opencv-python>=4.6.0
numpy>=1.21.0

# 报告生成
fpdf2>=2.5.0
reportlab>=3.6.0
matplotlib>=3.5.0

# 数据处理
pandas>=1.4.0
json5>=0.9.0

# 系统工具
psutil>=5.9.0
pathlib>=1.0.0

# 网络和安全
requests>=2.28.0
cryptography>=3.4.0

# 日志和调试
loguru>=0.6.0

# 可选：GPU支持
# tensorflow-gpu>=2.10.0  # 如果有NVIDIA GPU

# 可选：更好的图像处理
# scikit-image>=0.19.0
# imageio>=2.19.0

# 开发工具（可选）
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
