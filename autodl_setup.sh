#!/bin/bash

echo "🚀 AutoDL EEG训练环境配置脚本"
echo "=================================="

# 更新系统
echo "📦 更新系统包..."
apt-get update -qq

# 安装必要的Python包
echo "🐍 安装Python依赖..."
pip install --upgrade pip
pip install mne
pip install seaborn
pip install scikit-learn
pip install matplotlib
pip install pandas
pip install numpy

# 检查GPU
echo "🔍 检查GPU状态..."
nvidia-smi

# 检查TensorFlow GPU支持
echo "🧠 检查TensorFlow GPU支持..."
python -c "
import tensorflow as tf
print('TensorFlow版本:', tf.__version__)
print('GPU设备:', tf.config.list_physical_devices('GPU'))
print('GPU可用:', tf.test.is_gpu_available())
"

# 检查CUDA
echo "⚡ 检查CUDA版本..."
nvcc --version

# 创建必要目录
echo "📁 创建工作目录..."
mkdir -p /root/eeg_models
mkdir -p /root/eeg_results

echo "✅ 环境配置完成!"
echo "📋 现在可以运行: python scientific_eeg_gpu_trainer.py"
