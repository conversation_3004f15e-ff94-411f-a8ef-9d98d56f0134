#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境测试脚本 - 检查IDE中的Python环境
"""

import sys
import os

print("🔍 IDE环境检测")
print("=" * 40)
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")
print(f"当前目录: {os.getcwd()}")

# 测试TensorFlow
try:
    import tensorflow as tf
    print(f"✅ TensorFlow: {tf.__version__}")
except ImportError as e:
    print(f"❌ TensorFlow未安装: {e}")

# 测试NumPy
try:
    import numpy as np
    print(f"✅ NumPy: {np.__version__}")
except ImportError:
    print("❌ NumPy未安装")

print("=" * 40)
print("测试完成！")
