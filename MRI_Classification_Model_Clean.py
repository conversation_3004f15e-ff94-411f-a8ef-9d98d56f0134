"""
🧠 MRI图像痴呆症分类模型
=======================

功能说明:
- 基于深度学习的MRI图像痴呆症诊断
- 支持4类痴呆症分类: 轻度、中度、无痴呆、非常轻度
- 包含完整的数据预处理、模型训练和评估流程
- 优化的CNN架构，适用于医学图像分析

作者: AI医学影像团队
版本: v2.0
更新日期: 2024-06-23
"""

# =============================================================================
# 1. 导入必要的库
# =============================================================================

import os
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

# 深度学习框架
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from tensorflow.keras.preprocessing.image import ImageDataGenerator, load_img, img_to_array
from tensorflow.keras.applications import VGG16, ResNet50
from tensorflow.keras.utils import to_categorical

# 机器学习工具
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.preprocessing import LabelEncoder

# 图像处理
from PIL import Image
import cv2

# 设置环境
warnings.filterwarnings('ignore')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 隐藏TensorFlow警告
tf.random.set_seed(42)
np.random.seed(42)

print("✅ 所有库导入成功")
print(f"📊 TensorFlow版本: {tf.__version__}")

# =============================================================================
# 2. 配置参数
# =============================================================================

class Config:
    """模型配置类"""
    
    # 数据路径
    DATA_PATH = "D:/模型开发/MRI_data"           # MRI数据集路径
    MODEL_SAVE_PATH = "D:/模型开发/"             # 模型保存路径
    
    # 图像参数
    IMG_HEIGHT = 150                             # 图像高度
    IMG_WIDTH = 150                              # 图像宽度
    IMG_CHANNELS = 3                             # 图像通道数
    
    # 训练参数
    BATCH_SIZE = 32                              # 批次大小
    EPOCHS = 50                                  # 训练轮数
    LEARNING_RATE = 0.001                        # 学习率
    VALIDATION_SPLIT = 0.2                       # 验证集比例
    
    # 类别标签
    CLASS_LABELS = [
        'MildDemented',                          # 轻度痴呆
        'ModerateDemented',                      # 中度痴呆  
        'NonDemented',                           # 无痴呆
        'VeryMildDemented'                       # 非常轻度痴呆
    ]
    
    # 中文标签映射
    CHINESE_LABELS = {
        'MildDemented': '轻度痴呆',
        'ModerateDemented': '中度痴呆',
        'NonDemented': '无痴呆',
        'VeryMildDemented': '非常轻度痴呆'
    }

# 创建配置实例
config = Config()

print(f"📁 数据路径: {config.DATA_PATH}")
print(f"💾 模型保存路径: {config.MODEL_SAVE_PATH}")
print(f"🖼️ 图像尺寸: {config.IMG_HEIGHT}x{config.IMG_WIDTH}x{config.IMG_CHANNELS}")

# =============================================================================
# 3. 数据加载和预处理类
# =============================================================================

class MRIDataProcessor:
    """MRI数据处理器"""
    
    def __init__(self, config):
        self.config = config
        self.label_encoder = LabelEncoder()
        
    def load_data_from_directory(self, data_path):
        """
        从目录结构加载数据
        
        预期目录结构:
        data_path/
        ├── MildDemented/
        ├── ModerateDemented/
        ├── NonDemented/
        └── VeryMildDemented/
        
        Returns:
            X: 图像数据数组
            y: 标签数组
            file_paths: 文件路径列表
        """
        print("📂 开始加载MRI数据...")
        
        X = []  # 图像数据
        y = []  # 标签
        file_paths = []  # 文件路径
        
        # 遍历每个类别目录
        for class_name in self.config.CLASS_LABELS:
            class_path = os.path.join(data_path, class_name)
            
            if not os.path.exists(class_path):
                print(f"⚠️ 警告: 目录不存在 {class_path}")
                continue
                
            # 获取该类别的所有图像文件
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
            
            print(f"📊 {class_name}: 找到 {len(image_files)} 张图像")
            
            # 加载每张图像
            for img_file in image_files:
                img_path = os.path.join(class_path, img_file)
                
                try:
                    # 加载和预处理图像
                    img = self.load_and_preprocess_image(img_path)
                    
                    X.append(img)
                    y.append(class_name)
                    file_paths.append(img_path)
                    
                except Exception as e:
                    print(f"❌ 加载图像失败 {img_path}: {e}")
                    continue
        
        # 转换为numpy数组
        X = np.array(X)
        y = np.array(y)
        
        print(f"✅ 数据加载完成:")
        print(f"   📊 总样本数: {len(X)}")
        print(f"   🖼️ 图像形状: {X.shape}")
        print(f"   📋 类别分布:")
        
        # 显示类别分布
        unique, counts = np.unique(y, return_counts=True)
        for label, count in zip(unique, counts):
            print(f"      {label}: {count} 张")
        
        return X, y, file_paths
    
    def load_and_preprocess_image(self, img_path):
        """
        加载和预处理单张图像
        
        Args:
            img_path: 图像文件路径
            
        Returns:
            preprocessed_img: 预处理后的图像数组
        """
        # 使用Keras加载图像
        img = load_img(img_path, 
                      target_size=(self.config.IMG_HEIGHT, self.config.IMG_WIDTH))
        
        # 转换为数组
        img_array = img_to_array(img)
        
        # 归一化到[0,1]
        img_array = img_array / 255.0
        
        return img_array
    
    def encode_labels(self, y, fit=True):
        """
        编码标签
        
        Args:
            y: 原始标签
            fit: 是否拟合编码器
            
        Returns:
            encoded_labels: 编码后的标签
            categorical_labels: one-hot编码标签
        """
        if fit:
            encoded_labels = self.label_encoder.fit_transform(y)
        else:
            encoded_labels = self.label_encoder.transform(y)
        
        # 转换为one-hot编码
        categorical_labels = to_categorical(encoded_labels, 
                                          num_classes=len(self.config.CLASS_LABELS))
        
        return encoded_labels, categorical_labels
    
    def create_data_generators(self, X_train, y_train, X_val, y_val):
        """
        创建数据增强生成器
        
        Args:
            X_train, y_train: 训练数据
            X_val, y_val: 验证数据
            
        Returns:
            train_generator, val_generator: 数据生成器
        """
        # 训练数据增强
        train_datagen = ImageDataGenerator(
            rotation_range=20,          # 随机旋转
            width_shift_range=0.1,      # 水平平移
            height_shift_range=0.1,     # 垂直平移
            horizontal_flip=True,       # 水平翻转
            zoom_range=0.1,             # 随机缩放
            fill_mode='nearest'         # 填充模式
        )
        
        # 验证数据不进行增强
        val_datagen = ImageDataGenerator()
        
        # 创建生成器
        train_generator = train_datagen.flow(
            X_train, y_train,
            batch_size=self.config.BATCH_SIZE,
            shuffle=True
        )
        
        val_generator = val_datagen.flow(
            X_val, y_val,
            batch_size=self.config.BATCH_SIZE,
            shuffle=False
        )
        
        return train_generator, val_generator

# =============================================================================
# 4. 模型构建类
# =============================================================================

class MRIClassificationModel:
    """MRI分类模型构建器"""

    def __init__(self, config):
        self.config = config
        self.model = None
        self.history = None

    def build_cnn_model(self):
        """
        构建自定义CNN模型

        Returns:
            model: 编译好的Keras模型
        """
        print("🏗️ 构建自定义CNN模型...")

        model = models.Sequential([
            # 第一个卷积块
            layers.Conv2D(32, (3, 3), activation='relu',
                         input_shape=(self.config.IMG_HEIGHT, self.config.IMG_WIDTH, self.config.IMG_CHANNELS)),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第二个卷积块
            layers.Conv2D(64, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第三个卷积块
            layers.Conv2D(128, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 第四个卷积块
            layers.Conv2D(256, (3, 3), activation='relu'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),

            # 全连接层
            layers.Flatten(),
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            # 输出层
            layers.Dense(len(self.config.CLASS_LABELS), activation='softmax')
        ])

        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=self.config.LEARNING_RATE),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        print("✅ CNN模型构建完成")
        print(f"📊 模型参数总数: {model.count_params():,}")

        return model

    def build_transfer_learning_model(self, base_model_name='VGG16'):
        """
        构建迁移学习模型

        Args:
            base_model_name: 基础模型名称 ('VGG16' 或 'ResNet50')

        Returns:
            model: 编译好的迁移学习模型
        """
        print(f"🔄 构建{base_model_name}迁移学习模型...")

        # 选择基础模型
        if base_model_name == 'VGG16':
            base_model = VGG16(
                weights='imagenet',
                include_top=False,
                input_shape=(self.config.IMG_HEIGHT, self.config.IMG_WIDTH, self.config.IMG_CHANNELS)
            )
        elif base_model_name == 'ResNet50':
            base_model = ResNet50(
                weights='imagenet',
                include_top=False,
                input_shape=(self.config.IMG_HEIGHT, self.config.IMG_WIDTH, self.config.IMG_CHANNELS)
            )
        else:
            raise ValueError(f"不支持的基础模型: {base_model_name}")

        # 冻结基础模型的权重
        base_model.trainable = False

        # 构建完整模型
        model = models.Sequential([
            base_model,
            layers.GlobalAveragePooling2D(),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),

            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),

            layers.Dense(len(self.config.CLASS_LABELS), activation='softmax')
        ])

        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=self.config.LEARNING_RATE),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        print(f"✅ {base_model_name}迁移学习模型构建完成")
        print(f"📊 模型参数总数: {model.count_params():,}")
        print(f"🔒 冻结参数数: {base_model.count_params():,}")

        return model

    def create_callbacks(self, model_save_path):
        """
        创建训练回调函数

        Args:
            model_save_path: 模型保存路径

        Returns:
            callbacks_list: 回调函数列表
        """
        callbacks_list = [
            # 早停
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=10,
                restore_best_weights=True,
                verbose=1
            ),

            # 学习率调度
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=5,
                min_lr=1e-7,
                verbose=1
            ),

            # 模型检查点
            callbacks.ModelCheckpoint(
                filepath=os.path.join(model_save_path, 'best_mri_model.h5'),
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            ),

            # CSV日志
            callbacks.CSVLogger(
                filename=os.path.join(model_save_path, 'training_log.csv'),
                append=True
            )
        ]

        return callbacks_list

    def train_model(self, model, train_generator, val_generator, callbacks_list):
        """
        训练模型

        Args:
            model: 要训练的模型
            train_generator: 训练数据生成器
            val_generator: 验证数据生成器
            callbacks_list: 回调函数列表

        Returns:
            history: 训练历史
        """
        print("🚀 开始训练模型...")
        print(f"📊 训练参数:")
        print(f"   批次大小: {self.config.BATCH_SIZE}")
        print(f"   训练轮数: {self.config.EPOCHS}")
        print(f"   学习率: {self.config.LEARNING_RATE}")

        # 计算每轮的步数
        steps_per_epoch = len(train_generator)
        validation_steps = len(val_generator)

        print(f"   每轮训练步数: {steps_per_epoch}")
        print(f"   每轮验证步数: {validation_steps}")

        # 开始训练
        history = model.fit(
            train_generator,
            steps_per_epoch=steps_per_epoch,
            epochs=self.config.EPOCHS,
            validation_data=val_generator,
            validation_steps=validation_steps,
            callbacks=callbacks_list,
            verbose=1
        )

        print("✅ 模型训练完成!")

        self.model = model
        self.history = history

        return history

# =============================================================================
# 5. 模型评估和可视化类
# =============================================================================

class ModelEvaluator:
    """模型评估器"""

    def __init__(self, config):
        self.config = config

    def evaluate_model(self, model, X_test, y_test, y_test_categorical):
        """
        评估模型性能

        Args:
            model: 训练好的模型
            X_test: 测试图像数据
            y_test: 测试标签
            y_test_categorical: 测试标签(one-hot编码)

        Returns:
            evaluation_results: 评估结果字典
        """
        print("📊 开始模型评估...")

        # 预测
        y_pred_proba = model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)
        y_true = np.argmax(y_test_categorical, axis=1)

        # 计算准确率
        accuracy = accuracy_score(y_true, y_pred)

        # 生成分类报告
        class_report = classification_report(
            y_true, y_pred,
            target_names=self.config.CLASS_LABELS,
            output_dict=True
        )

        # 生成混淆矩阵
        cm = confusion_matrix(y_true, y_pred)

        # 计算每个类别的准确率
        class_accuracies = {}
        for i, class_name in enumerate(self.config.CLASS_LABELS):
            class_acc = class_report[class_name]['precision']
            class_accuracies[class_name] = class_acc

        evaluation_results = {
            'overall_accuracy': accuracy,
            'classification_report': class_report,
            'confusion_matrix': cm,
            'class_accuracies': class_accuracies,
            'predictions': y_pred,
            'prediction_probabilities': y_pred_proba
        }

        # 打印结果
        print(f"✅ 模型评估完成:")
        print(f"   🎯 总体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        print(f"   📋 各类别准确率:")
        for class_name, acc in class_accuracies.items():
            chinese_name = self.config.CHINESE_LABELS[class_name]
            print(f"      {chinese_name}: {acc:.4f} ({acc*100:.2f}%)")

        return evaluation_results

    def plot_training_history(self, history, save_path=None):
        """
        绘制训练历史

        Args:
            history: 训练历史对象
            save_path: 图像保存路径
        """
        print("📈 绘制训练历史...")

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

        # 绘制准确率
        ax1.plot(history.history['accuracy'], label='训练准确率', linewidth=2)
        ax1.plot(history.history['val_accuracy'], label='验证准确率', linewidth=2)
        ax1.set_title('模型准确率', fontsize=14, fontweight='bold')
        ax1.set_xlabel('训练轮数', fontsize=12)
        ax1.set_ylabel('准确率', fontsize=12)
        ax1.legend(fontsize=11)
        ax1.grid(True, alpha=0.3)

        # 绘制损失
        ax2.plot(history.history['loss'], label='训练损失', linewidth=2)
        ax2.plot(history.history['val_loss'], label='验证损失', linewidth=2)
        ax2.set_title('模型损失', fontsize=14, fontweight='bold')
        ax2.set_xlabel('训练轮数', fontsize=12)
        ax2.set_ylabel('损失值', fontsize=12)
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(os.path.join(save_path, 'training_history.png'),
                       dpi=300, bbox_inches='tight')
            print(f"💾 训练历史图已保存: {save_path}/training_history.png")

        plt.show()

    def plot_confusion_matrix(self, cm, save_path=None):
        """
        绘制混淆矩阵

        Args:
            cm: 混淆矩阵
            save_path: 图像保存路径
        """
        print("📊 绘制混淆矩阵...")

        plt.figure(figsize=(10, 8))

        # 使用中文标签
        chinese_labels = [self.config.CHINESE_LABELS[label] for label in self.config.CLASS_LABELS]

        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=chinese_labels,
                   yticklabels=chinese_labels,
                   cbar_kws={'label': '样本数量'})

        plt.title('混淆矩阵', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('预测标签', fontsize=14)
        plt.ylabel('真实标签', fontsize=14)
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)

        # 添加准确率信息
        for i in range(len(self.config.CLASS_LABELS)):
            accuracy = cm[i, i] / np.sum(cm[i, :]) if np.sum(cm[i, :]) > 0 else 0
            plt.text(i + 0.5, i - 0.3, f'{accuracy:.2%}',
                    ha='center', va='center', fontweight='bold', color='red')

        plt.tight_layout()

        if save_path:
            plt.savefig(os.path.join(save_path, 'confusion_matrix.png'),
                       dpi=300, bbox_inches='tight')
            print(f"💾 混淆矩阵图已保存: {save_path}/confusion_matrix.png")

        plt.show()

    def plot_class_distribution(self, y, title="类别分布", save_path=None):
        """
        绘制类别分布图

        Args:
            y: 标签数组
            title: 图表标题
            save_path: 图像保存路径
        """
        print("📊 绘制类别分布...")

        # 统计各类别数量
        unique, counts = np.unique(y, return_counts=True)

        # 转换为中文标签
        chinese_labels = [self.config.CHINESE_LABELS[label] for label in unique]

        plt.figure(figsize=(10, 6))

        # 创建柱状图
        bars = plt.bar(chinese_labels, counts,
                      color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                      alpha=0.8, edgecolor='black', linewidth=1)

        # 添加数值标签
        for bar, count in zip(bars, counts):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    str(count), ha='center', va='bottom', fontweight='bold')

        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('痴呆症类别', fontsize=14)
        plt.ylabel('样本数量', fontsize=14)
        plt.xticks(rotation=45)
        plt.grid(axis='y', alpha=0.3)

        # 添加总数信息
        total = sum(counts)
        plt.text(0.02, 0.98, f'总样本数: {total}',
                transform=plt.gca().transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat'))

        plt.tight_layout()

        if save_path:
            plt.savefig(os.path.join(save_path, 'class_distribution.png'),
                       dpi=300, bbox_inches='tight')
            print(f"💾 类别分布图已保存: {save_path}/class_distribution.png")

        plt.show()

# =============================================================================
# 6. 预测和部署类
# =============================================================================

class MRIPredictor:
    """MRI图像预测器"""

    def __init__(self, model_path, config):
        self.config = config
        self.model = None
        self.load_model(model_path)

    def load_model(self, model_path):
        """
        加载训练好的模型

        Args:
            model_path: 模型文件路径
        """
        try:
            self.model = tf.keras.models.load_model(model_path)
            print(f"✅ 模型加载成功: {model_path}")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.model = None

    def predict_single_image(self, image_path):
        """
        预测单张图像

        Args:
            image_path: 图像文件路径

        Returns:
            prediction_result: 预测结果字典
        """
        if self.model is None:
            return {"error": "模型未加载"}

        try:
            # 加载和预处理图像
            img = load_img(image_path,
                          target_size=(self.config.IMG_HEIGHT, self.config.IMG_WIDTH))
            img_array = img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0

            # 进行预测
            predictions = self.model.predict(img_array, verbose=0)
            predicted_class_idx = np.argmax(predictions, axis=1)[0]
            predicted_class_name = self.config.CLASS_LABELS[predicted_class_idx]
            confidence = float(np.max(predictions))

            # 构建结果
            prediction_result = {
                "success": True,
                "predicted_class": predicted_class_name,
                "predicted_class_chinese": self.config.CHINESE_LABELS[predicted_class_name],
                "confidence": confidence,
                "confidence_percentage": f"{confidence*100:.2f}%",
                "all_probabilities": {
                    self.config.CLASS_LABELS[i]: float(predictions[0][i])
                    for i in range(len(self.config.CLASS_LABELS))
                },
                "all_probabilities_chinese": {
                    self.config.CHINESE_LABELS[self.config.CLASS_LABELS[i]]: float(predictions[0][i])
                    for i in range(len(self.config.CLASS_LABELS))
                }
            }

            return prediction_result

        except Exception as e:
            return {"error": f"预测失败: {str(e)}"}

    def predict_batch_images(self, image_paths):
        """
        批量预测图像

        Args:
            image_paths: 图像文件路径列表

        Returns:
            batch_results: 批量预测结果列表
        """
        batch_results = []

        for img_path in image_paths:
            result = self.predict_single_image(img_path)
            result["image_path"] = img_path
            batch_results.append(result)

        return batch_results

# =============================================================================
# 7. 主训练流程
# =============================================================================

def main_training_pipeline():
    """
    主训练流程
    """
    print("🧠 MRI痴呆症分类模型训练流程")
    print("=" * 60)

    # 1. 初始化组件
    print("\n📋 步骤1: 初始化组件")
    data_processor = MRIDataProcessor(config)
    model_builder = MRIClassificationModel(config)
    evaluator = ModelEvaluator(config)

    # 2. 加载数据
    print("\n📋 步骤2: 加载数据")
    if not os.path.exists(config.DATA_PATH):
        print(f"❌ 数据路径不存在: {config.DATA_PATH}")
        print("💡 请确保数据路径正确，并包含以下子目录:")
        for label in config.CLASS_LABELS:
            print(f"   - {label}/")
        return

    X, y, file_paths = data_processor.load_data_from_directory(config.DATA_PATH)

    if len(X) == 0:
        print("❌ 没有找到任何图像数据")
        return

    # 3. 数据预处理
    print("\n📋 步骤3: 数据预处理")

    # 编码标签
    y_encoded, y_categorical = data_processor.encode_labels(y, fit=True)

    # 划分数据集
    X_train, X_test, y_train_cat, y_test_cat = train_test_split(
        X, y_categorical,
        test_size=0.2,
        random_state=42,
        stratify=y_encoded
    )

    X_train, X_val, y_train_cat, y_val_cat = train_test_split(
        X_train, y_train_cat,
        test_size=0.2,
        random_state=42,
        stratify=np.argmax(y_train_cat, axis=1)
    )

    print(f"📊 数据集划分:")
    print(f"   训练集: {len(X_train)} 张")
    print(f"   验证集: {len(X_val)} 张")
    print(f"   测试集: {len(X_test)} 张")

    # 创建数据生成器
    train_generator, val_generator = data_processor.create_data_generators(
        X_train, y_train_cat, X_val, y_val_cat
    )

    # 4. 构建模型
    print("\n📋 步骤4: 构建模型")
    print("选择模型类型:")
    print("1. 自定义CNN模型")
    print("2. VGG16迁移学习")
    print("3. ResNet50迁移学习")

    # 这里可以根据需要选择模型类型
    model_choice = 1  # 默认使用自定义CNN

    if model_choice == 1:
        model = model_builder.build_cnn_model()
    elif model_choice == 2:
        model = model_builder.build_transfer_learning_model('VGG16')
    else:
        model = model_builder.build_transfer_learning_model('ResNet50')

    # 5. 训练模型
    print("\n📋 步骤5: 训练模型")

    # 创建回调函数
    callbacks_list = model_builder.create_callbacks(config.MODEL_SAVE_PATH)

    # 开始训练
    history = model_builder.train_model(
        model, train_generator, val_generator, callbacks_list
    )

    # 6. 评估模型
    print("\n📋 步骤6: 评估模型")

    # 获取测试集标签
    y_test_original = data_processor.label_encoder.inverse_transform(
        np.argmax(y_test_cat, axis=1)
    )

    evaluation_results = evaluator.evaluate_model(
        model, X_test, y_test_original, y_test_cat
    )

    # 7. 可视化结果
    print("\n📋 步骤7: 生成可视化结果")

    # 绘制训练历史
    evaluator.plot_training_history(history, config.MODEL_SAVE_PATH)

    # 绘制混淆矩阵
    evaluator.plot_confusion_matrix(
        evaluation_results['confusion_matrix'],
        config.MODEL_SAVE_PATH
    )

    # 绘制类别分布
    evaluator.plot_class_distribution(y, "训练数据类别分布", config.MODEL_SAVE_PATH)

    # 8. 保存最终模型
    print("\n📋 步骤8: 保存最终模型")
    final_model_path = os.path.join(config.MODEL_SAVE_PATH, "MRI_class.h5")
    model.save(final_model_path)
    print(f"💾 最终模型已保存: {final_model_path}")

    # 9. 测试预测功能
    print("\n📋 步骤9: 测试预测功能")
    predictor = MRIPredictor(final_model_path, config)

    if len(file_paths) > 0:
        # 测试第一张图像
        test_image = file_paths[0]
        result = predictor.predict_single_image(test_image)

        if "error" not in result:
            print(f"🧪 测试预测结果:")
            print(f"   图像: {os.path.basename(test_image)}")
            print(f"   预测: {result['predicted_class_chinese']}")
            print(f"   置信度: {result['confidence_percentage']}")

    print("\n🎉 训练流程完成!")
    print("=" * 60)

    return model, evaluation_results

# =============================================================================
# 8. 程序入口
# =============================================================================

if __name__ == "__main__":
    # 运行主训练流程
    model, results = main_training_pipeline()

    print("\n📊 最终结果总结:")
    if results:
        print(f"🎯 模型准确率: {results['overall_accuracy']:.4f}")
        print(f"📋 各类别表现:")
        for class_name, acc in results['class_accuracies'].items():
            chinese_name = config.CHINESE_LABELS[class_name]
            print(f"   {chinese_name}: {acc:.4f}")

    print("\n💡 使用建议:")
    print("1. 如果准确率较低，可以尝试:")
    print("   - 增加训练数据")
    print("   - 调整模型架构")
    print("   - 使用迁移学习")
    print("   - 调整超参数")
    print("2. 模型文件保存在: D:/模型开发/MRI_class.h5")
    print("3. 可以使用MRIPredictor类进行预测")
