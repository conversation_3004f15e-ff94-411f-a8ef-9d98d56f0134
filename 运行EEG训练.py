"""
🚀 EEG模型训练启动脚本
简化版本，适合在AutoDL环境中运行
"""

import os
import sys

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查是否在AutoDL环境
    if os.path.exists("/root/autodl-tmp"):
        print("✅ 检测到AutoDL环境")
        return True
    else:
        print("⚠️ 未检测到AutoDL环境，请确认运行环境")
        return True  # 仍然允许运行

def install_dependencies():
    """安装必要的依赖"""
    print("📦 检查和安装依赖...")
    
    dependencies = [
        "mne",
        "scikit-learn", 
        "matplotlib",
        "seaborn",
        "tensorflow"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 已安装")
        except ImportError:
            print(f"🔄 安装 {dep}...")
            os.system(f"pip install {dep}")

def main():
    """主启动函数"""
    print("🧠 EEG痴呆症检测模型训练系统")
    print("=" * 50)
    
    # 检查环境
    if not check_environment():
        return
    
    # 安装依赖
    install_dependencies()
    
    # 导入主训练模块
    try:
        from EEG_数据集处理和模型训练 import main as train_main
        
        print("\n🚀 启动训练流程...")
        train_main()
        
    except ImportError as e:
        print(f"❌ 导入训练模块失败: {e}")
        print("请确保 EEG_数据集处理和模型训练.py 文件在同一目录下")
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
