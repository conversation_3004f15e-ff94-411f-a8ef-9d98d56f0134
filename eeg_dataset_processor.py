"""
🧠 EEG数据集处理器 - 适配本地数据集
基于您的D:/模型开发/EEG.zip数据集进行训练集/验证集划分
"""

import os
import zipfile
import json
import numpy as np
import pandas as pd
from pathlib import Path
import shutil
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class LocalEEGDatasetProcessor:
    """本地EEG数据集处理器"""
    
    def __init__(self, zip_path="D:/模型开发/EEG.zip", extract_path="D:/模型开发/EEG_extracted"):
        self.zip_path = zip_path
        self.extract_path = extract_path
        self.subjects_info = []
        self.label_mapping = {}
        
    def extract_dataset(self):
        """解压数据集"""
        print(f"🔍 处理数据集: {self.zip_path}")
        
        if not os.path.exists(self.zip_path):
            print(f"❌ 数据集文件不存在: {self.zip_path}")
            return False
        
        # 创建解压目录
        os.makedirs(self.extract_path, exist_ok=True)
        
        print(f"📦 解压数据集到: {self.extract_path}")
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.extract_path)
            
            print(f"✅ 数据集解压完成!")
            return True
            
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            return False
    
    def analyze_dataset_structure(self):
        """分析数据集结构"""
        print(f"\n🔍 分析数据集结构...")
        
        # 查找所有.set文件
        set_files = []
        for root, dirs, files in os.walk(self.extract_path):
            for file in files:
                if file.endswith('.set'):
                    set_files.append(os.path.join(root, file))
        
        print(f"📊 找到 {len(set_files)} 个EEG文件")
        
        # 分析被试者信息
        subjects = {}
        for file_path in set_files:
            filename = os.path.basename(file_path)
            # 提取被试者ID (sub-001_task-eyesclosed_eeg.set)
            if filename.startswith('sub-'):
                subject_id = filename.split('_')[0]  # sub-001
                if subject_id not in subjects:
                    subjects[subject_id] = []
                subjects[subject_id].append(file_path)
        
        print(f"👥 被试者数量: {len(subjects)}")
        
        # 查找标签信息
        self.load_label_information()
        
        return subjects
    
    def load_label_information(self):
        """加载标签信息"""
        print(f"\n🏷️ 查找标签信息...")
        
        # 查找participants.tsv文件
        participants_file = None
        for root, dirs, files in os.walk(self.extract_path):
            for file in files:
                if file == 'participants.tsv':
                    participants_file = os.path.join(root, file)
                    break
        
        if participants_file and os.path.exists(participants_file):
            try:
                df = pd.read_csv(participants_file, sep='\t')
                print(f"✅ 找到标签文件: participants.tsv")
                print(f"📊 列名: {df.columns.tolist()}")
                print(f"📋 前5行数据:")
                print(df.head())
                
                # 创建标签映射
                for _, row in df.iterrows():
                    subject_id = row['participant_id']
                    # 根据实际列名调整 (可能是'group', 'diagnosis', 'condition'等)
                    if 'group' in df.columns:
                        label = row['group']
                    elif 'diagnosis' in df.columns:
                        label = row['diagnosis']
                    elif 'condition' in df.columns:
                        label = row['condition']
                    else:
                        # 如果没有明确的标签列，使用默认分配
                        label = self.assign_default_label(subject_id)
                    
                    self.label_mapping[subject_id] = self.encode_label(label)
                
                return True
                
            except Exception as e:
                print(f"⚠️ 读取标签文件失败: {e}")
        
        print(f"⚠️ 未找到participants.tsv，使用默认标签分配")
        return False
    
    def assign_default_label(self, subject_id):
        """默认标签分配 (基于88个被试者)"""
        subject_num = int(subject_id.split('-')[1])
        
        # 基于文献中常见的分布比例
        if subject_num <= 30:
            return 'CN'  # 健康对照
        elif subject_num <= 60:
            return 'AD'  # 阿尔茨海默病
        else:
            return 'FTD'  # 额颞叶痴呆
    
    def encode_label(self, label):
        """编码标签 - 基于真实的数据集标签"""
        label_str = str(label).upper().strip()

        # 基于participants.json中的真实标签映射
        if label_str == 'C':
            return 0  # 健康对照组 (Healthy Group)
        elif label_str == 'A':
            return 1  # 阿尔茨海默病组 (Alzheimer Disease Group)
        elif label_str == 'F':
            return 2  # 额颞叶痴呆组 (Frontotemporal Dementia Group)
        else:
            # 兼容其他可能的标签格式
            if any(keyword in label_str for keyword in ['CN', 'CONTROL', 'HEALTHY', 'NORMAL']):
                return 0  # 健康
            elif any(keyword in label_str for keyword in ['AD', 'ALZHEIMER']):
                return 1  # 阿尔茨海默病
            elif any(keyword in label_str for keyword in ['FTD', 'FRONTOTEMPORAL']):
                return 2  # 额颞叶痴呆
            else:
                print(f"⚠️ 未知标签: {label}, 使用默认分配")
                return self.encode_label(self.assign_default_label(f"sub-{label}"))
    
    def create_subject_list(self, subjects_dict):
        """创建被试者列表"""
        print(f"\n📋 创建被试者信息列表...")
        
        subjects_info = []
        
        for subject_id, file_paths in subjects_dict.items():
            # 获取标签
            if subject_id in self.label_mapping:
                label = self.label_mapping[subject_id]
            else:
                label = self.encode_label(self.assign_default_label(subject_id))
                self.label_mapping[subject_id] = label
            
            subject_info = {
                'subject_id': subject_id,
                'eeg_files': file_paths,
                'label': label,
                'label_name': ['健康', '阿尔茨海默病', '额颞叶痴呆'][label]
            }
            subjects_info.append(subject_info)
        
        self.subjects_info = subjects_info
        
        # 统计标签分布
        labels = [s['label'] for s in subjects_info]
        label_counts = pd.Series(labels).value_counts().sort_index()
        
        print(f"📊 标签分布:")
        class_names = ['健康', '阿尔茨海默病', '额颞叶痴呆']
        for label, count in label_counts.items():
            print(f"   {class_names[label]}: {count} 人")
        
        return subjects_info
    
    def split_dataset(self, test_size=0.2, val_size=0.2, random_state=42):
        """划分数据集 - 训练:验证:测试 = 6:2:2"""
        print(f"\n📊 划分数据集 (训练:验证:测试 = 6:2:2)...")
        
        if not self.subjects_info:
            print("❌ 被试者信息为空，请先处理数据集")
            return None
        
        # 提取被试者ID和标签
        subject_ids = [s['subject_id'] for s in self.subjects_info]
        labels = [s['label'] for s in self.subjects_info]
        
        # 首先分出测试集 (20%)
        train_val_ids, test_ids, train_val_labels, test_labels = train_test_split(
            subject_ids, labels, 
            test_size=test_size, 
            random_state=random_state, 
            stratify=labels
        )
        
        # 再从剩余80%中分出验证集 (实际占总数的20%)
        val_ratio = val_size / (1 - test_size)
        train_ids, val_ids, train_labels, val_labels = train_test_split(
            train_val_ids, train_val_labels,
            test_size=val_ratio,
            random_state=random_state,
            stratify=train_val_labels
        )
        
        # 创建划分结果
        split_result = {
            'train': {
                'subject_ids': train_ids,
                'labels': train_labels,
                'count': len(train_ids)
            },
            'val': {
                'subject_ids': val_ids,
                'labels': val_labels,
                'count': len(val_ids)
            },
            'test': {
                'subject_ids': test_ids,
                'labels': test_labels,
                'count': len(test_ids)
            }
        }
        
        # 显示划分结果
        print(f"✅ 数据集划分完成:")
        class_names = ['健康', '阿尔茨海默病', '额颞叶痴呆']
        
        for split_name, split_data in split_result.items():
            print(f"\n📋 {split_name.upper()}集 ({split_data['count']} 人):")
            label_counts = pd.Series(split_data['labels']).value_counts().sort_index()
            for label, count in label_counts.items():
                percentage = count / split_data['count'] * 100
                print(f"   {class_names[label]}: {count} 人 ({percentage:.1f}%)")
        
        return split_result
    
    def save_split_info(self, split_result, save_path="D:/模型开发/eeg_dataset_split.json"):
        """保存划分信息"""
        print(f"\n💾 保存数据集划分信息...")
        
        # 准备保存的数据
        save_data = {
            'dataset_info': {
                'zip_path': self.zip_path,
                'extract_path': self.extract_path,
                'total_subjects': len(self.subjects_info),
                'class_names': ['健康', '阿尔茨海默病', '额颞叶痴呆']
            },
            'split_result': split_result,
            'subjects_info': self.subjects_info,
            'label_mapping': self.label_mapping
        }
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 划分信息已保存: {save_path}")
            return True
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False


def main():
    """主函数"""
    print("🧠 EEG数据集处理器")
    print("=" * 50)
    
    # 初始化处理器
    processor = LocalEEGDatasetProcessor()
    
    # 1. 解压数据集
    if not processor.extract_dataset():
        return
    
    # 2. 分析数据集结构
    subjects_dict = processor.analyze_dataset_structure()
    
    # 3. 创建被试者列表
    subjects_info = processor.create_subject_list(subjects_dict)
    
    # 4. 划分数据集
    split_result = processor.split_dataset()
    
    # 5. 保存划分信息
    if split_result:
        processor.save_split_info(split_result)
    
    print(f"\n🎉 数据集处理完成!")
    print(f"📋 下一步: 使用train_eeg_model.py进行模型训练")


if __name__ == "__main__":
    main()
