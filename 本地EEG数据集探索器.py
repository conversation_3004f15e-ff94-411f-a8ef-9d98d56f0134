"""
🔍 本地EEG数据集探索器
请在本地运行此脚本，然后将输出结果复制给AI助手
"""

import os
import zipfile
import json
from pathlib import Path

def explore_eeg_dataset(zip_path):
    """探索EEG数据集结构"""
    
    print("🔍 EEG数据集结构分析报告")
    print("=" * 60)
    print(f"📁 数据集路径: {zip_path}")
    
    # 检查文件是否存在
    if not os.path.exists(zip_path):
        print(f"❌ 错误: 文件不存在 - {zip_path}")
        print("\n请检查路径是否正确，然后重新运行脚本")
        return
    
    # 获取文件大小
    file_size_mb = os.path.getsize(zip_path) / (1024 * 1024)
    print(f"📊 文件大小: {file_size_mb:.2f} MB")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            file_list = zip_ref.namelist()
            
            print(f"📦 压缩包信息:")
            print(f"   总文件/文件夹数: {len(file_list)}")
            
            # 分析文件类型
            file_extensions = {}
            folders = []
            files = []
            
            for item in file_list:
                if item.endswith('/'):
                    folders.append(item)
                else:
                    files.append(item)
                    ext = Path(item).suffix.lower()
                    if ext:
                        file_extensions[ext] = file_extensions.get(ext, 0) + 1
                    else:
                        file_extensions['无扩展名'] = file_extensions.get('无扩展名', 0) + 1
            
            print(f"   文件夹数: {len(folders)}")
            print(f"   文件数: {len(files)}")
            
            # 显示文件类型统计
            print(f"\n📋 文件类型统计:")
            for ext, count in sorted(file_extensions.items()):
                print(f"   {ext}: {count} 个")
            
            # 显示目录结构（前30个条目）
            print(f"\n📁 目录结构 (前30个条目):")
            for i, item in enumerate(file_list[:30]):
                level = item.count('/')
                indent = "  " * level
                name = os.path.basename(item.rstrip('/'))
                
                if item.endswith('/'):
                    print(f"{indent}📁 {name}/")
                else:
                    # 获取文件大小
                    try:
                        info = zip_ref.getinfo(item)
                        size = info.file_size
                        if size > 1024*1024:
                            size_str = f" ({size/(1024*1024):.1f}MB)"
                        elif size > 1024:
                            size_str = f" ({size/1024:.1f}KB)"
                        else:
                            size_str = f" ({size}B)"
                    except:
                        size_str = ""
                    
                    print(f"{indent}📄 {name}{size_str}")
            
            if len(file_list) > 30:
                print(f"   ... 还有 {len(file_list) - 30} 个条目")
            
            # 查找EEG相关文件
            print(f"\n🧠 EEG文件分析:")
            eeg_extensions = ['.set', '.fdt', '.edf', '.bdf', '.fif', '.mat', '.csv']
            eeg_files = {}
            
            for ext in eeg_extensions:
                matching_files = [f for f in files if f.lower().endswith(ext)]
                if matching_files:
                    eeg_files[ext] = matching_files
                    print(f"   {ext}文件: {len(matching_files)} 个")
                    # 显示前3个文件名作为示例
                    for example in matching_files[:3]:
                        print(f"      示例: {os.path.basename(example)}")
                    if len(matching_files) > 3:
                        print(f"      ... 还有 {len(matching_files) - 3} 个")
            
            # 查找被试者信息
            print(f"\n👥 被试者信息:")
            subjects = set()
            for item in file_list:
                # 查找sub-开头的模式
                if 'sub-' in item:
                    parts = item.split('/')
                    for part in parts:
                        if part.startswith('sub-'):
                            subjects.add(part)
                            break
            
            if subjects:
                subjects_list = sorted(list(subjects))
                print(f"   被试者数量: {len(subjects_list)}")
                print(f"   被试者范围: {subjects_list[0]} 到 {subjects_list[-1]}")
                print(f"   前10个被试者: {subjects_list[:10]}")
                if len(subjects_list) > 10:
                    print(f"   ... 还有 {len(subjects_list) - 10} 个")
            else:
                print(f"   未发现标准的sub-xxx被试者命名模式")
                # 尝试其他命名模式
                potential_subjects = set()
                for item in files:
                    basename = os.path.basename(item)
                    # 查找可能的被试者编号
                    if any(keyword in basename.lower() for keyword in ['subject', 'patient', 'participant']):
                        potential_subjects.add(basename)
                
                if potential_subjects:
                    print(f"   发现可能的被试者文件: {len(potential_subjects)} 个")
                    for example in list(potential_subjects)[:5]:
                        print(f"      示例: {example}")
            
            # 查找标签信息
            print(f"\n🏷️ 标签信息分析:")
            json_files = [f for f in files if f.endswith('.json')]
            csv_files = [f for f in files if f.endswith('.csv')]
            txt_files = [f for f in files if f.endswith('.txt')]
            
            print(f"   JSON文件: {len(json_files)} 个")
            print(f"   CSV文件: {len(csv_files)} 个")
            print(f"   TXT文件: {len(txt_files)} 个")
            
            # 分析文件名中的标签信息
            label_keywords = ['ad', 'alzheimer', 'ftd', 'frontotemporal', 'cn', 'control', 'healthy', 'normal']
            label_analysis = {}
            
            for keyword in label_keywords:
                matching_files = [f for f in files if keyword in os.path.basename(f).lower()]
                if matching_files:
                    label_analysis[keyword] = len(matching_files)
            
            if label_analysis:
                print(f"   文件名中的标签关键词:")
                for keyword, count in label_analysis.items():
                    print(f"      '{keyword}': {count} 个文件")
            
            # 尝试读取第一个JSON文件的内容
            if json_files:
                print(f"\n📋 JSON文件内容示例:")
                try:
                    with zip_ref.open(json_files[0]) as f:
                        json_content = json.load(f)
                        print(f"   文件: {json_files[0]}")
                        print(f"   键值对:")
                        for key, value in list(json_content.items())[:10]:
                            print(f"      {key}: {value}")
                        if len(json_content) > 10:
                            print(f"      ... 还有 {len(json_content) - 10} 个键值对")
                except Exception as e:
                    print(f"   JSON读取失败: {e}")
            
            print(f"\n" + "=" * 60)
            print(f"✅ 分析完成!")
            print(f"📋 请将以上完整输出复制给AI助手进行代码调整")
            print(f"=" * 60)
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        print(f"请检查文件是否为有效的ZIP格式")

def main():
    """主函数"""
    print("请输入EEG数据集的完整路径:")
    print("示例: C:\\Users\\<USER>\\Downloads\\open-nuro-dataset.zip")
    print("或者: /path/to/your/dataset.zip")
    print()
    
    # 获取用户输入的路径
       # 直接指定 EEG 数据集的完整路径
    zip_path = "D:/模型开发/EEG.zip"

    
    if not zip_path:
        print("❌ 路径不能为空")
        return
    
    # 开始分析
    explore_eeg_dataset(zip_path)
    
    print(f"\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
