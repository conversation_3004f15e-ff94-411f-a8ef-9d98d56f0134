# 🧠 EEG深度学习模型使用指南

## 📦 文件说明

训练完成后，您会得到以下文件：
- `final_complete_eeg_model.h5` - 完整训练模型（主要文件）
- `eeg_label_encoder.pkl` - 标签编码器
- `eeg_model_loader.py` - 模型加载器（使用库）

## 🚀 快速开始

### 1️⃣ 基本使用

```python
from eeg_model_loader import EEGModelLoader
import numpy as np

# 创建加载器
loader = EEGModelLoader()

# 加载模型
loader.load_model()

# 预测EEG数据
eeg_data = np.random.randn(19, 128)  # 示例数据
result = loader.predict(eeg_data)
print(f"预测结果: {result}")
```

### 2️⃣ 从文件预测

```python
# 直接从.set文件预测
result = loader.predict_from_file("patient_001.set")
print(f"预测结果: {result}")
```

### 3️⃣ 获取详细概率

```python
# 获取详细预测信息
result = loader.predict(eeg_data, return_probabilities=True)
print(f"预测标签: {result['labels']}")
print(f"预测概率: {result['probabilities']}")
```

### 4️⃣ 快速预测函数

```python
from eeg_model_loader import quick_predict

# 最简单的使用方式
result = quick_predict(eeg_data)
print(f"预测结果: {result}")

# 或从文件
result = quick_predict("patient_001.set")
print(f"预测结果: {result}")
```

## 📊 输入数据格式

### 支持的输入格式：

1. **单个样本**: `(19, 128)` - 19个通道，128个时间点
2. **多个样本**: `(n_samples, 19, 128)` - 多个EEG片段
3. **文件路径**: `"path/to/file.set"` - 直接从.set文件读取

### 数据要求：
- **通道数**: 最多19个通道（不足会补零，超出会截取）
- **时间点**: 最多128个采样点（不足会补零，超出会截取）
- **数据类型**: numpy数组或.set文件

## 🎯 预测结果

### 标签类型：
根据您的训练数据，可能的预测结果：
- `A` - 阿尔茨海默病 (Alzheimer's Disease)
- `C` - 健康对照 (Control/Healthy)
- `F` - 额颞叶痴呆 (Frontotemporal Dementia)

### 输出格式：
- **简单模式**: 返回预测标签数组
- **详细模式**: 返回字典包含标签、概率、类别索引

## 🔧 高级使用

### 批量预测

```python
# 批量处理多个文件
import os

loader = EEGModelLoader()
loader.load_model()

results = {}
for filename in os.listdir("eeg_files/"):
    if filename.endswith('.set'):
        filepath = os.path.join("eeg_files/", filename)
        result = loader.predict_from_file(filepath)
        results[filename] = result

print(results)
```

### 自定义模型路径

```python
# 使用自定义模型路径
loader = EEGModelLoader(
    model_path="path/to/your/model.h5",
    encoder_path="path/to/your/encoder.pkl"
)
```

### 获取模型信息

```python
loader = EEGModelLoader()
loader.load_model()

info = loader.get_model_info()
print(f"模型信息: {info}")
```

## 📱 集成到您的应用

### 在GUI中使用

```python
import tkinter as tk
from eeg_model_loader import EEGModelLoader

class EEGApp:
    def __init__(self):
        self.loader = EEGModelLoader()
        self.loader.load_model()
        
    def predict_file(self, file_path):
        result = self.loader.predict_from_file(file_path)
        return result
```

### 在Web服务中使用

```python
from flask import Flask, request, jsonify
from eeg_model_loader import EEGModelLoader

app = Flask(__name__)
loader = EEGModelLoader()
loader.load_model()

@app.route('/predict', methods=['POST'])
def predict():
    # 接收EEG数据
    eeg_data = request.json['data']
    result = loader.predict(eeg_data)
    return jsonify({'prediction': result})
```

## ⚠️ 注意事项

1. **模型文件**: 确保`final_complete_eeg_model.h5`和`eeg_label_encoder.pkl`在同一目录
2. **依赖库**: 需要安装`tensorflow`, `mne`, `joblib`, `numpy`
3. **内存使用**: 模型约100MB，预测时需要额外内存
4. **GPU支持**: 自动检测GPU，如有GPU会自动使用

## 🛠️ 故障排除

### 常见问题：

1. **模型加载失败**
   ```python
   # 检查文件是否存在
   import os
   print(os.path.exists('final_complete_eeg_model.h5'))
   print(os.path.exists('eeg_label_encoder.pkl'))
   ```

2. **预测失败**
   ```python
   # 检查数据形状
   print(f"数据形状: {eeg_data.shape}")
   print(f"数据类型: {type(eeg_data)}")
   ```

3. **内存不足**
   ```python
   # 分批处理大量数据
   batch_size = 10
   for i in range(0, len(data), batch_size):
       batch = data[i:i+batch_size]
       result = loader.predict(batch)
   ```

## 📞 技术支持

如果遇到问题，请检查：
1. 所有依赖库是否正确安装
2. 模型文件是否完整
3. 输入数据格式是否正确
4. 系统内存是否充足

---

**🎉 现在您可以轻松使用训练好的EEG深度学习模型了！**
