"""
Complete Speech Dataset Downloader
Download multiple speech datasets for dementia analysis
"""

import tensorflow_datasets as tfds
import os
import pandas as pd
import numpy as np
from pathlib import Path
import requests
import zipfile

def download_speech_commands():
    """Download Google Speech Commands dataset"""
    
    print("Downloading Google Speech Commands dataset...")
    
    try:
        dataset, info = tfds.load(
            'speech_commands',
            with_info=True,
            download=True,
            data_dir=r"D:\模型开发\speech_commands",
            as_supervised=True
        )
        
        print("Speech Commands downloaded successfully!")
        print(f"Splits: {list(dataset.keys())}")
        print(f"Total samples: {info.splits.total_num_examples}")
        
        return dataset, info
        
    except Exception as e:
        print(f"Speech Commands download failed: {e}")
        return None, None

def download_librispeech():
    """Download LibriSpeech dataset"""
    
    print("Downloading LibriSpeech dataset...")
    
    try:
        dataset, info = tfds.load(
            'librispeech',
            with_info=True,
            download=True,
            data_dir=r"D:\模型开发\librispeech",
            split='train_clean_100[:100]'  # Small subset
        )
        
        print("LibriSpeech downloaded successfully!")
        return dataset, info
        
    except Exception as e:
        print(f"LibriSpeech download failed: {e}")
        return None, None

def create_comprehensive_speech_dataset():
    """Create comprehensive speech analysis dataset"""
    
    print("Creating comprehensive speech dataset...")
    
    np.random.seed(42)
    n_samples = 5000
    
    # Generate realistic speech features
    data = {}
    
    # Basic demographics
    data['age'] = np.random.normal(70, 15, n_samples).clip(50, 95)
    data['gender'] = np.random.choice([0, 1], n_samples)  # 0=Female, 1=Male
    data['education_years'] = np.random.normal(12, 4, n_samples).clip(6, 20)
    
    # Speech timing features
    data['speech_rate'] = np.random.normal(4.5, 1.2, n_samples).clip(1, 8)
    data['pause_frequency'] = np.random.normal(0.3, 0.1, n_samples).clip(0.1, 0.8)
    data['pause_duration_mean'] = np.random.normal(0.8, 0.3, n_samples).clip(0.2, 2.0)
    data['pause_duration_std'] = np.random.normal(0.4, 0.2, n_samples).clip(0.1, 1.0)
    
    # Acoustic features
    data['fundamental_freq_mean'] = np.random.normal(150, 50, n_samples).clip(80, 300)
    data['fundamental_freq_std'] = np.random.normal(30, 15, n_samples).clip(10, 80)
    data['jitter'] = np.random.normal(0.02, 0.01, n_samples).clip(0.005, 0.1)
    data['shimmer'] = np.random.normal(0.05, 0.02, n_samples).clip(0.01, 0.2)
    
    # Voice quality features
    data['hnr'] = np.random.normal(15, 5, n_samples).clip(5, 30)  # Harmonics-to-noise ratio
    data['spectral_centroid'] = np.random.normal(2000, 500, n_samples).clip(1000, 4000)
    data['spectral_rolloff'] = np.random.normal(3000, 800, n_samples).clip(1500, 6000)
    data['zero_crossing_rate'] = np.random.normal(0.1, 0.03, n_samples).clip(0.05, 0.2)
    
    # MFCC features (13 coefficients)
    for i in range(13):
        data[f'mfcc_{i+1}'] = np.random.normal(0, 1, n_samples)
    
    # Linguistic features
    data['word_count'] = np.random.poisson(50, n_samples).clip(10, 150)
    data['unique_words'] = np.random.poisson(35, n_samples).clip(8, 100)
    data['avg_word_length'] = np.random.normal(4.5, 0.8, n_samples).clip(3, 7)
    data['sentence_length_mean'] = np.random.normal(8, 3, n_samples).clip(3, 20)
    
    # Fluency features
    data['filled_pauses'] = np.random.poisson(3, n_samples)
    data['repetitions'] = np.random.poisson(2, n_samples)
    data['false_starts'] = np.random.poisson(1, n_samples)
    
    # Create labels based on realistic patterns
    # Combine multiple risk factors
    risk_score = (
        (data['age'] - 50) / 45 * 0.3 +  # Age factor
        (8 - data['speech_rate']) / 7 * 0.2 +  # Speech rate factor
        data['pause_frequency'] * 0.2 +  # Pause factor
        (0.1 - data['jitter']) / 0.095 * -0.1 +  # Voice quality factor
        data['filled_pauses'] / 10 * 0.2  # Fluency factor
    )
    
    # Add noise and create labels
    risk_score += np.random.normal(0, 0.1, n_samples)
    
    # Create three-class labels
    labels = np.zeros(n_samples)
    labels[risk_score > 0.6] = 2  # Dementia
    labels[(risk_score > 0.3) & (risk_score <= 0.6)] = 1  # MCI
    # labels <= 0.3 remain 0 (Normal)
    
    data['diagnosis'] = labels.astype(int)
    data['risk_score'] = risk_score
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    # Save dataset
    save_path = Path(r"D:\模型开发\comprehensive_speech_dementia.csv")
    df.to_csv(save_path, index=False)
    
    print(f"Comprehensive dataset created: {save_path}")
    print(f"Shape: {df.shape}")
    print(f"Label distribution:")
    label_names = ['Normal', 'MCI', 'Dementia']
    for i, name in enumerate(label_names):
        count = (df['diagnosis'] == i).sum()
        print(f"  {name}: {count} ({count/len(df)*100:.1f}%)")
    
    return df

def download_alternative_datasets():
    """Download alternative speech datasets"""
    
    print("Trying alternative speech datasets...")
    
    # Try CREMA-D emotion dataset
    try:
        print("Downloading CREMA-D emotion dataset...")
        dataset = tfds.load(
            'crema_d',
            download=True,
            data_dir=r"D:\模型开发\crema_d"
        )
        print("CREMA-D downloaded successfully!")
        return dataset
    except:
        print("CREMA-D not available")
    
    # Try SAVEE emotion dataset
    try:
        print("Downloading SAVEE emotion dataset...")
        dataset = tfds.load(
            'savee',
            download=True,
            data_dir=r"D:\模型开发\savee"
        )
        print("SAVEE downloaded successfully!")
        return dataset
    except:
        print("SAVEE not available")
    
    return None

def create_training_ready_dataset():
    """Create a training-ready dataset with proper splits"""
    
    print("Creating training-ready dataset...")
    
    # Load the comprehensive dataset
    df_path = Path(r"D:\模型开发\comprehensive_speech_dementia.csv")
    
    if df_path.exists():
        df = pd.read_csv(df_path)
    else:
        df = create_comprehensive_speech_dataset()
    
    # Create train/validation/test splits
    from sklearn.model_selection import train_test_split
    
    # First split: train+val vs test
    train_val, test = train_test_split(
        df, test_size=0.2, random_state=42, 
        stratify=df['diagnosis']
    )
    
    # Second split: train vs val
    train, val = train_test_split(
        train_val, test_size=0.25, random_state=42,
        stratify=train_val['diagnosis']
    )
    
    # Save splits
    base_path = Path(r"D:\模型开发")
    train.to_csv(base_path / "speech_train.csv", index=False)
    val.to_csv(base_path / "speech_val.csv", index=False)
    test.to_csv(base_path / "speech_test.csv", index=False)
    
    print(f"Dataset splits created:")
    print(f"  Train: {len(train)} samples")
    print(f"  Validation: {len(val)} samples") 
    print(f"  Test: {len(test)} samples")
    
    return train, val, test

if __name__ == "__main__":
    print("Complete Speech Dataset Downloader")
    print("=" * 50)
    
    # Create output directory
    os.makedirs(r"D:\模型开发", exist_ok=True)
    
    # Try downloading real datasets
    speech_commands = download_speech_commands()
    librispeech = download_librispeech()
    alternative = download_alternative_datasets()
    
    # Create comprehensive synthetic dataset
    comprehensive_df = create_comprehensive_speech_dataset()
    
    # Create training splits
    train, val, test = create_training_ready_dataset()
    
    print("\nDownload Summary:")
    print("=" * 30)
    
    if speech_commands[0] is not None:
        print("✅ Google Speech Commands: Downloaded")
    else:
        print("❌ Google Speech Commands: Failed")
    
    if librispeech is not None:
        print("✅ LibriSpeech: Downloaded")
    else:
        print("❌ LibriSpeech: Failed")
    
    if alternative is not None:
        print("✅ Alternative dataset: Downloaded")
    else:
        print("❌ Alternative dataset: Failed")
    
    print("✅ Comprehensive synthetic dataset: Created")
    print("✅ Training splits: Created")
    
    print(f"\nAll datasets saved to: D:\\模型开发\\")
    print("Ready for speech-based dementia detection training!")
