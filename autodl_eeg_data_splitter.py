"""
📊 简化版 EEG数据集划分器
直接使用已解压的EEG数据，快速划分训练集和验证集
"""

import os
import json
from collections import Counter
from sklearn.model_selection import train_test_split
import shutil

class SimpleEEGDataSplitter:
    """简化版 EEG数据集划分器"""

    def __init__(self):
        # ==========================================
        # 🔧 路径配置 - 请修改为您的实际路径
        # ==========================================

        # 已解压的数据路径 (您说的EEG_extracted)
        self.data_path = "EEG_extracted/dataset"  # 📍 修改为您的路径

        # 输出路径
        self.output_path = "EEG_splits"  # 📍 修改为您想要的输出路径

        # 划分比例 (训练70%, 验证15%, 测试15%)
        self.train_ratio = 0.70
        self.val_ratio = 0.15
        self.test_ratio = 0.15

        # 内部变量
        self.patient_labels = {}
        self.eeg_files = {}
        
    def load_labels(self):
        """加载患者标签"""
        print("� 加载患者标签...")

        # 读取标签文件
        participants_tsv = os.path.join(self.data_path, "participants.tsv")

        if not os.path.exists(participants_tsv):
            print(f"❌ 标签文件不存在: {participants_tsv}")
            return False

        # 解析标签文件
        with open(participants_tsv, 'r') as f:
            lines = f.read().strip().split('\n')
            header = lines[0].split('\t')

            for line in lines[1:]:
                if line.strip():
                    parts = line.split('\t')
                    subject_id = parts[0].strip()  # participant_id
                    label = parts[1].strip()       # Group (A/C/F)
                    self.patient_labels[subject_id] = label

        print(f"✅ 加载了 {len(self.patient_labels)} 个患者标签")
        return True
    
    def load_patient_labels(self):
        """从已解压的数据中加载患者标签信息"""
        print("📋 从解压数据加载患者标签信息...")
        
        try:
            # 读取标签定义
            participants_json_path = os.path.join(self.dataset_root, "participants.json")
            with open(participants_json_path, 'r', encoding='utf-8') as f:
                participants_json = json.load(f)
                self.label_definitions = participants_json['Group']['Levels']
            
            print(f"✅ 标签定义:")
            for code, description in self.label_definitions.items():
                print(f"   {code}: {description}")
            
            # 读取患者数据
            participants_tsv_path = os.path.join(self.dataset_root, "participants.tsv")
            with open(participants_tsv_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
                header = lines[0].split('\t')
                
                participant_col = header.index('participant_id')
                group_col = header.index('Group')
                
                for line in lines[1:]:
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) > max(participant_col, group_col):
                            participant_id = parts[participant_col].strip()
                            group_label = parts[group_col].strip()
                            self.patient_labels[participant_id] = group_label
            
            print(f"✅ 成功加载 {len(self.patient_labels)} 个患者的标签")
            return True
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return False
    
    def find_eeg_files(self):
        """查找所有EEG文件"""
        print(f"🔍 查找EEG文件...")
        
        eeg_files = {}
        missing_files = []
        
        for subject_id in self.patient_labels.keys():
            subject_dir = os.path.join(self.dataset_root, subject_id, "eeg")
            
            if os.path.exists(subject_dir):
                # 查找.set文件
                set_files = [f for f in os.listdir(subject_dir) if f.endswith('.set')]
                
                if set_files:
                    # 使用第一个.set文件
                    eeg_file_path = os.path.join(subject_dir, set_files[0])
                    eeg_files[subject_id] = eeg_file_path
                else:
                    missing_files.append(f"{subject_id}: 无.set文件")
            else:
                missing_files.append(f"{subject_id}: 目录不存在")
        
        self.eeg_files = eeg_files
        
        print(f"✅ 找到 {len(eeg_files)} 个EEG文件")
        
        if missing_files:
            print(f"⚠️ 缺失文件 ({len(missing_files)} 个):")
            for missing in missing_files[:5]:  # 只显示前5个
                print(f"   {missing}")
            if len(missing_files) > 5:
                print(f"   ... 还有 {len(missing_files)-5} 个")
        
        return len(eeg_files) > 0
    
    def display_patient_distribution(self):
        """显示患者分布"""
        print(f"\n📊 患者分布统计:")
        print("=" * 50)
        
        label_counts = Counter(self.patient_labels.values())
        total_patients = len(self.patient_labels)
        
        for label_code, count in sorted(label_counts.items()):
            if label_code in self.label_definitions:
                description = self.label_definitions[label_code]
                percentage = (count / total_patients) * 100
                print(f"🏷️ {label_code} - {description}:")
                print(f"   患者数量: {count} 人 ({percentage:.1f}%)")
                print(f"   有EEG文件: {sum(1 for sid in self.patient_labels.keys() if self.patient_labels[sid] == label_code and sid in self.eeg_files)} 人")
                print()
        
        print(f"📋 总患者数: {total_patients}")
        print(f"📋 有EEG文件的患者: {len(self.eeg_files)}")
    
    def create_stratified_split(self):
        """创建分层划分 - 只使用有EEG文件的患者"""
        print(f"\n🔄 创建分层数据划分...")
        print(f"📊 划分比例: 训练{self.train_ratio*100:.0f}% | 验证{self.val_ratio*100:.0f}% | 测试{self.test_ratio*100:.0f}%")
        
        # 只使用有EEG文件的患者
        valid_subjects = [sid for sid in self.patient_labels.keys() if sid in self.eeg_files]
        valid_labels = [self.patient_labels[sid] for sid in valid_subjects]
        
        print(f"📋 用于划分的有效患者数: {len(valid_subjects)}")
        
        # 第一次划分: 分出测试集
        train_val_ids, test_ids, train_val_labels, test_labels = train_test_split(
            valid_subjects, valid_labels,
            test_size=self.test_ratio,
            random_state=42,
            stratify=valid_labels
        )
        
        # 第二次划分: 从训练+验证中分出验证集
        val_ratio_adjusted = self.val_ratio / (self.train_ratio + self.val_ratio)
        train_ids, val_ids, train_labels, val_labels = train_test_split(
            train_val_ids, train_val_labels,
            test_size=val_ratio_adjusted,
            random_state=42,
            stratify=train_val_labels
        )
        
        # 保存划分结果
        self.split_result = {
            'train': {
                'subject_ids': train_ids,
                'labels': train_labels,
                'count': len(train_ids)
            },
            'val': {
                'subject_ids': val_ids,
                'labels': val_labels,
                'count': len(val_ids)
            },
            'test': {
                'subject_ids': test_ids,
                'labels': test_labels,
                'count': len(test_ids)
            }
        }
        
        # 显示划分结果
        self.display_split_results()
        
        return True
    
    def display_split_results(self):
        """显示划分结果"""
        print(f"\n✅ 数据划分完成!")
        print("=" * 60)
        
        class_names = {
            'A': '阿尔茨海默病',
            'C': '健康对照',
            'F': '额颞叶痴呆'
        }
        
        for split_name, split_data in self.split_result.items():
            print(f"\n📋 {split_name.upper()}集 ({split_data['count']} 人):")
            
            # 统计各类别数量
            label_counts = Counter(split_data['labels'])
            for label, count in sorted(label_counts.items()):
                class_name = class_names.get(label, label)
                percentage = count / split_data['count'] * 100
                print(f"   {label} ({class_name}): {count} 人 ({percentage:.1f}%)")
            
            # 显示具体的患者ID (前10个)
            print(f"   患者ID示例: {', '.join(split_data['subject_ids'][:10])}")
            if len(split_data['subject_ids']) > 10:
                print(f"   ... 还有 {len(split_data['subject_ids'])-10} 个")
    
    def create_split_directories(self):
        """创建划分目录结构"""
        print(f"\n📁 创建划分目录结构...")
        print(f"📍 输出基础路径: {self.output_base_path}")
        
        # 创建主目录
        os.makedirs(self.output_base_path, exist_ok=True)
        
        # 为每个划分创建目录
        split_dirs = {}
        for split_name in ['train', 'val', 'test']:
            split_dir = os.path.join(self.output_base_path, split_name)
            os.makedirs(split_dir, exist_ok=True)
            split_dirs[split_name] = split_dir
            print(f"   📂 {split_name}集目录: {split_dir}")
        
        self.split_dirs = split_dirs
        return True
    
    def copy_files_to_splits(self):
        """复制文件到对应的划分目录"""
        print(f"\n📋 复制文件到划分目录...")
        
        for split_name, split_data in self.split_result.items():
            split_dir = self.split_dirs[split_name]
            subject_ids = split_data['subject_ids']
            
            print(f"\n🔄 处理 {split_name.upper()}集 ({len(subject_ids)} 个文件)...")
            
            copied_count = 0
            for subject_id in subject_ids:
                if subject_id in self.eeg_files:
                    source_file = self.eeg_files[subject_id]
                    if os.path.exists(source_file):
                        # 目标文件名
                        filename = os.path.basename(source_file)
                        target_file = os.path.join(split_dir, filename)
                        
                        # 复制文件
                        try:
                            shutil.copy2(source_file, target_file)
                            copied_count += 1
                        except Exception as e:
                            print(f"   ⚠️ 复制失败 {subject_id}: {e}")
                    else:
                        print(f"   ⚠️ 文件不存在: {subject_id}")
                else:
                    print(f"   ⚠️ 未找到EEG文件: {subject_id}")
            
            print(f"   ✅ {split_name.upper()}集: 成功复制 {copied_count}/{len(subject_ids)} 个文件")
    
    def save_split_metadata(self):
        """保存划分元数据"""
        print(f"\n💾 保存划分元数据...")
        
        # 准备元数据
        metadata = {
            'dataset_info': {
                'source_path': self.extracted_data_path,
                'dataset_root': self.dataset_root,
                'output_path': self.output_base_path,
                'total_patients': len(self.patient_labels),
                'valid_patients': len(self.eeg_files),
                'split_ratios': {
                    'train': self.train_ratio,
                    'val': self.val_ratio,
                    'test': self.test_ratio
                }
            },
            'label_definitions': self.label_definitions,
            'patient_labels': self.patient_labels,
            'split_result': self.split_result,
            'file_paths': {
                split_name: {
                    'directory': self.split_dirs[split_name],
                    'subjects': split_data['subject_ids'],
                    'labels': split_data['labels']
                }
                for split_name, split_data in self.split_result.items()
            }
        }
        
        # 保存到JSON文件
        metadata_file = os.path.join(self.output_base_path, "split_metadata.json")
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            print(f"✅ 元数据已保存: {metadata_file}")
        except Exception as e:
            print(f"❌ 元数据保存失败: {e}")
        
        # 保存简化的标签文件
        for split_name, split_data in self.split_result.items():
            label_file = os.path.join(self.split_dirs[split_name], "labels.txt")
            try:
                with open(label_file, 'w', encoding='utf-8') as f:
                    f.write("subject_id\tlabel\tlabel_name\n")
                    for subject_id, label in zip(split_data['subject_ids'], split_data['labels']):
                        label_name = self.label_definitions.get(label, label)
                        f.write(f"{subject_id}\t{label}\t{label_name}\n")
                print(f"✅ {split_name.upper()}集标签文件: {label_file}")
            except Exception as e:
                print(f"❌ {split_name}集标签文件保存失败: {e}")
    
    def generate_summary_report(self):
        """生成总结报告"""
        print(f"\n📊 生成总结报告...")
        
        report_file = os.path.join(self.output_base_path, "split_summary.txt")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("AutoDL环境 EEG数据集划分总结报告\n")
                f.write("=" * 50 + "\n\n")
                
                f.write(f"数据集信息:\n")
                f.write(f"  解压数据路径: {self.extracted_data_path}\n")
                f.write(f"  数据集根目录: {self.dataset_root}\n")
                f.write(f"  输出目录: {self.output_base_path}\n")
                f.write(f"  总患者数: {len(self.patient_labels)}\n")
                f.write(f"  有效患者数: {len(self.eeg_files)}\n\n")
                
                f.write(f"划分比例:\n")
                f.write(f"  训练集: {self.train_ratio*100:.0f}%\n")
                f.write(f"  验证集: {self.val_ratio*100:.0f}%\n")
                f.write(f"  测试集: {self.test_ratio*100:.0f}%\n\n")
                
                f.write(f"划分结果:\n")
                for split_name, split_data in self.split_result.items():
                    f.write(f"  {split_name.upper()}集: {split_data['count']} 人\n")
                    label_counts = Counter(split_data['labels'])
                    for label, count in sorted(label_counts.items()):
                        label_name = self.label_definitions.get(label, label)
                        f.write(f"    {label} ({label_name}): {count} 人\n")
                    f.write(f"\n")
                
                f.write(f"文件路径:\n")
                for split_name in ['train', 'val', 'test']:
                    f.write(f"  {split_name.upper()}集目录: {self.split_dirs[split_name]}\n")
            
            print(f"✅ 总结报告已保存: {report_file}")
            
        except Exception as e:
            print(f"❌ 总结报告保存失败: {e}")
    
    def run_complete_split(self):
        """运行完整的数据划分流程"""
        print("🚀 开始AutoDL环境EEG数据集划分流程")
        print("=" * 60)
        
        # 步骤1: 检查数据结构
        if not self.check_data_structure():
            return False
        
        # 步骤2: 加载患者标签
        if not self.load_patient_labels():
            return False
        
        # 步骤3: 查找EEG文件
        if not self.find_eeg_files():
            return False
        
        # 步骤4: 显示患者分布
        self.display_patient_distribution()
        
        # 步骤5: 创建分层划分
        if not self.create_stratified_split():
            return False
        
        # 步骤6: 创建划分目录
        if not self.create_split_directories():
            return False
        
        # 步骤7: 复制文件到划分目录
        self.copy_files_to_splits()
        
        # 步骤8: 保存元数据
        self.save_split_metadata()
        
        # 步骤9: 生成总结报告
        self.generate_summary_report()
        
        print(f"\n🎉 AutoDL环境数据划分完成!")
        print("=" * 60)
        print(f"📁 输出目录: {self.output_base_path}")
        print(f"📋 查看详细信息: {os.path.join(self.output_base_path, 'split_summary.txt')}")
        print(f"📊 元数据文件: {os.path.join(self.output_base_path, 'split_metadata.json')}")
        
        return True


def main():
    """主函数"""
    print("📊 AutoDL环境 EEG数据集划分器")
    print("=" * 50)
    print("🔧 适用于已解压的EEG数据集")
    print("⚠️ 使用前请检查并修改脚本顶部的路径配置!")
    print()
    
    # 创建划分器
    splitter = AutoDLEEGDataSplitter()
    
    # 运行完整划分流程
    success = splitter.run_complete_split()
    
    if success:
        print(f"\n✅ 所有步骤完成!")
        print(f"📋 下一步: 使用划分好的数据训练EEG模型")
    else:
        print(f"\n❌ 划分过程中出现错误，请检查配置和路径")


if __name__ == "__main__":
    main()
