"""
🔧 GPU问题诊断和修复工具
专门解决AutoDL环境中的cuDNN/GPU问题
"""

import os
import subprocess
import sys

def check_system_info():
    """检查系统信息"""
    print("🔍 系统信息检查")
    print("=" * 40)
    
    # 检查CUDA版本
    try:
        result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ CUDA版本:")
            print(result.stdout)
        else:
            print("❌ CUDA未安装或不可用")
    except FileNotFoundError:
        print("❌ nvcc命令未找到，CUDA可能未正确安装")
    
    # 检查GPU
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("\n✅ GPU信息:")
            lines = result.stdout.split('\n')
            for line in lines[:10]:  # 只显示前10行
                if line.strip():
                    print(line)
        else:
            print("❌ nvidia-smi失败")
    except FileNotFoundError:
        print("❌ nvidia-smi命令未找到")

def check_tensorflow():
    """检查TensorFlow配置"""
    print("\n🔍 TensorFlow配置检查")
    print("=" * 40)
    
    try:
        import tensorflow as tf
        print(f"✅ TensorFlow版本: {tf.__version__}")
        
        # 检查GPU设备
        gpus = tf.config.list_physical_devices('GPU')
        print(f"📊 检测到GPU数量: {len(gpus)}")
        
        if gpus:
            for i, gpu in enumerate(gpus):
                print(f"   GPU {i}: {gpu}")
        else:
            print("⚠️ 未检测到GPU设备")
        
        # 检查cuDNN
        try:
            # 尝试创建一个简单的卷积操作
            with tf.device('/GPU:0' if gpus else '/CPU:0'):
                x = tf.random.normal((1, 10, 10, 1))
                conv = tf.keras.layers.Conv2D(1, 3)
                y = conv(x)
            print("✅ cuDNN测试通过")
            return True
        except Exception as e:
            print(f"❌ cuDNN测试失败: {e}")
            return False
            
    except ImportError:
        print("❌ TensorFlow未安装")
        return False

def fix_gpu_issues():
    """尝试修复GPU问题"""
    print("\n🔧 尝试修复GPU问题")
    print("=" * 40)
    
    # 方案1: 重新安装TensorFlow
    print("📦 方案1: 重新安装TensorFlow...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'tensorflow', '-y'], 
                      capture_output=True)
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'tensorflow==2.8.0'], 
                      capture_output=True)
        print("✅ TensorFlow重新安装完成")
    except Exception as e:
        print(f"❌ TensorFlow重新安装失败: {e}")
    
    # 方案2: 安装兼容的cuDNN
    print("\n📦 方案2: 检查cuDNN兼容性...")
    try:
        # 检查是否有libcudnn
        result = subprocess.run(['ldconfig', '-p'], capture_output=True, text=True)
        if 'libcudnn' in result.stdout:
            print("✅ 系统中找到libcudnn")
        else:
            print("⚠️ 系统中未找到libcudnn")
    except Exception as e:
        print(f"❌ cuDNN检查失败: {e}")

def create_cpu_fallback():
    """创建CPU备用方案"""
    print("\n💻 创建CPU备用方案")
    print("=" * 40)
    
    cpu_script = """
# CPU强制模式
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
tf.config.set_visible_devices([], 'GPU')

print("🔧 已切换到CPU模式")
"""
    
    with open('force_cpu_mode.py', 'w') as f:
        f.write(cpu_script)
    
    print("✅ CPU备用脚本已创建: force_cpu_mode.py")
    print("💡 使用方法: 在训练脚本开头添加 'exec(open('force_cpu_mode.py').read())'")

def recommend_solutions():
    """推荐解决方案"""
    print("\n💡 推荐解决方案")
    print("=" * 40)
    
    print("🎯 方案优先级:")
    print("1. 使用CPU训练器 (推荐)")
    print("   python cpu_only_eeg_trainer.py")
    print()
    print("2. 更换AutoDL镜像")
    print("   - TensorFlow 2.6.0 Python 3.8 Cuda 11.2")
    print("   - PyTorch 1.10.0 Python 3.8 Cuda 11.3")
    print()
    print("3. 手动修复cuDNN")
    print("   conda install cudnn=8.2.1")
    print("   或")
    print("   pip install nvidia-cudnn-cu11==*********")
    print()
    print("4. 降级TensorFlow")
    print("   pip install tensorflow==2.6.0")

def main():
    """主函数"""
    print("🔧 AutoDL GPU问题诊断和修复工具")
    print("=" * 50)
    
    # 检查系统
    check_system_info()
    
    # 检查TensorFlow
    tf_ok = check_tensorflow()
    
    if not tf_ok:
        # 尝试修复
        fix_gpu_issues()
        
        # 重新检查
        print("\n🔄 重新检查TensorFlow...")
        tf_ok = check_tensorflow()
    
    # 创建备用方案
    create_cpu_fallback()
    
    # 推荐解决方案
    recommend_solutions()
    
    if tf_ok:
        print("\n🎉 GPU配置正常，可以使用GPU训练")
    else:
        print("\n⚠️ GPU配置有问题，建议使用CPU训练器")
        print("📝 运行: python cpu_only_eeg_trainer.py")

if __name__ == "__main__":
    main()
