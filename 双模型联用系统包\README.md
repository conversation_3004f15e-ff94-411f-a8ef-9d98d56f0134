# 🧠 双模型AI痴呆症识别系统 v2.0

基于深度学习的医学影像智能分析系统，采用双模型串联架构，实现CT图像验证和痴呆症症状智能分析。

## ✨ 主要特性

### 🤖 双模型架构
- **CT图像识别模型**: 自动验证输入图像是否为CT扫描
- **症状分析模型**: 对CT图像进行痴呆症分类分析
- **智能串联**: 只对通过CT验证的图像进行症状分析

### 🎯 核心功能
- ✅ **多类别痴呆症检测**: 轻度、中度、无痴呆、非常轻度
- ✅ **实时摄像头支持**: 拍照即时分析
- ✅ **概率分布可视化**: 详细的预测概率展示
- ✅ **专业报告生成**: PDF和HTML格式报告
- ✅ **历史记录管理**: 完整的分析历史追踪
- ✅ **现代化GUI**: CustomTkinter深色主题界面

### 🔬 技术特点
- **高精度**: 双模型验证确保分析准确性
- **用户友好**: 直观的图形界面和操作流程
- **专业级**: 符合医学影像分析标准
- **可扩展**: 模块化设计，易于功能扩展

## 🚀 快速开始

### 系统要求
- Python 3.8+
- Windows 10/11 或 Linux
- 8GB+ RAM (推荐16GB)
- 可选: NVIDIA GPU (CUDA支持)

### 安装方法

#### 方法1: 使用pip安装 (推荐)
```bash
pip install dual-model-dementia-detector
```

#### 方法2: 从源码安装
```bash
git clone https://github.com/aimedical/dual-model-dementia-detector.git
cd dual-model-dementia-detector
pip install -r requirements.txt
python setup.py install
```

#### 方法3: 开发模式安装
```bash
git clone https://github.com/aimedical/dual-model-dementia-detector.git
cd dual-model-dementia-detector
pip install -e .
```

### 模型文件准备
将您的模型文件放置在以下位置：
```
models/
├── ct_other_model.h5      # CT图像识别模型
└── ct_class.h5            # 症状分析模型
```

## 📖 使用指南

### GUI模式 (推荐)
```bash
# 启动图形界面
dmd-gui

# 或者直接运行
python -m dual_model_detector.gui
```

### 命令行模式
```bash
# 分析单张图像
dmd-cli --image path/to/image.jpg

# 批量分析
dmd-cli --batch path/to/images/

# 生成报告
dmd-cli --image image.jpg --report pdf --output report.pdf
```

### Python API
```python
from dual_model_detector import DualModelDetector

# 初始化检测器
detector = DualModelDetector(
    ct_model_path="models/ct_other_model.h5",
    classification_model_path="models/ct_class.h5"
)

# 分析图像
result = detector.analyze_image("path/to/ct_image.jpg")

# 查看结果
print(f"CT检测: {result['ct_detection']['is_ct']}")
print(f"症状分析: {result['symptom_analysis']['predicted_class']}")
```

## 🔧 配置选项

### 模型配置
```python
# config/model_config.json
{
    "ct_detection": {
        "model_path": "models/ct_other_model.h5",
        "threshold": 0.5,
        "input_size": [150, 150, 3]
    },
    "symptom_analysis": {
        "model_path": "models/ct_class.h5",
        "classes": [
            "MildDemented",
            "ModerateDemented", 
            "NonDemented",
            "VeryMildDemented"
        ]
    }
}
```

### GUI配置
```python
# config/gui_config.json
{
    "theme": "dark",
    "color_theme": "blue",
    "window_size": "1200x800",
    "auto_save_results": true,
    "default_report_format": "pdf"
}
```

## 📊 分析流程

```mermaid
graph TD
    A[输入图像] --> B[图像预处理]
    B --> C[CT图像检测]
    C --> D{是CT图像?}
    D -->|是| E[症状分析]
    D -->|否| F[提示重新选择]
    E --> G[生成结果报告]
    F --> A
```

## 📄 报告示例

### PDF报告特性
- 📋 完整的分析信息
- 📊 概率分布图表
- 🖼️ 原始图像展示
- ⚠️ 专业免责声明

### HTML报告特性
- 🌐 交互式网页展示
- 📱 响应式设计
- 🎨 现代化视觉效果
- 🔗 可分享链接

## 🛠️ 开发指南

### 项目结构
```
dual_model_detector/
├── __init__.py
├── main.py              # 主程序入口
├── gui.py               # GUI界面
├── cli.py               # 命令行接口
├── core/
│   ├── detector.py      # 核心检测器
│   ├── models.py        # 模型管理
│   └── utils.py         # 工具函数
├── reports/
│   ├── pdf_generator.py # PDF报告生成
│   └── html_generator.py# HTML报告生成
├── config/
│   ├── settings.py      # 配置管理
│   └── *.json          # 配置文件
└── assets/
    ├── icons/          # 图标资源
    └── templates/      # 报告模板
```

### 扩展开发
```python
# 自定义分析器
from dual_model_detector.core import BaseDetector

class CustomDetector(BaseDetector):
    def custom_analysis(self, image):
        # 实现自定义分析逻辑
        pass
```

## 🔒 安全和隐私

- ✅ **本地处理**: 所有分析在本地进行，无数据上传
- ✅ **数据加密**: 敏感结果可选择加密存储
- ✅ **访问控制**: 支持用户权限管理
- ✅ **审计日志**: 完整的操作记录

## ⚠️ 重要声明

**本软件仅供研究和参考使用，不能替代专业医学诊断。**

任何医疗决策都应基于合格医疗专业人员的建议。请咨询神经科医生或相关专家获取准确的诊断和治疗方案。

## 🤝 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 贡献方式
- 🐛 报告Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码

## 📞 支持和联系

- 📧 **邮箱**: <EMAIL>
- 🐛 **Bug报告**: [GitHub Issues](https://github.com/aimedical/dual-model-dementia-detector/issues)
- 📖 **文档**: [在线文档](https://dual-model-detector.readthedocs.io/)
- 💬 **讨论**: [GitHub Discussions](https://github.com/aimedical/dual-model-dementia-detector/discussions)

## 📜 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和研究人员。

---

**© 2024 双模型AI Medical Solutions | 让AI技术服务医疗健康**
