# AI痴呆症识别系统 - 客户端服务器架构实现指南

## 🎯 架构概述

本指南提供将您的本地AI痴呆症识别模型部署为远程服务的完整技术方案，支持多用户并发访问，确保数据安全和系统稳定性。

## 🏗️ 系统架构设计

### 整体架构图
```
[客户端设备] ←→ [网络传输] ←→ [服务器端]
     ↓                           ↓
[图像采集]                   [AI模型处理]
[结果显示]                   [数据存储]
[用户界面]                   [日志记录]
```

### 核心组件
1. **Web API服务器**：处理HTTP请求，管理模型推理
2. **AI模型引擎**：加载和运行痴呆症识别模型
3. **数据库系统**：存储用户数据、分析历史
4. **安全认证模块**：用户身份验证和权限管理
5. **客户端应用**：用户界面和数据采集

## 💻 服务器端实现

### 1. Flask Web API服务器

```python
# server.py - 主服务器文件
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import JWTManager, create_access_token, jwt_required
import tensorflow as tf
import numpy as np
from PIL import Image
import io
import base64
import logging
from datetime import datetime
import sqlite3
import hashlib

app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = 'your-secret-key-here'  # 更改为安全的密钥
jwt = JWTManager(app)
CORS(app)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIModelService:
    def __init__(self):
        self.ct_model = None
        self.dementia_model = None
        self.load_models()
    
    def load_models(self):
        """加载AI模型"""
        try:
            # 加载CT识别模型
            self.ct_model = tf.keras.models.load_model('models/ct_detection_model.h5')
            # 加载痴呆症识别模型
            self.dementia_model = tf.keras.models.load_model('models/dementia_classification_model.h5')
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
    
    def preprocess_image(self, image_data):
        """图像预处理"""
        try:
            # 解码base64图像
            image_bytes = base64.b64decode(image_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # 调整尺寸和归一化
            image = image.resize((150, 150))
            image_array = np.array(image) / 255.0
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            return None
    
    def predict(self, image_array):
        """执行AI预测"""
        try:
            # 步骤1: CT图像识别
            ct_prediction = self.ct_model.predict(image_array, verbose=0)
            is_ct = ct_prediction[0][0] > 0.5
            ct_confidence = float(ct_prediction[0][0])
            
            if not is_ct:
                return {
                    'success': False,
                    'error': '输入图像不是CT图像',
                    'ct_confidence': ct_confidence
                }
            
            # 步骤2: 痴呆症分类
            dementia_prediction = self.dementia_model.predict(image_array, verbose=0)
            predicted_class = np.argmax(dementia_prediction, axis=1)[0]
            confidence = float(np.max(dementia_prediction))
            
            class_labels = [
                'MildDemented(轻度痴呆)',
                'ModerateDemented(中度痴呆)', 
                'NonDemented(无痴呆)',
                'VeryMildDemented(非常轻度痴呆)'
            ]
            
            return {
                'success': True,
                'ct_confidence': ct_confidence,
                'predicted_class': class_labels[predicted_class],
                'confidence': confidence,
                'probabilities': dementia_prediction[0].tolist(),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {'success': False, 'error': str(e)}

# 初始化AI服务
ai_service = AIModelService()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

@app.route('/api/login', methods=['POST'])
def login():
    """用户登录接口"""
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    # 简单的用户验证（生产环境需要更安全的实现）
    if verify_user(username, password):
        access_token = create_access_token(identity=username)
        return jsonify({'access_token': access_token})
    else:
        return jsonify({'error': '用户名或密码错误'}), 401

@app.route('/api/predict', methods=['POST'])
@jwt_required()
def predict():
    """AI预测接口"""
    try:
        data = request.get_json()
        image_data = data.get('image')
        
        if not image_data:
            return jsonify({'error': '缺少图像数据'}), 400
        
        # 预处理图像
        image_array = ai_service.preprocess_image(image_data)
        if image_array is None:
            return jsonify({'error': '图像处理失败'}), 400
        
        # 执行预测
        result = ai_service.predict(image_array)
        
        # 记录日志
        logger.info(f"预测请求 - 用户: {request.current_user}, 结果: {result.get('success')}")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"预测接口错误: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

def verify_user(username, password):
    """验证用户凭据"""
    # 这里应该连接到真实的用户数据库
    # 示例：简单的硬编码验证
    users = {
        'doctor1': 'password123',
        'admin': 'admin123'
    }
    return users.get(username) == password

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)
```

### 2. 数据库设计

```sql
-- database.sql - 数据库结构
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

CREATE TABLE predictions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    image_hash VARCHAR(64),
    ct_confidence REAL,
    predicted_class VARCHAR(50),
    confidence REAL,
    probabilities TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id)
);

CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level VARCHAR(10),
    message TEXT,
    user_id INTEGER,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 配置文件

```python
# config.py - 服务器配置
import os

class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key'
    
    # 数据库配置
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///app.db'
    
    # 模型路径
    CT_MODEL_PATH = 'models/ct_detection_model.h5'
    DEMENTIA_MODEL_PATH = 'models/dementia_classification_model.h5'
    
    # 安全配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_TIMEOUT = 30  # 秒
    
    # 性能配置
    MAX_CONCURRENT_REQUESTS = 10
    MODEL_CACHE_SIZE = 2
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/app.log'
```

## 📱 客户端实现

### 1. Web客户端（HTML + JavaScript）

```html
<!-- client.html - Web客户端界面 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI痴呆症识别系统</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .result-area { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>🧠 AI痴呆症识别系统</h1>
    
    <div id="login-section">
        <h2>用户登录</h2>
        <input type="text" id="username" placeholder="用户名">
        <input type="password" id="password" placeholder="密码">
        <button onclick="login()">登录</button>
    </div>
    
    <div id="main-section" style="display: none;">
        <h2>上传CT图像进行分析</h2>
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>点击此处选择CT图像文件</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="handleFileSelect(event)">
        </div>
        
        <div id="preview-area"></div>
        <button id="analyzeBtn" onclick="analyzeImage()" disabled>开始分析</button>
        
        <div id="result-area" class="result-area" style="display: none;"></div>
    </div>

    <script>
        let authToken = null;
        let selectedImage = null;
        
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    authToken = data.access_token;
                    document.getElementById('login-section').style.display = 'none';
                    document.getElementById('main-section').style.display = 'block';
                } else {
                    alert('登录失败: ' + data.error);
                }
            } catch (error) {
                alert('登录错误: ' + error.message);
            }
        }
        
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    selectedImage = e.target.result.split(',')[1]; // 移除data:image/...;base64,前缀
                    
                    // 显示预览
                    document.getElementById('preview-area').innerHTML = 
                        `<img src="${e.target.result}" style="max-width: 300px; max-height: 300px;">`;
                    
                    document.getElementById('analyzeBtn').disabled = false;
                };
                reader.readAsDataURL(file);
            }
        }
        
        async function analyzeImage() {
            if (!selectedImage || !authToken) return;
            
            document.getElementById('analyzeBtn').disabled = true;
            document.getElementById('analyzeBtn').textContent = '分析中...';
            
            try {
                const response = await fetch('/api/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ image: selectedImage })
                });
                
                const result = await response.json();
                displayResult(result);
                
            } catch (error) {
                displayResult({ success: false, error: error.message });
            } finally {
                document.getElementById('analyzeBtn').disabled = false;
                document.getElementById('analyzeBtn').textContent = '开始分析';
            }
        }
        
        function displayResult(result) {
            const resultArea = document.getElementById('result-area');
            resultArea.style.display = 'block';
            
            if (result.success) {
                resultArea.innerHTML = `
                    <h3 class="success">✅ 分析完成</h3>
                    <p><strong>CT图像置信度:</strong> ${(result.ct_confidence * 100).toFixed(2)}%</p>
                    <p><strong>预测结果:</strong> ${result.predicted_class}</p>
                    <p><strong>置信度:</strong> ${(result.confidence * 100).toFixed(2)}%</p>
                    <p><strong>分析时间:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
                `;
            } else {
                resultArea.innerHTML = `
                    <h3 class="error">❌ 分析失败</h3>
                    <p>${result.error}</p>
                `;
            }
        }
    </script>
</body>
</html>
```

### 2. Python桌面客户端

```python
# desktop_client.py - 桌面客户端
import tkinter as tk
from tkinter import filedialog, messagebox
import requests
import base64
import json
from PIL import Image, ImageTk

class DementiaDetectionClient:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AI痴呆症识别客户端")
        self.root.geometry("600x500")
        
        self.server_url = "http://localhost:5000"
        self.auth_token = None
        self.selected_image_path = None
        
        self.create_widgets()
    
    def create_widgets(self):
        # 登录框架
        login_frame = tk.Frame(self.root)
        login_frame.pack(pady=20)
        
        tk.Label(login_frame, text="服务器地址:").grid(row=0, column=0, padx=5)
        self.server_entry = tk.Entry(login_frame, width=30)
        self.server_entry.insert(0, self.server_url)
        self.server_entry.grid(row=0, column=1, padx=5)
        
        tk.Label(login_frame, text="用户名:").grid(row=1, column=0, padx=5)
        self.username_entry = tk.Entry(login_frame, width=30)
        self.username_entry.grid(row=1, column=1, padx=5)
        
        tk.Label(login_frame, text="密码:").grid(row=2, column=0, padx=5)
        self.password_entry = tk.Entry(login_frame, show="*", width=30)
        self.password_entry.grid(row=2, column=1, padx=5)
        
        tk.Button(login_frame, text="连接", command=self.login).grid(row=3, column=0, columnspan=2, pady=10)
        
        # 主功能框架
        self.main_frame = tk.Frame(self.root)
        
        tk.Button(self.main_frame, text="选择CT图像", command=self.select_image).pack(pady=10)
        
        # 图像预览
        self.image_label = tk.Label(self.main_frame)
        self.image_label.pack(pady=10)
        
        tk.Button(self.main_frame, text="开始分析", command=self.analyze_image).pack(pady=10)
        
        # 结果显示
        self.result_text = tk.Text(self.main_frame, height=10, width=60)
        self.result_text.pack(pady=10)
    
    def login(self):
        self.server_url = self.server_entry.get()
        username = self.username_entry.get()
        password = self.password_entry.get()
        
        try:
            response = requests.post(f"{self.server_url}/api/login", 
                                   json={"username": username, "password": password})
            
            if response.status_code == 200:
                self.auth_token = response.json()["access_token"]
                messagebox.showinfo("成功", "连接成功！")
                self.main_frame.pack(fill="both", expand=True)
            else:
                messagebox.showerror("错误", "登录失败")
        except Exception as e:
            messagebox.showerror("错误", f"连接失败: {e}")
    
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="选择CT图像",
            filetypes=[("图像文件", "*.jpg *.jpeg *.png *.bmp")]
        )
        
        if file_path:
            self.selected_image_path = file_path
            
            # 显示预览
            image = Image.open(file_path)
            image.thumbnail((200, 200))
            photo = ImageTk.PhotoImage(image)
            self.image_label.configure(image=photo)
            self.image_label.image = photo
    
    def analyze_image(self):
        if not self.selected_image_path or not self.auth_token:
            messagebox.showwarning("警告", "请先登录并选择图像")
            return
        
        try:
            # 编码图像
            with open(self.selected_image_path, "rb") as f:
                image_data = base64.b64encode(f.read()).decode()
            
            # 发送请求
            headers = {"Authorization": f"Bearer {self.auth_token}"}
            response = requests.post(f"{self.server_url}/api/predict",
                                   json={"image": image_data},
                                   headers=headers)
            
            result = response.json()
            self.display_result(result)
            
        except Exception as e:
            messagebox.showerror("错误", f"分析失败: {e}")
    
    def display_result(self, result):
        self.result_text.delete(1.0, tk.END)
        
        if result.get("success"):
            text = f"""
✅ 分析完成

CT图像置信度: {result['ct_confidence']*100:.2f}%
预测结果: {result['predicted_class']}
置信度: {result['confidence']*100:.2f}%
分析时间: {result['timestamp']}

详细概率分布:
"""
            for i, prob in enumerate(result['probabilities']):
                labels = ['轻度痴呆', '中度痴呆', '无痴呆', '非常轻度痴呆']
                text += f"{labels[i]}: {prob*100:.2f}%\n"
        else:
            text = f"❌ 分析失败\n错误: {result.get('error', '未知错误')}"
        
        self.result_text.insert(1.0, text)
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    client = DementiaDetectionClient()
    client.run()
```

## 🔒 安全性考虑

### 1. 身份认证和授权
- **JWT令牌**：使用JSON Web Token进行用户认证
- **角色权限**：区分医生、管理员等不同角色
- **会话管理**：设置合理的令牌过期时间

### 2. 数据传输安全
- **HTTPS加密**：所有通信使用SSL/TLS加密
- **数据压缩**：减少传输数据量
- **请求限制**：防止DDoS攻击

### 3. 数据存储安全
- **密码哈希**：使用bcrypt等安全哈希算法
- **数据库加密**：敏感数据加密存储
- **备份策略**：定期备份重要数据

## 📊 硬件要求分析

### 服务器端要求
- **CPU**: 8核以上，支持AVX指令集
- **内存**: 16GB+ (模型加载需要4-6GB，并发处理需要额外内存)
- **GPU**: 可选，NVIDIA GTX 1060或更高(加速推理)
- **存储**: SSD 100GB+ (模型文件、数据库、日志)
- **网络**: 上传带宽10Mbps+ (支持多用户并发)

### 客户端要求
- **最低配置**: 2GB内存，任何支持现代浏览器的设备
- **推荐配置**: 4GB内存，稳定的网络连接
- **网络要求**: 下载带宽2Mbps+ (图像上传和结果接收)

## 🚀 部署指南

### 1. 服务器部署步骤
```bash
# 1. 安装依赖
pip install flask tensorflow pillow flask-cors flask-jwt-extended

# 2. 创建目录结构
mkdir ai_server
cd ai_server
mkdir models logs static templates

# 3. 复制模型文件
cp your_models/*.h5 models/

# 4. 启动服务器
python server.py
```

### 2. 生产环境配置
```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 server:app

# 使用Nginx反向代理
# /etc/nginx/sites-available/ai_server
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📈 性能优化建议

### 1. 模型优化
- **模型量化**：减少模型大小和推理时间
- **批处理**：支持多图像并行处理
- **模型缓存**：避免重复加载模型

### 2. 系统优化
- **连接池**：数据库连接池管理
- **缓存策略**：Redis缓存频繁查询结果
- **负载均衡**：多服务器实例分担负载

### 3. 监控和维护
- **性能监控**：CPU、内存、网络使用率
- **错误日志**：详细的错误追踪和报告
- **自动重启**：服务异常时自动恢复

这个架构方案提供了完整的远程AI服务解决方案，既保证了安全性，又确保了良好的用户体验。您可以根据实际需求调整配置和功能。
