#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
环境诊断脚本 - 检查Python环境和依赖
"""

import sys
import os

print("=" * 50)
print("🔍 Python环境诊断")
print("=" * 50)

# 1. Python基本信息
print(f"✅ Python版本: {sys.version}")
print(f"✅ Python路径: {sys.executable}")
print(f"✅ 当前工作目录: {os.getcwd()}")

# 2. 检查关键模块
modules_to_check = [
    'tensorflow',
    'numpy', 
    'matplotlib',
    'PIL'
]

print("\n📦 模块检查:")
for module in modules_to_check:
    try:
        __import__(module)
        if module == 'tensorflow':
            import tensorflow as tf
            print(f"✅ {module}: 已安装 (版本: {tf.__version__})")
        else:
            print(f"✅ {module}: 已安装")
    except ImportError as e:
        print(f"❌ {module}: 未安装 - {e}")

# 3. 检查文件路径
print("\n📁 文件路径检查:")
files_to_check = [
    r"D:\模型开发\升级model.h5",
    r"D:\模型开发\1.jpg"
]

for file_path in files_to_check:
    if os.path.exists(file_path):
        print(f"✅ {file_path}: 存在")
    else:
        print(f"❌ {file_path}: 不存在")

# 4. 环境变量检查
print("\n🔧 环境变量:")
env_vars = ['TF_CPP_MIN_LOG_LEVEL', 'TF_ENABLE_ONEDNN_OPTS']
for var in env_vars:
    value = os.environ.get(var, '未设置')
    print(f"   {var}: {value}")

print("\n" + "=" * 50)
print("🎯 诊断完成！")
print("=" * 50)

# 5. 简单的TensorFlow测试
print("\n🧪 TensorFlow快速测试:")
try:
    import tensorflow as tf
    # 创建一个简单的张量
    test_tensor = tf.constant([1, 2, 3, 4])
    print(f"✅ TensorFlow工作正常: {test_tensor}")
except Exception as e:
    print(f"❌ TensorFlow测试失败: {e}")
