# -*- coding: utf-8 -*-
"""
图像验证测试工具
用于分析为什么某些图像会触发验证警告
"""

import cv2
import numpy as np
from PIL import Image
import os
import matplotlib.pyplot as plt

def analyze_image_properties(image_path):
    """详细分析图像属性"""
    print(f"\n🔍 分析图像: {os.path.basename(image_path)}")
    print("=" * 50)
    
    try:
        # 加载图像
        pil_image = Image.open(image_path)
        img_array = np.array(pil_image)
        
        # 基本信息
        height, width = img_array.shape[:2]
        print(f"📏 尺寸: {width} x {height}")
        print(f"📊 通道数: {len(img_array.shape)}")
        print(f"📁 格式: {pil_image.format}")
        
        # 转换为灰度图
        if len(img_array.shape) == 3:
            gray_img = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
            # 检查颜色差异
            r, g, b = img_array[:,:,0], img_array[:,:,1], img_array[:,:,2]
            color_variance = np.var([np.mean(r), np.mean(g), np.mean(b)])
            print(f"🎨 颜色方差: {color_variance:.2f} {'(彩色)' if color_variance > 100 else '(灰度)'}")
        else:
            gray_img = img_array
            print(f"🎨 图像类型: 灰度图")
        
        # 亮度分析
        unique_values = len(np.unique(gray_img))
        print(f"🌈 唯一颜色数: {unique_values}/256")
        
        # 亮度分布
        hist = cv2.calcHist([gray_img], [0], None, [256], [0, 256])
        max_hist_value = np.max(hist)
        total_pixels = width * height
        max_ratio = max_hist_value / total_pixels
        print(f"📊 最大亮度占比: {max_ratio:.2%}")
        
        # 对比度
        contrast = np.std(gray_img)
        print(f"🔆 对比度(标准差): {contrast:.2f}")
        
        # 边缘检测
        edges = cv2.Canny(gray_img, 30, 100)
        edge_ratio = np.sum(edges > 0) / (width * height)
        print(f"🔲 边缘比例: {edge_ratio:.3f} ({edge_ratio*100:.1f}%)")
        
        # 亮度统计
        print(f"💡 亮度统计:")
        print(f"   最小值: {np.min(gray_img)}")
        print(f"   最大值: {np.max(gray_img)}")
        print(f"   平均值: {np.mean(gray_img):.1f}")
        print(f"   中位数: {np.median(gray_img):.1f}")
        
        # 验证判断
        print(f"\n⚠️ 验证检查:")
        warnings = []
        
        if width < 50 or height < 50:
            warnings.append("❌ 分辨率过低")
        else:
            print("✅ 分辨率正常")
            
        if len(img_array.shape) == 3:
            if color_variance > 100:
                warnings.append("❌ 明显彩色图像")
            else:
                print("✅ 颜色检查通过")
        
        if unique_values < 10:
            warnings.append("❌ 颜色过于单调")
        else:
            print("✅ 颜色丰富度正常")
            
        if max_ratio > 0.5:
            warnings.append("❌ 亮度过于集中")
        else:
            print("✅ 亮度分布正常")
            
        if contrast < 10:
            warnings.append("❌ 对比度过低")
        else:
            print("✅ 对比度正常")
            
        if edge_ratio < 0.02:
            warnings.append("❌ 细节不足")
        elif edge_ratio > 0.4:
            warnings.append("❌ 过于复杂")
        else:
            print("✅ 边缘细节正常")
        
        # 文件名检查
        filename = os.path.basename(image_path).lower()
        non_medical_keywords = ['photo', 'selfie', 'portrait', '风景', '人物', 'landscape', 'nature', '自拍']
        for keyword in non_medical_keywords:
            if keyword in filename:
                warnings.append(f"❌ 文件名包含'{keyword}'")
                break
        else:
            print("✅ 文件名检查通过")
        
        print(f"\n🎯 总结:")
        print(f"警告数量: {len(warnings)}")
        if warnings:
            print("警告列表:")
            for warning in warnings:
                print(f"  {warning}")
        
        if len(warnings) >= 3:
            print("🔴 验证结果: 拒绝分析")
        elif len(warnings) >= 1:
            print("🟡 验证结果: 存在警告")
        else:
            print("🟢 验证结果: 通过验证")
            
        return {
            'warnings': warnings,
            'properties': {
                'size': f"{width}x{height}",
                'channels': len(img_array.shape),
                'color_variance': color_variance if len(img_array.shape) == 3 else 0,
                'unique_colors': unique_values,
                'max_brightness_ratio': max_ratio,
                'contrast': contrast,
                'edge_ratio': edge_ratio
            }
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_multiple_images():
    """测试多个图像"""
    print("🧪 图像验证测试工具")
    print("=" * 60)
    
    # 提示用户输入图像路径
    print("\n请输入要测试的图像文件路径（可以输入多个，用逗号分隔）:")
    print("例如: D:/图片/ct1.jpg, D:/图片/风景.jpg")
    
    paths_input = input("图像路径: ").strip()
    
    if not paths_input:
        print("❌ 未输入路径")
        return
    
    # 分割路径
    image_paths = [path.strip().strip('"\'') for path in paths_input.split(',')]
    
    results = []
    for path in image_paths:
        if os.path.exists(path):
            result = analyze_image_properties(path)
            if result:
                results.append((path, result))
        else:
            print(f"❌ 文件不存在: {path}")
    
    # 对比分析
    if len(results) > 1:
        print("\n" + "=" * 60)
        print("📊 对比分析")
        print("=" * 60)
        
        for path, result in results:
            name = os.path.basename(path)
            props = result['properties']
            warnings_count = len(result['warnings'])
            
            print(f"\n📁 {name}:")
            print(f"   尺寸: {props['size']}")
            print(f"   颜色方差: {props['color_variance']:.1f}")
            print(f"   对比度: {props['contrast']:.1f}")
            print(f"   边缘比例: {props['edge_ratio']:.3f}")
            print(f"   警告数: {warnings_count}")

if __name__ == "__main__":
    test_multiple_images()
