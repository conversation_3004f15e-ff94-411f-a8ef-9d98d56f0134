# 📹 摄像头功能使用说明

## 🎯 功能概述

双模型系统现在支持完整的摄像头功能，包括实时预览、拍照和分析。

## 🚀 使用步骤

### 1. 启动摄像头
- 点击 **"📹 启动摄像头"** 按钮
- 系统会自动检测并启动默认摄像头
- 按钮变为红色 **"📹 关闭摄像头"**
- 左侧面板开始显示实时摄像头画面

### 2. 实时预览
- 摄像头启动后，左侧会显示实时画面
- 画面会以30fps的频率更新
- 分辨率自动调整为400x400像素

### 3. 拍照功能
- 摄像头启动后，**"📸 拍照分析"** 按钮变为可用
- 点击拍照按钮进行拍摄
- 拍照后：
  - 摄像头预览停止
  - 界面保持拍照时的画面
  - 图像自动保存到临时文件夹
  - **"🔍 开始AI分析"** 按钮变为可用

### 4. 分析拍摄的图像
- 拍照后点击 **"🔍 开始AI分析"**
- 系统会对拍摄的图像进行双模型分析：
  - CT图像检测
  - 痴呆症分析

## 🎨 界面状态

### 摄像头关闭状态
- 📹 启动摄像头 (绿色)
- 📸 拍照分析 (禁用)

### 摄像头运行状态
- 📹 关闭摄像头 (红色)
- 📸 拍照分析 (可用，紫色)

### 拍照完成状态
- 📹 启动摄像头 (绿色)
- 📸 拍照分析 (禁用)
- 🔍 开始AI分析 (可用)

## 🔧 技术特性

### 摄像头管理
- 自动检测系统默认摄像头
- 安全的摄像头资源管理
- 应用关闭时自动释放摄像头

### 图像处理
- 实时颜色格式转换 (BGR → RGB)
- 自动尺寸调整
- 高质量图像保存

### 文件管理
- 拍照图像保存到系统临时文件夹
- 文件名包含时间戳，避免冲突
- 格式：`camera_capture_YYYYMMDD_HHMMSS.jpg`

## ⚠️ 注意事项

### 摄像头权限
- 确保应用有摄像头访问权限
- 如果摄像头被其他应用占用，可能无法启动

### 性能优化
- 摄像头预览以30fps运行，确保流畅体验
- 拍照后自动停止预览，节省系统资源

### 错误处理
- 摄像头启动失败会显示错误提示
- 拍照失败会自动恢复摄像头状态
- 应用关闭时确保摄像头正确释放

## 🎯 使用场景

### 医学影像拍摄
1. 启动摄像头
2. 对准CT扫描图像或其他医学影像
3. 调整角度和距离
4. 点击拍照
5. 开始AI分析

### 实时预览
- 可以实时查看摄像头画面
- 调整拍摄角度和位置
- 确保图像质量后再拍照

## 🔄 工作流程

```
启动应用 → 点击"启动摄像头" → 实时预览
    ↓
调整拍摄角度 → 点击"拍照分析" → 保持拍照画面
    ↓
点击"开始AI分析" → CT检测 + 痴呆症分析
    ↓
查看结果 → 生成报告 → 重新启动摄像头(手动)
```

## 📱 快捷操作

- **Space键**: 可以考虑添加快捷键拍照功能
- **Esc键**: 可以考虑添加快捷键关闭摄像头
- **Enter键**: 可以考虑添加快捷键开始分析

---

现在你的双模型系统具备了完整的摄像头功能！可以实时预览、拍照并保持拍照时的界面，然后进行AI分析。
