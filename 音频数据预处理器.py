"""
Audio Data Preprocessor for Dementia Detection
音频数据预处理器 - 痴呆症检测模型
目标: 创建科学合理的训练集、验证集、测试集
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.preprocessing import StandardScaler, LabelEncoder
import os
import json
import warnings
warnings.filterwarnings('ignore')

class AudioDataPreprocessor:
    def __init__(self, audio_path=r"D:\模型开发\audio"):
        self.audio_path = audio_path
        self.datasets = {}
        self.combined_data = None
        self.feature_info = {}
        self.splits = {}
        
    def load_datasets(self):
        """加载所有CSV数据集"""
        
        print("🔍 加载音频数据集...")
        
        csv_files = {
            'acoustic': 'acoustic_features_dataset.csv',
            'cookie_theft': 'cookie_theft_dataset.csv', 
            'semantic_fluency': 'semantic_fluency_dataset.csv'
        }
        
        for name, filename in csv_files.items():
            file_path = os.path.join(self.audio_path, filename)
            if os.path.exists(file_path):
                df = pd.read_csv(file_path)
                self.datasets[name] = df
                print(f"✅ 加载 {name}: {df.shape[0]} 样本, {df.shape[1]} 特征")
                print(f"   诊断分布: {df['diagnosis_name'].value_counts().to_dict()}")
            else:
                print(f"❌ 未找到文件: {filename}")
                
        return len(self.datasets) > 0
    
    def merge_datasets(self):
        """合并三个数据集，创建37维特征向量"""
        
        print("\n🔗 合并数据集创建37维特征向量...")
        
        if len(self.datasets) < 3:
            raise ValueError("需要三个完整的数据集")
        
        # 找到共同的subject_id
        common_subjects = None
        for name, df in self.datasets.items():
            subjects = set(df['subject_id'])
            if common_subjects is None:
                common_subjects = subjects
            else:
                common_subjects = common_subjects.intersection(subjects)
        
        print(f"   找到 {len(common_subjects)} 个共同样本")
        
        # 定义特征分组
        self.feature_info = {
            'demographic': ['age', 'gender'],  # 2维 (education_years从cookie_theft获取)
            'acoustic': ['f0_mean', 'f0_std', 'f0_range', 'jitter_percent',
                        'shimmer_percent', 'hnr_db', 'spectral_centroid',
                        'spectral_bandwidth', 'spectral_rolloff', 'zero_crossing_rate'],  # 10维
            'mfcc': [f'mfcc_{i}' for i in range(1, 14)],  # 13维
            'linguistic': ['education_years', 'speech_rate_wpm', 'pause_frequency', 'pause_duration_mean',
                          'total_words', 'unique_words', 'type_token_ratio',
                          'information_units', 'efficiency_ratio', 'coherence_score',
                          'filled_pauses', 'repetitions', 'semantic_errors']  # 13维
        }
        
        # 开始合并
        combined_df = None
        
        # 1. 从acoustic数据集获取基础信息和声学特征
        acoustic_df = self.datasets['acoustic']
        acoustic_df = acoustic_df[acoustic_df['subject_id'].isin(common_subjects)].copy()
        
        # 选择需要的列
        acoustic_cols = (['subject_id', 'diagnosis', 'diagnosis_name'] + 
                        self.feature_info['demographic'] + 
                        self.feature_info['acoustic'] + 
                        self.feature_info['mfcc'])
        
        combined_df = acoustic_df[acoustic_cols].copy()
        
        # 2. 从cookie_theft数据集获取语言特征
        cookie_df = self.datasets['cookie_theft']
        cookie_df = cookie_df[cookie_df['subject_id'].isin(common_subjects)].copy()
        
        # 选择语言特征 (排除重复的demographic特征)
        linguistic_cols = ['subject_id'] + self.feature_info['linguistic']
        cookie_features = cookie_df[linguistic_cols]
        
        # 合并
        combined_df = combined_df.merge(cookie_features, on='subject_id', how='inner')
        
        # 3. 验证数据完整性
        print(f"   合并后样本数: {len(combined_df)}")
        print(f"   特征总数: {len(combined_df.columns) - 3}")  # 减去subject_id, diagnosis, diagnosis_name
        
        # 4. 检查缺失值
        missing_info = combined_df.isnull().sum()
        if missing_info.sum() > 0:
            print("   ⚠️ 发现缺失值:")
            for col, missing_count in missing_info[missing_info > 0].items():
                print(f"     {col}: {missing_count} 个缺失值")
        
        # 5. 数据类型转换
        # 确保gender是数值型
        if combined_df['gender'].dtype == 'object':
            combined_df['gender'] = combined_df['gender'].map({'M': 1, 'F': 0, 'Male': 1, 'Female': 0, 1: 1, 0: 0})
        
        self.combined_data = combined_df
        
        # 6. 特征统计
        feature_count = {
            'demographic': len(self.feature_info['demographic']),
            'acoustic': len(self.feature_info['acoustic']),
            'mfcc': len(self.feature_info['mfcc']),
            'linguistic': len([col for col in self.feature_info['linguistic'] if col in combined_df.columns])
        }
        
        total_features = sum(feature_count.values())
        print(f"   ✅ 特征分布:")
        for category, count in feature_count.items():
            print(f"     {category}: {count} 维")
        print(f"   总特征维度: {total_features}")
        
        return combined_df
    
    def create_train_val_test_splits(self):
        """创建训练集、验证集、测试集"""
        
        print("\n📊 创建训练集、验证集、测试集...")
        
        if self.combined_data is None:
            raise ValueError("请先合并数据集")
        
        # 准备特征和标签
        feature_cols = []
        for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
            for feature in self.feature_info[category]:
                if feature in self.combined_data.columns:
                    feature_cols.append(feature)
        
        X = self.combined_data[feature_cols]
        y = self.combined_data['diagnosis_name']
        
        # 编码标签
        le = LabelEncoder()
        y_encoded = le.fit_transform(y)
        
        print(f"   特征矩阵: {X.shape}")
        print(f"   标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        # 分层抽样: 70% 训练, 15% 验证, 15% 测试
        X_temp, X_test, y_temp, y_test = train_test_split(
            X, y_encoded, test_size=0.15, random_state=42, stratify=y_encoded
        )
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp, test_size=0.176, random_state=42, stratify=y_temp  # 0.176 * 0.85 ≈ 0.15
        )
        
        # 特征标准化
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        X_test_scaled = scaler.transform(X_test)
        
        # 保存分割结果
        self.splits = {
            'train': {
                'X': X_train_scaled,
                'X_raw': X_train,
                'y': y_train,
                'y_names': le.inverse_transform(y_train),
                'size': len(X_train)
            },
            'validation': {
                'X': X_val_scaled,
                'X_raw': X_val,
                'y': y_val,
                'y_names': le.inverse_transform(y_val),
                'size': len(X_val)
            },
            'test': {
                'X': X_test_scaled,
                'X_raw': X_test,
                'y': y_test,
                'y_names': le.inverse_transform(y_test),
                'size': len(X_test)
            },
            'feature_names': feature_cols,
            'label_encoder': le,
            'scaler': scaler,
            'class_names': list(le.classes_)
        }
        
        print(f"   ✅ 数据分割完成:")
        print(f"     训练集: {len(X_train)} 样本 ({len(X_train)/len(X)*100:.1f}%)")
        print(f"     验证集: {len(X_val)} 样本 ({len(X_val)/len(X)*100:.1f}%)")
        print(f"     测试集: {len(X_test)} 样本 ({len(X_test)/len(X)*100:.1f}%)")
        
        # 检查类别平衡
        for split_name, split_data in self.splits.items():
            if split_name in ['train', 'validation', 'test']:
                class_dist = pd.Series(split_data['y_names']).value_counts()
                print(f"     {split_name} 类别分布: {class_dist.to_dict()}")
        
        return self.splits

    def save_processed_datasets(self):
        """保存处理后的数据集到文件"""

        print("\n💾 保存处理后的数据集...")

        if not self.splits:
            raise ValueError("请先创建数据分割")

        # 创建输出目录
        output_dir = os.path.join(self.audio_path, "processed_datasets")
        os.makedirs(output_dir, exist_ok=True)

        # 保存训练集、验证集、测试集
        for split_name in ['train', 'validation', 'test']:
            split_data = self.splits[split_name]

            # 创建DataFrame
            df = pd.DataFrame(split_data['X_raw'], columns=self.splits['feature_names'])
            df['diagnosis_encoded'] = split_data['y']
            df['diagnosis_name'] = split_data['y_names']

            # 保存原始数据
            raw_path = os.path.join(output_dir, f"{split_name}_set_raw.csv")
            df.to_csv(raw_path, index=False)

            # 保存标准化数据
            df_scaled = pd.DataFrame(split_data['X'], columns=self.splits['feature_names'])
            df_scaled['diagnosis_encoded'] = split_data['y']
            df_scaled['diagnosis_name'] = split_data['y_names']

            scaled_path = os.path.join(output_dir, f"{split_name}_set_scaled.csv")
            df_scaled.to_csv(scaled_path, index=False)

            print(f"   ✅ 保存 {split_name} 集: {len(df)} 样本")

        # 保存特征信息
        feature_info_path = os.path.join(output_dir, "feature_info.json")
        with open(feature_info_path, 'w', encoding='utf-8') as f:
            json.dump(self.feature_info, f, indent=2, ensure_ascii=False)

        # 保存标准化器和编码器
        import pickle
        scaler_path = os.path.join(output_dir, "scaler.pkl")
        with open(scaler_path, 'wb') as f:
            pickle.dump(self.splits['scaler'], f)

        encoder_path = os.path.join(output_dir, "label_encoder.pkl")
        with open(encoder_path, 'wb') as f:
            pickle.dump(self.splits['label_encoder'], f)

        print(f"   📁 所有文件保存到: {output_dir}")
        return output_dir

    def generate_html_report(self, output_dir):
        """生成HTML清单报告"""

        print("\n📋 生成HTML清单报告...")

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频数据预处理报告 - 痴呆症检测模型</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 15px; }}
        h3 {{ color: #7f8c8d; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .feature-group {{ background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }}
        .dataset-info {{ display: flex; justify-content: space-around; flex-wrap: wrap; }}
        .dataset-card {{ background: #ffffff; border: 2px solid #3498db; border-radius: 8px; padding: 20px; margin: 10px; min-width: 250px; text-align: center; }}
        .metric {{ font-size: 24px; font-weight: bold; color: #2980b9; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #bdc3c7; padding: 12px; text-align: left; }}
        th {{ background-color: #3498db; color: white; }}
        tr:nth-child(even) {{ background-color: #f2f2f2; }}
        .file-list {{ background: #e8f6f3; padding: 15px; border-radius: 5px; }}
        .success {{ color: #27ae60; font-weight: bold; }}
        .warning {{ color: #f39c12; font-weight: bold; }}
        .info {{ color: #3498db; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 音频数据预处理报告</h1>
        <p class="info" style="text-align: center;">痴呆症检测模型 - 数据预处理完成报告</p>

        <div class="summary">
            <h2>📊 数据集概览</h2>
            <div class="dataset-info">
                <div class="dataset-card">
                    <h3>总样本数</h3>
                    <div class="metric">{len(self.combined_data):,}</div>
                </div>
                <div class="dataset-card">
                    <h3>特征维度</h3>
                    <div class="metric">{len(self.splits['feature_names'])}</div>
                </div>
                <div class="dataset-card">
                    <h3>类别数</h3>
                    <div class="metric">{len(self.splits['class_names'])}</div>
                </div>
            </div>
        </div>

        <h2>🎯 特征组成分析</h2>
        """

        # 特征组成表格
        html_content += """
        <table>
            <tr><th>特征类别</th><th>维度</th><th>特征列表</th></tr>
        """

        for category, features in self.feature_info.items():
            available_features = [f for f in features if f in self.splits['feature_names']]
            html_content += f"""
            <tr>
                <td><strong>{category}</strong></td>
                <td>{len(available_features)}</td>
                <td>{', '.join(available_features)}</td>
            </tr>
            """

        html_content += "</table>"

        # 数据分割信息
        html_content += """
        <h2>📈 数据分割详情</h2>
        <table>
            <tr><th>数据集</th><th>样本数</th><th>比例</th><th>Normal</th><th>MCI</th><th>Dementia</th></tr>
        """

        total_samples = len(self.combined_data)
        for split_name in ['train', 'validation', 'test']:
            split_data = self.splits[split_name]
            class_counts = pd.Series(split_data['y_names']).value_counts()

            html_content += f"""
            <tr>
                <td><strong>{split_name.title()}</strong></td>
                <td>{split_data['size']:,}</td>
                <td>{split_data['size']/total_samples*100:.1f}%</td>
                <td>{class_counts.get('Normal', 0)}</td>
                <td>{class_counts.get('MCI', 0)}</td>
                <td>{class_counts.get('Dementia', 0)}</td>
            </tr>
            """

        html_content += "</table>"

        # 文件清单
        html_content += f"""
        <h2>📁 生成文件清单</h2>
        <div class="file-list">
            <h3>数据文件:</h3>
            <ul>
                <li><strong>train_set_raw.csv</strong> - 训练集原始数据 ({self.splits['train']['size']} 样本)</li>
                <li><strong>train_set_scaled.csv</strong> - 训练集标准化数据</li>
                <li><strong>validation_set_raw.csv</strong> - 验证集原始数据 ({self.splits['validation']['size']} 样本)</li>
                <li><strong>validation_set_scaled.csv</strong> - 验证集标准化数据</li>
                <li><strong>test_set_raw.csv</strong> - 测试集原始数据 ({self.splits['test']['size']} 样本)</li>
                <li><strong>test_set_scaled.csv</strong> - 测试集标准化数据</li>
            </ul>

            <h3>配置文件:</h3>
            <ul>
                <li><strong>feature_info.json</strong> - 特征分组信息</li>
                <li><strong>scaler.pkl</strong> - 标准化器 (用于新数据预处理)</li>
                <li><strong>label_encoder.pkl</strong> - 标签编码器</li>
                <li><strong>data_preprocessing_report.html</strong> - 本报告文件</li>
            </ul>
        </div>

        <h2>✅ 预处理完成状态</h2>
        <div class="summary">
            <p class="success">✅ 数据加载完成 - 3个CSV文件成功合并</p>
            <p class="success">✅ 特征工程完成 - {len(self.splits['feature_names'])}维特征向量构建</p>
            <p class="success">✅ 数据分割完成 - 训练/验证/测试集科学划分</p>
            <p class="success">✅ 数据标准化完成 - 特征缩放和标签编码</p>
            <p class="success">✅ 文件保存完成 - 所有数据集和配置文件已保存</p>
        </div>

        <h2>🚀 下一步操作建议</h2>
        <div class="feature-group">
            <ol>
                <li><strong>模型训练</strong>: 使用 train_set_scaled.csv 训练分类模型</li>
                <li><strong>模型验证</strong>: 使用 validation_set_scaled.csv 进行超参数调优</li>
                <li><strong>模型测试</strong>: 使用 test_set_scaled.csv 进行最终性能评估</li>
                <li><strong>特征分析</strong>: 分析各特征组的重要性和贡献度</li>
                <li><strong>模型部署</strong>: 集成 scaler.pkl 和 label_encoder.pkl 用于新数据预测</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #7f8c8d;">
            <p>报告生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>数据路径: {output_dir}</p>
        </div>
    </div>
</body>
</html>
        """

        # 保存HTML报告
        html_path = os.path.join(output_dir, "data_preprocessing_report.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"   ✅ HTML报告已生成: {html_path}")
        return html_path

    def run_complete_preprocessing(self):
        """运行完整的数据预处理流程"""

        print("🚀 开始音频数据预处理...")
        print("=" * 60)

        try:
            # 1. 加载数据集
            if not self.load_datasets():
                raise ValueError("数据集加载失败")

            # 2. 合并数据集
            combined_data = self.merge_datasets()
            if combined_data is None:
                raise ValueError("数据集合并失败")

            # 3. 创建训练/验证/测试集
            splits = self.create_train_val_test_splits()
            if not splits:
                raise ValueError("数据分割失败")

            # 4. 保存处理后的数据
            output_dir = self.save_processed_datasets()

            # 5. 生成HTML报告
            html_path = self.generate_html_report(output_dir)

            print("\n" + "=" * 60)
            print("✅ 数据预处理完成!")
            print(f"📁 输出目录: {output_dir}")
            print(f"📋 HTML报告: {html_path}")
            print("🚀 可以开始模型训练了!")

            return {
                'output_dir': output_dir,
                'html_report': html_path,
                'splits': self.splits,
                'feature_info': self.feature_info,
                'combined_data': self.combined_data
            }

        except Exception as e:
            print(f"❌ 预处理失败: {str(e)}")
            return None

if __name__ == "__main__":
    print("🎵 音频数据预处理器 - 痴呆症检测模型")
    print("=" * 60)

    # 创建预处理器
    preprocessor = AudioDataPreprocessor()

    # 运行完整预处理流程
    result = preprocessor.run_complete_preprocessing()

    if result:
        print(f"\n🎯 预处理结果摘要:")
        print(f"   总样本数: {len(result['combined_data']):,}")
        print(f"   特征维度: {len(result['splits']['feature_names'])}")
        print(f"   训练集: {result['splits']['train']['size']} 样本")
        print(f"   验证集: {result['splits']['validation']['size']} 样本")
        print(f"   测试集: {result['splits']['test']['size']} 样本")
        print(f"   类别: {result['splits']['class_names']}")

        print(f"\n📋 查看详细报告: {result['html_report']}")
    else:
        print("\n❌ 预处理失败，请检查数据文件")
