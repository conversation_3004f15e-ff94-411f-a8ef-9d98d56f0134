"""
📊 超简单 EEG数据集划分器
直接使用已解压的数据，快速生成训练集和验证集
"""

import os
import shutil
from sklearn.model_selection import train_test_split

# ==========================================
# 🔧 配置 - 基于您的实际路径
# ==========================================

# 您的已解压数据路径
DATA_PATH = "EEG_extracted/dataset"  # 您提到的解压路径

# 输出路径
OUTPUT_PATH = "EEG_splits"  # 输出到当前目录下的EEG_splits文件夹

# 划分比例
TRAIN_RATIO = 0.70  # 70%训练
VAL_RATIO = 0.15    # 15%验证  
TEST_RATIO = 0.15   # 15%测试

# ==========================================

def load_patient_labels():
    """加载患者标签"""
    print("📋 加载患者标签...")
    
    labels_file = os.path.join(DATA_PATH, "participants.tsv")
    patient_labels = {}
    
    with open(labels_file, 'r') as f:
        lines = f.read().strip().split('\n')
        for line in lines[1:]:  # 跳过表头
            if line.strip():
                parts = line.split('\t')
                subject_id = parts[0].strip()  # sub-001
                label = parts[1].strip()       # A/C/F
                patient_labels[subject_id] = label
    
    print(f"✅ 加载了 {len(patient_labels)} 个患者")
    return patient_labels

def find_eeg_files(patient_labels):
    """查找EEG文件"""
    print("🔍 查找EEG文件...")
    
    eeg_files = {}
    for subject_id in patient_labels.keys():
        eeg_dir = os.path.join(DATA_PATH, subject_id, "eeg")
        if os.path.exists(eeg_dir):
            set_files = [f for f in os.listdir(eeg_dir) if f.endswith('.set')]
            if set_files:
                eeg_files[subject_id] = os.path.join(eeg_dir, set_files[0])
    
    print(f"✅ 找到 {len(eeg_files)} 个EEG文件")
    return eeg_files

def split_data(patient_labels, eeg_files):
    """划分数据"""
    print("🔄 划分数据...")
    
    # 只使用有EEG文件的患者
    valid_subjects = [sid for sid in patient_labels.keys() if sid in eeg_files]
    valid_labels = [patient_labels[sid] for sid in valid_subjects]
    
    # 第一次划分：分出测试集
    train_val_ids, test_ids, train_val_labels, test_labels = train_test_split(
        valid_subjects, valid_labels, 
        test_size=TEST_RATIO, 
        random_state=42, 
        stratify=valid_labels
    )
    
    # 第二次划分：分出验证集
    val_ratio_adj = VAL_RATIO / (TRAIN_RATIO + VAL_RATIO)
    train_ids, val_ids, train_labels, val_labels = train_test_split(
        train_val_ids, train_val_labels,
        test_size=val_ratio_adj,
        random_state=42,
        stratify=train_val_labels
    )
    
    splits = {
        'train': {'ids': train_ids, 'labels': train_labels},
        'val': {'ids': val_ids, 'labels': val_labels},
        'test': {'ids': test_ids, 'labels': test_labels}
    }
    
    # 显示结果
    print("✅ 划分完成:")
    for split_name, split_data in splits.items():
        print(f"   {split_name}: {len(split_data['ids'])} 人")
    
    return splits

def copy_files(splits, eeg_files):
    """复制文件到对应目录"""
    print("📁 复制文件...")
    
    # 创建输出目录
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    for split_name, split_data in splits.items():
        # 创建子目录
        split_dir = os.path.join(OUTPUT_PATH, split_name)
        os.makedirs(split_dir, exist_ok=True)
        
        print(f"🔄 复制 {split_name} 集...")
        
        # 复制文件
        for subject_id in split_data['ids']:
            if subject_id in eeg_files:
                source_file = eeg_files[subject_id]
                filename = os.path.basename(source_file)
                target_file = os.path.join(split_dir, filename)
                shutil.copy2(source_file, target_file)
        
        # 保存标签文件
        label_file = os.path.join(split_dir, "labels.txt")
        with open(label_file, 'w') as f:
            f.write("subject_id\tlabel\n")
            for i, subject_id in enumerate(split_data['ids']):
                label = split_data['labels'][i]
                f.write(f"{subject_id}\t{label}\n")
        
        print(f"✅ {split_name}: {len(split_data['ids'])} 个文件")

def save_summary(splits):
    """保存总结"""
    summary_file = os.path.join(OUTPUT_PATH, "summary.txt")
    
    with open(summary_file, 'w') as f:
        f.write("EEG数据集划分总结\n")
        f.write("=" * 30 + "\n\n")
        
        f.write(f"数据路径: {DATA_PATH}\n")
        f.write(f"输出路径: {OUTPUT_PATH}\n\n")
        
        f.write("划分结果:\n")
        for split_name, split_data in splits.items():
            f.write(f"  {split_name}: {len(split_data['ids'])} 人\n")
            
            # 统计各类别
            from collections import Counter
            label_counts = Counter(split_data['labels'])
            for label, count in sorted(label_counts.items()):
                f.write(f"    {label}: {count} 人\n")
            f.write("\n")
    
    print(f"📄 总结已保存: {summary_file}")

def main():
    """主函数"""
    print("🚀 开始EEG数据划分")
    print("=" * 40)
    
    # 1. 加载标签
    patient_labels = load_patient_labels()
    
    # 2. 查找文件
    eeg_files = find_eeg_files(patient_labels)
    
    # 3. 划分数据
    splits = split_data(patient_labels, eeg_files)
    
    # 4. 复制文件
    copy_files(splits, eeg_files)
    
    # 5. 保存总结
    save_summary(splits)
    
    print("\n🎉 完成!")
    print(f"📁 输出目录: {OUTPUT_PATH}")
    print("📋 包含: train/, val/, test/ 目录")
    print("📄 查看 summary.txt 了解详情")

if __name__ == "__main__":
    main()
