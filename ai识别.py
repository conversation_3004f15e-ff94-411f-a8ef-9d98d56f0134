import os
import warnings

# 设置环境变量来隐藏TensorFlow的日志信息（必须在导入TensorFlow之前设置）
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 隐藏INFO和WARNING信息
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # 关闭oneDNN优化信息

# 隐藏其他警告信息
warnings.filterwarnings('ignore')

# 现在导入TensorFlow
import tensorflow as tf
from tensorflow.keras.preprocessing import image
import numpy as np
import matplotlib.pyplot as plt

# 设置TensorFlow日志级别
tf.get_logger().setLevel('ERROR')

# 隐藏absl警告
import absl.logging
absl.logging.set_verbosity(absl.logging.ERROR)

# 加载模型
model = tf.keras.models.load_model("D:\模型开发\升级model.h5")

# 更新类别标签映射
class_labels = ['MildDemented(轻度痴呆)', 'ModerateDemented(中度痴呆)', 'NonDemented(无痴呆)', 'VeryMildDemented(非常轻度痴呆)']


# 图像预处理函数
def load_and_preprocess_image(img_path):
    img = image.load_img(img_path, target_size=(150, 150))  # 根据模型的输入大小调整
    img_array = image.img_to_array(img)  # 将图片转化为数组
    img_array = np.expand_dims(img_array, axis=0)  # 扩展维度，以便批量输入
    img_array = img_array / 255.0  # 归一化像素值到 [0, 1]
    return img_array, img  # 返回处理后的图像和原始图像

# 加载并预处理输入图像
img_path = "D:/模型开发/1.jpg"  # 你的图片路径
processed_image, original_image = load_and_preprocess_image(img_path)

# 使用模型进行预测
predictions = model.predict(processed_image)
predicted_class = np.argmax(predictions, axis=1)  # 获取类别索引

# 获取每个类别的预测概率
prediction_probs = predictions[0].tolist()

# 显示预测类别名称
predicted_class_name = class_labels[predicted_class[0]]

# 显示预测结果及概率
print(f"Predicted class: {predicted_class_name}")
print("Prediction probabilities for each class:")
for i, prob in enumerate(prediction_probs):
    print(f"{class_labels[i]}: {prob:.4f}")

# 显示预测的置信度（最大概率）
confidence = max(prediction_probs)
print(f"Confidence in predicted class: {confidence:.4f}")

