# 📍 EEG数据集划分器 - 路径配置说明

## 🔧 需要修改的路径配置

在运行 `eeg_data_splitter.py` 之前，请根据您的实际情况修改以下路径：

### 1. 数据集路径配置

```python
# 在 eeg_data_splitter.py 第 18-19 行
self.zip_path = "D:/模型开发/EEG.zip"  # 📍 修改为您的EEG数据集ZIP文件路径
self.extract_path = "D:/模型开发/EEG_extracted"  # 📍 修改为您希望解压到的目录
```

**修改说明:**
- `self.zip_path`: 您的EEG.zip文件的完整路径
- `self.extract_path`: 临时解压目录，脚本会自动创建

**示例修改:**
```python
# 如果您的数据集在其他位置，请修改为:
self.zip_path = "C:/Users/<USER>/Documents/EEG.zip"
self.extract_path = "C:/Users/<USER>/Documents/EEG_temp"
```

### 2. 输出路径配置

```python
# 在 eeg_data_splitter.py 第 22 行
self.output_base_path = "D:/模型开发/EEG_splits"  # 📍 修改为您希望保存划分结果的目录
```

**修改说明:**
- `self.output_base_path`: 划分后的训练集、验证集、测试集保存目录
- 脚本会在此目录下创建 `train/`, `val/`, `test/` 子目录

**示例修改:**
```python
# 如果您希望保存到其他位置，请修改为:
self.output_base_path = "C:/Users/<USER>/Documents/EEG_Training_Data"
```

### 3. 划分比例配置 (可选)

```python
# 在 eeg_data_splitter.py 第 24-26 行
self.train_ratio = 0.70  # 训练集比例 (70%)
self.val_ratio = 0.15    # 验证集比例 (15%)
self.test_ratio = 0.15   # 测试集比例 (15%)
```

**说明:**
- 当前配置针对双模型联用优化
- 如需调整比例，请确保三个比例之和等于 1.0

## 📁 输出目录结构

运行脚本后，会在 `output_base_path` 下创建以下结构：

```
EEG_splits/                    # 您配置的输出基础目录
├── train/                     # 训练集目录 (61个.set文件)
│   ├── sub-001_task-eyesclosed_eeg.set
│   ├── sub-002_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt            # 训练集标签文件
├── val/                      # 验证集目录 (12个.set文件)
│   ├── sub-037_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt            # 验证集标签文件
├── test/                     # 测试集目录 (15个.set文件)
│   ├── sub-066_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt            # 测试集标签文件
├── split_metadata.json       # 完整的划分元数据
└── split_summary.txt         # 划分总结报告
```

## 🚀 使用步骤

### 步骤1: 修改路径配置
1. 打开 `eeg_data_splitter.py`
2. 找到第 18-22 行的配置部分
3. 根据您的实际路径修改这些变量

### 步骤2: 运行脚本
```bash
python eeg_data_splitter.py
```

### 步骤3: 检查输出
脚本运行完成后，检查：
1. 输出目录是否创建成功
2. 三个子目录 (train/val/test) 是否包含正确数量的文件
3. 查看 `split_summary.txt` 了解详细划分结果

## 📊 预期输出结果

基于88个患者的优化划分：

| 数据集 | 患者数量 | A组(AD) | C组(健康) | F组(FTD) |
|--------|----------|---------|-----------|----------|
| 训练集 | 61人 (69.3%) | 25人 | 20人 | 16人 |
| 验证集 | 12人 (13.6%) | 5人 | 4人 | 3人 |
| 测试集 | 15人 (17.0%) | 6人 | 5人 | 4人 |

## ⚠️ 注意事项

1. **磁盘空间**: 确保输出目录有足够空间 (约5GB)
2. **路径格式**: Windows路径使用正斜杠 `/` 或双反斜杠 `\\`
3. **权限**: 确保对输出目录有写入权限
4. **备份**: 建议先备份原始数据集

## 🔍 故障排除

### 问题1: 文件不存在错误
```
❌ 错误: 数据集文件不存在
```
**解决方案**: 检查 `self.zip_path` 路径是否正确

### 问题2: 权限错误
```
❌ 解压失败: Permission denied
```
**解决方案**: 
- 检查输出目录权限
- 尝试以管理员身份运行

### 问题3: 磁盘空间不足
```
❌ 复制失败: No space left on device
```
**解决方案**: 
- 清理磁盘空间
- 更改输出路径到空间充足的磁盘

## 📞 下一步

数据划分完成后，您可以：

1. **检查划分结果**: 查看 `split_summary.txt`
2. **开始模型训练**: 使用划分好的数据训练EEG模型
3. **集成双模型**: 将训练好的EEG模型与现有MRI双模型系统集成

## 📋 快速配置模板

复制以下代码到 `eeg_data_splitter.py` 的配置部分：

```python
# ==========================================
# 🔧 配置部分 - 请根据您的实际路径修改
# ==========================================

# 1. 数据集路径配置
self.zip_path = "您的EEG.zip文件路径"  # 📍 修改这里
self.extract_path = "您的临时解压目录"  # 📍 修改这里

# 2. 输出路径配置  
self.output_base_path = "您的输出目录"  # 📍 修改这里

# 3. 划分比例配置 (针对双模型联用优化)
self.train_ratio = 0.70  # 训练集比例
self.val_ratio = 0.15    # 验证集比例  
self.test_ratio = 0.15   # 测试集比例
```

---

💡 **提示**: 修改完路径后，建议先在小范围测试，确认脚本能正常运行后再处理完整数据集。
