# -*- coding: utf-8 -*-
"""
双模型联用系统 - GUI界面模块
完整的图形用户界面，整合所有功能
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import json
from datetime import datetime
import cv2
import numpy as np
import warnings

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# 抑制警告
warnings.filterwarnings('ignore')

# 设置CustomTkinter主题
ctk.set_appearance_mode("dark")  # 深色主题
ctk.set_default_color_theme("blue")  # 蓝色主题

class DualModelAIDetectionApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🧠 AI痴呆症识别器 - 双模型联用版 v2.0")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 设置窗口图标
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # 初始化变量
        self.ct_detection_model = None
        self.ct_classification_model = None
        self.current_image_path = None
        self.results_history = []
        self.camera = None
        self.camera_active = False
        
        # 模型路径
        self.ct_detection_path = r"D:\模型开发\ct_other_model.h5"  # CT图像识别
        self.ct_classification_path = r"D:\模型开发\ct_class.h5"   # 症状分析
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 创建界面
        self.create_widgets()
        self.load_models_async()
        
    def create_widgets(self):
        """创建主界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="🧠 AI痴呆症识别器 - 双模型联用版",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.title_label.pack(pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="CT图像验证 + 症状智能分析 | 基于深度学习的医学影像智能分析系统",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        self.subtitle_label.pack(pady=(0, 20))
        
        # 创建左右分栏
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图像显示
        self.left_panel = ctk.CTkFrame(self.content_frame)
        self.left_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        # 右侧面板 - 控制和结果
        self.right_panel = ctk.CTkFrame(self.content_frame)
        self.right_panel.pack(side="right", fill="y", padx=(10, 0))
        self.right_panel.configure(width=400)
        
        self.create_left_panel()
        self.create_right_panel()
        
        # 底部状态栏
        self.create_status_bar()
        
    def create_left_panel(self):
        """创建左侧图像显示面板"""
        # 图像显示标题
        image_title = ctk.CTkLabel(
            self.left_panel,
            text="📷 医学影像显示",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        image_title.pack(pady=(20, 10))
        
        # 图像显示区域
        self.image_frame = ctk.CTkFrame(self.left_panel)
        self.image_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 默认显示
        self.image_label = ctk.CTkLabel(
            self.image_frame,
            text="🖼️\n\n请选择要分析的医学影像\n\n支持格式: JPG, PNG, BMP\n\n双模型系统将先验证CT图像，再进行症状分析",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        )
        self.image_label.pack(expand=True)
        
    def create_right_panel(self):
        """创建右侧控制面板"""
        # 模型状态显示
        self.create_model_status_panel()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 结果显示区域
        self.create_results_panel()
        
        # 功能按钮
        self.create_function_buttons()
        
    def create_model_status_panel(self):
        """创建模型状态面板"""
        status_frame = ctk.CTkFrame(self.right_panel)
        status_frame.pack(fill="x", padx=20, pady=20)
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="🤖 双模型状态",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 10))
        
        # CT识别模型状态
        self.ct_detection_status = ctk.CTkLabel(
            status_frame,
            text="CT识别模型: 🔴 未加载",
            font=ctk.CTkFont(size=12)
        )
        self.ct_detection_status.pack(pady=2)
        
        # 症状分析模型状态
        self.ct_classification_status = ctk.CTkLabel(
            status_frame,
            text="症状分析模型: 🔴 未加载",
            font=ctk.CTkFont(size=12)
        )
        self.ct_classification_status.pack(pady=2)
        
        # 系统整体状态
        self.system_status = ctk.CTkLabel(
            status_frame,
            text="系统状态: ⏳ 初始化中...",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.system_status.pack(pady=(10, 15))
        
    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = ctk.CTkFrame(self.right_panel)
        control_frame.pack(fill="x", padx=20, pady=10)
        
        # 选择图像按钮
        self.select_btn = ctk.CTkButton(
            control_frame,
            text="📁 选择影像文件",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.select_image
        )
        self.select_btn.pack(fill="x", pady=10)
        
        # 开始分析按钮
        self.analyze_btn = ctk.CTkButton(
            control_frame,
            text="🔍 开始双模型分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.start_dual_analysis,
            state="disabled"
        )
        self.analyze_btn.pack(fill="x", pady=10)

        # 摄像头控制按钮
        self.camera_btn = ctk.CTkButton(
            control_frame,
            text="📹 启动摄像头",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.toggle_camera,
            fg_color="green"
        )
        self.camera_btn.pack(fill="x", pady=10)

        # 拍照分析按钮
        self.capture_btn = ctk.CTkButton(
            control_frame,
            text="📸 拍照分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            command=self.capture_and_analyze,
            state="disabled",
            fg_color="purple"
        )
        self.capture_btn.pack(fill="x", pady=10)
        
        # 进度条
        self.progress = ctk.CTkProgressBar(control_frame)
        self.progress.pack(fill="x", pady=10)
        self.progress.set(0)
        
    def create_results_panel(self):
        """创建结果显示面板"""
        results_frame = ctk.CTkFrame(self.right_panel)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 结果标题
        results_title = ctk.CTkLabel(
            results_frame,
            text="📊 双模型分析结果",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # CT检测结果
        self.ct_result_label = ctk.CTkLabel(
            results_frame,
            text="CT检测: 等待分析...",
            font=ctk.CTkFont(size=12),
            wraplength=320
        )
        self.ct_result_label.pack(pady=5)
        
        # 症状分析结果
        self.symptom_result_label = ctk.CTkLabel(
            results_frame,
            text="症状分析: 等待分析...",
            font=ctk.CTkFont(size=14),
            wraplength=320
        )
        self.symptom_result_label.pack(pady=10)
        
        # 置信度显示
        self.confidence_label = ctk.CTkLabel(
            results_frame,
            text="",
            font=ctk.CTkFont(size=12)
        )
        self.confidence_label.pack(pady=5)
        
        # 详细概率显示框
        self.details_frame = ctk.CTkScrollableFrame(results_frame, height=120)
        self.details_frame.pack(fill="x", padx=10, pady=10)
        
    def create_function_buttons(self):
        """创建功能按钮"""
        func_frame = ctk.CTkFrame(self.right_panel)
        func_frame.pack(fill="x", padx=20, pady=10)
        
        # 保存结果按钮
        self.save_btn = ctk.CTkButton(
            func_frame,
            text="💾 保存结果",
            command=self.save_results,
            state="disabled"
        )
        self.save_btn.pack(fill="x", pady=3)

        # 生成PDF报告按钮
        self.pdf_btn = ctk.CTkButton(
            func_frame,
            text="📄 生成PDF报告",
            command=self.generate_pdf_report,
            state="disabled",
            fg_color="orange"
        )
        self.pdf_btn.pack(fill="x", pady=3)

        # 生成HTML报告按钮
        self.html_btn = ctk.CTkButton(
            func_frame,
            text="🌐 生成HTML报告",
            command=self.generate_html_report,
            state="disabled",
            fg_color="purple"
        )
        self.html_btn.pack(fill="x", pady=3)
        
        # 查看历史按钮
        self.history_btn = ctk.CTkButton(
            func_frame,
            text="📋 查看历史",
            command=self.show_history
        )
        self.history_btn.pack(fill="x", pady=3)
        
        # 关于按钮
        self.about_btn = ctk.CTkButton(
            func_frame,
            text="ℹ️ 关于软件",
            command=self.show_about
        )
        self.about_btn.pack(fill="x", pady=3)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ctk.CTkFrame(self.root)
        self.status_frame.pack(fill="x", side="bottom", padx=20, pady=(0, 20))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="🤖 正在加载双模型系统...",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # 版本信息
        self.version_label = ctk.CTkLabel(
            self.status_frame,
            text="v2.0 双模型联用版",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.version_label.pack(side="right", padx=20, pady=10)
