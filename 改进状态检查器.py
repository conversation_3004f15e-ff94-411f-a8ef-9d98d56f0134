"""
改进状态检查器
正确解析现有的JSON文件格式
"""

import os
import json
import glob
import time

print("📊 改进状态检查器")
print("🔍 正确解析所有训练结果")
print(f"⏰ 检查时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 60)

# 检查所有可能的输出目录
output_dirs = [
    r"D:\模型开发\audio",
    "w",
    "trained_audio_models",
    "."
]

all_results = []

print("📁 扫描输出目录...")

for output_dir in output_dirs:
    if os.path.exists(output_dir):
        print(f"\n📂 检查目录: {output_dir}")
        
        # 查找所有JSON文件
        json_files = glob.glob(os.path.join(output_dir, "*.json"))
        
        print(f"   发现 {len(json_files)} 个JSON文件")
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                result = {
                    'file': json_file,
                    'directory': output_dir,
                    'filename': os.path.basename(json_file)
                }
                
                # 解析不同格式的JSON文件
                accuracy = 0
                method = "Unknown"
                model_type = "Unknown"
                
                # 格式1: 直接包含accuracy字段
                if 'accuracy' in data:
                    accuracy = data['accuracy']
                    method = data.get('model_type', 'Unknown')
                    model_type = "Direct Format"
                
                # 格式2: performance.test_accuracy
                elif 'performance' in data and 'test_accuracy' in data['performance']:
                    accuracy = data['performance']['test_accuracy']
                    method = "Neural Network"
                    model_type = "Performance Format"
                
                # 格式3: training_summary
                elif 'training_summary' in data:
                    summary = data['training_summary']
                    accuracy = summary.get('best_accuracy', 0)
                    method = summary.get('best_model', 'Unknown')
                    model_type = "Training Summary"
                
                # 格式4: 其他可能的格式
                elif 'model_info' in data:
                    if 'accuracy' in data['model_info']:
                        accuracy = data['model_info']['accuracy']
                    elif 'performance' in data and 'accuracy' in data['performance']:
                        accuracy = data['performance']['accuracy']
                    method = data['model_info'].get('model_name', 'Unknown')
                    model_type = "Model Info Format"
                
                # 提取其他有用信息
                dementia_performance = None
                if 'performance' in data and 'classification_report' in data['performance']:
                    report = data['performance']['classification_report']
                    if 'Dementia' in report:
                        dementia_info = report['Dementia']
                        dementia_performance = {
                            'precision': dementia_info.get('precision', 0),
                            'recall': dementia_info.get('recall', 0),
                            'f1_score': dementia_info.get('f1-score', 0),
                            'support': dementia_info.get('support', 0)
                        }
                
                result.update({
                    'accuracy': accuracy,
                    'method': method,
                    'type': model_type,
                    'dementia_performance': dementia_performance
                })
                
                if accuracy > 0:
                    all_results.append(result)
                    status = "🏆" if accuracy >= 0.90 else "✅" if accuracy >= 0.85 else "📈" if accuracy >= 0.70 else "⚠️"
                    print(f"     ✅ {result['filename']}: {accuracy:.4f} ({accuracy*100:.2f}%) {status}")
                    
                    if dementia_performance:
                        dem_recall = dementia_performance['recall']
                        dem_precision = dementia_performance['precision']
                        dem_status = "🎯" if dem_recall >= 0.5 else "❌"
                        print(f"        Dementia: 精确率={dem_precision:.3f}, 召回率={dem_recall:.3f} {dem_status}")
                else:
                    print(f"     ⚠️ {result['filename']}: 无准确率信息")
            
            except Exception as e:
                print(f"     ❌ {os.path.basename(json_file)}: 读取失败 ({e})")
        
        # 查找模型文件
        model_files = []
        for pattern in ["*.h5", "*.pkl"]:
            model_files.extend(glob.glob(os.path.join(output_dir, pattern)))
        
        if model_files:
            print(f"   发现 {len(model_files)} 个模型文件:")
            for model_file in model_files:
                file_size = os.path.getsize(model_file) / (1024*1024)  # MB
                mod_time = time.ctime(os.path.getmtime(model_file))
                print(f"     📦 {os.path.basename(model_file)} ({file_size:.2f}MB, {mod_time})")

# 分析结果
print(f"\n📊 训练结果详细分析:")
print("=" * 90)

if all_results:
    # 按准确率排序
    all_results.sort(key=lambda x: x.get('accuracy', 0), reverse=True)
    
    print(f"{'排名':<4} {'文件名':<30} {'准确率':<10} {'方法':<20} {'Dementia召回':<12}")
    print("-" * 90)
    
    for i, result in enumerate(all_results, 1):
        accuracy = result.get('accuracy', 0)
        method = result.get('method', 'Unknown')
        filename = result.get('filename', 'Unknown')
        
        # Dementia召回率
        dementia_recall = "N/A"
        if result.get('dementia_performance'):
            dementia_recall = f"{result['dementia_performance']['recall']:.3f}"
        
        # 状态标识
        if accuracy >= 0.90:
            status = "🏆"
        elif accuracy >= 0.85:
            status = "✅"
        elif accuracy >= 0.70:
            status = "📈"
        else:
            status = "⚠️"
        
        print(f"{i:<4} {filename:<30} {accuracy:<10.4f} {method:<20} {dementia_recall:<12} {status}")
    
    # 最佳结果分析
    best_result = all_results[0]
    print(f"\n🏆 当前最佳结果:")
    print(f"   文件: {best_result['filename']}")
    print(f"   准确率: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
    print(f"   方法: {best_result['method']}")
    print(f"   位置: {best_result['directory']}")
    
    if best_result.get('dementia_performance'):
        dem_perf = best_result['dementia_performance']
        print(f"   Dementia类别表现:")
        print(f"     精确率: {dem_perf['precision']:.3f}")
        print(f"     召回率: {dem_perf['recall']:.3f}")
        print(f"     F1分数: {dem_perf['f1_score']:.3f}")
        print(f"     支持样本: {dem_perf['support']}")
    
    # 问题分析
    print(f"\n🔍 问题分析:")
    
    # 检查Dementia类别识别问题
    dementia_issues = []
    for result in all_results:
        if result.get('dementia_performance'):
            dem_recall = result['dementia_performance']['recall']
            if dem_recall == 0:
                dementia_issues.append(result)
    
    if dementia_issues:
        print(f"   ❌ Dementia识别问题: {len(dementia_issues)} 个模型完全无法识别Dementia类别")
        print(f"   🔍 这表明存在严重的类别不平衡问题")
        print(f"   💡 建议: 使用SMOTE过采样、调整类别权重、或专门的不平衡学习方法")
    
    # 准确率分析
    high_acc = [r for r in all_results if r['accuracy'] >= 0.85]
    medium_acc = [r for r in all_results if 0.70 <= r['accuracy'] < 0.85]
    low_acc = [r for r in all_results if r['accuracy'] < 0.70]
    
    print(f"\n📈 准确率分布:")
    print(f"   🏆 高准确率 (≥85%): {len(high_acc)} 个")
    print(f"   📈 中等准确率 (70-85%): {len(medium_acc)} 个")
    print(f"   ⚠️ 低准确率 (<70%): {len(low_acc)} 个")
    
    # 建议
    print(f"\n💡 改进建议:")
    
    if best_result['accuracy'] < 0.85:
        print(f"   1. 当前最佳准确率 {best_result['accuracy']*100:.1f}% 未达到85%医疗标准")
        print(f"   2. 建议尝试以下方法:")
        print(f"      - 使用SMOTE或ADASYN处理类别不平衡")
        print(f"      - 应用更强的正则化防止过拟合")
        print(f"      - 尝试集成学习方法 (Random Forest, XGBoost)")
        print(f"      - 增加特征工程 (多项式特征、交互特征)")
        print(f"      - 使用更深的神经网络架构")
    
    if dementia_issues:
        print(f"   3. Dementia类别识别问题严重:")
        print(f"      - 类别0 (Dementia) 样本过少，需要重点处理")
        print(f"      - 建议使用专门的少数类学习方法")
        print(f"      - 考虑调整决策阈值优化召回率")
    
    # 运行状态检查
    print(f"\n🔄 并行训练状态:")
    print(f"   当前有多个训练器在并行运行")
    print(f"   建议等待所有训练器完成后再次检查")
    print(f"   预期会有更好的结果产生")

else:
    print("❌ 未发现有效的训练结果")
    print("💡 建议检查训练进程是否正常运行")

print(f"\n⏰ 检查完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("📊 改进状态检查器结束")

# 显示当前最佳模型的使用建议
if all_results:
    best = all_results[0]
    print(f"\n🎯 当前最佳模型使用建议:")
    
    if best['accuracy'] >= 0.85:
        print(f"   ✅ 可用于医疗辅助诊断")
        print(f"   🏥 建议在临床环境中进行验证")
    elif best['accuracy'] >= 0.70:
        print(f"   📈 可用于初步筛查")
        print(f"   🔍 建议结合其他诊断方法")
    else:
        print(f"   ⚠️ 仅适用于研究目的")
        print(f"   🔬 需要进一步优化后才能实际应用")
    
    print(f"   📁 模型文件位置: {best['directory']}")
    print(f"   📋 详细报告: {best['file']}")
