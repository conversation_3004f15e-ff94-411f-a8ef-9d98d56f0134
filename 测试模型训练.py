"""
测试模型训练 - 简化版本
"""

import pandas as pd
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

# 检查数据文件是否存在
data_path = r"D:\模型开发\audio\processed_datasets"

print("🔍 检查数据文件...")

files_to_check = [
    "train_set_scaled.csv",
    "validation_set_scaled.csv", 
    "test_set_scaled.csv",
    "feature_info.json",
    "label_encoder.pkl",
    "scaler.pkl"
]

for file in files_to_check:
    file_path = os.path.join(data_path, file)
    if os.path.exists(file_path):
        print(f"✅ 找到: {file}")
        if file.endswith('.csv'):
            df = pd.read_csv(file_path)
            print(f"   形状: {df.shape}")
            print(f"   列名: {list(df.columns)[:5]}...")
    else:
        print(f"❌ 未找到: {file}")

print("\n🔍 检查当前目录...")
current_files = [f for f in os.listdir('.') if 'processed' in f or 'train' in f or 'validation' in f]
print(f"当前目录相关文件: {current_files}")

# 尝试在当前目录查找
print("\n🔍 检查当前目录下的CSV文件...")
csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
for file in csv_files:
    if 'train' in file or 'validation' in file or 'test' in file:
        print(f"✅ 找到: {file}")
        df = pd.read_csv(file)
        print(f"   形状: {df.shape}")
