"""
🔧 自适应EEG数据加载器
能够自动识别和适应不同的EEG数据集结构
"""

import os
import zipfile
import json
import numpy as np
import pandas as pd
from pathlib import Path
import mne
import warnings
warnings.filterwarnings('ignore')

class AdaptiveEEGLoader:
    """自适应EEG数据加载器"""
    
    def __init__(self, zip_path):
        self.zip_path = zip_path
        self.dataset_info = None
        self.subjects_data = []
        
    def analyze_dataset_structure(self):
        """分析数据集结构"""
        print("🔍 分析数据集结构...")
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                # 分析文件结构
                structure = {
                    'total_files': len(file_list),
                    'eeg_files': [],
                    'json_files': [],
                    'subjects': set(),
                    'data_format': None,
                    'has_bids_structure': False
                }
                
                for file_path in file_list:
                    # 检查EEG文件
                    if file_path.endswith(('.set', '.edf', '.fif', '.bdf')):
                        structure['eeg_files'].append(file_path)
                        
                        # 提取被试者信息
                        if 'sub-' in file_path:
                            parts = file_path.split('/')
                            for part in parts:
                                if part.startswith('sub-'):
                                    structure['subjects'].add(part)
                                    break
                    
                    # 检查JSON文件
                    elif file_path.endswith('.json'):
                        structure['json_files'].append(file_path)
                    
                    # 检查BIDS结构
                    if '/eeg/' in file_path or 'dataset_description.json' in file_path:
                        structure['has_bids_structure'] = True
                
                # 确定数据格式
                if any(f.endswith('.set') for f in structure['eeg_files']):
                    structure['data_format'] = 'EEGLAB'
                elif any(f.endswith('.edf') for f in structure['eeg_files']):
                    structure['data_format'] = 'EDF'
                elif any(f.endswith('.fif') for f in structure['eeg_files']):
                    structure['data_format'] = 'FIF'
                
                structure['subjects'] = sorted(list(structure['subjects']))
                self.dataset_info = structure
                
                print(f"📊 数据集信息:")
                print(f"   总文件数: {structure['total_files']}")
                print(f"   EEG文件数: {len(structure['eeg_files'])}")
                print(f"   被试者数: {len(structure['subjects'])}")
                print(f"   数据格式: {structure['data_format']}")
                print(f"   BIDS结构: {'是' if structure['has_bids_structure'] else '否'}")
                
                return structure
                
        except Exception as e:
            print(f"❌ 数据集分析失败: {e}")
            return None
    
    def extract_dataset(self, extract_path="extracted_eeg_data"):
        """提取数据集"""
        print(f"📦 提取数据集到: {extract_path}")
        
        os.makedirs(extract_path, exist_ok=True)
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
            
            print(f"✅ 数据集提取完成")
            return extract_path
            
        except Exception as e:
            print(f"❌ 数据集提取失败: {e}")
            return None
    
    def load_subject_data(self, extract_path, subject_id):
        """加载单个被试者数据"""
        try:
            # 查找被试者的EEG文件
            eeg_files = []
            
            for root, dirs, files in os.walk(extract_path):
                if subject_id in root:
                    for file in files:
                        if file.endswith(('.set', '.edf', '.fif', '.bdf')):
                            eeg_files.append(os.path.join(root, file))
            
            if not eeg_files:
                print(f"❌ 未找到 {subject_id} 的EEG文件")
                return None, None
            
            # 加载第一个EEG文件
            eeg_file = eeg_files[0]
            print(f"📂 加载: {eeg_file}")
            
            # 根据文件格式选择加载方法
            if eeg_file.endswith('.set'):
                raw = mne.io.read_raw_eeglab(eeg_file, preload=True, verbose=False)
            elif eeg_file.endswith('.edf'):
                raw = mne.io.read_raw_edf(eeg_file, preload=True, verbose=False)
            elif eeg_file.endswith('.fif'):
                raw = mne.io.read_raw_fif(eeg_file, preload=True, verbose=False)
            else:
                print(f"❌ 不支持的文件格式: {eeg_file}")
                return None, None
            
            # 获取数据
            data = raw.get_data()
            sfreq = raw.info['sfreq']
            
            print(f"   ✅ 通道数: {data.shape[0]}, 采样点: {data.shape[1]}, 采样率: {sfreq}Hz")
            
            # 尝试获取标签信息
            label = self.get_subject_label(extract_path, subject_id)
            
            return data, label
            
        except Exception as e:
            print(f"❌ 加载 {subject_id} 失败: {e}")
            return None, None
    
    def get_subject_label(self, extract_path, subject_id):
        """获取被试者标签"""
        # 方法1: 从JSON文件获取
        json_files = []
        for root, dirs, files in os.walk(extract_path):
            if subject_id in root:
                for file in files:
                    if file.endswith('.json'):
                        json_files.append(os.path.join(root, file))
        
        for json_file in json_files:
            try:
                with open(json_file, 'r') as f:
                    metadata = json.load(f)
                
                # 查找可能的标签字段
                label_fields = ['diagnosis', 'group', 'condition', 'label', 'class']
                for field in label_fields:
                    if field in metadata:
                        diagnosis = metadata[field]
                        return self.map_diagnosis_to_label(diagnosis)
                        
            except Exception as e:
                continue
        
        # 方法2: 从文件名推断
        if 'ad' in subject_id.lower() or 'alzheimer' in subject_id.lower():
            return 1  # AD
        elif 'ftd' in subject_id.lower() or 'frontotemporal' in subject_id.lower():
            return 2  # FTD
        elif 'cn' in subject_id.lower() or 'control' in subject_id.lower() or 'hc' in subject_id.lower():
            return 0  # 健康
        
        # 方法3: 根据编号推断（最后的备选方案）
        try:
            subject_num = int(''.join(filter(str.isdigit, subject_id)))
            total_subjects = len(self.dataset_info['subjects']) if self.dataset_info else 100
            
            if subject_num <= total_subjects // 3:
                return 0  # 健康
            elif subject_num <= 2 * total_subjects // 3:
                return 1  # AD
            else:
                return 2  # FTD
        except:
            return 0  # 默认为健康
    
    def map_diagnosis_to_label(self, diagnosis):
        """将诊断映射为标签"""
        diagnosis_str = str(diagnosis).lower()
        
        if any(keyword in diagnosis_str for keyword in ['cn', 'control', 'healthy', 'normal']):
            return 0  # 健康
        elif any(keyword in diagnosis_str for keyword in ['ad', 'alzheimer']):
            return 1  # AD
        elif any(keyword in diagnosis_str for keyword in ['ftd', 'frontotemporal']):
            return 2  # FTD
        else:
            return 0  # 默认
    
    def load_all_subjects(self, max_subjects=None):
        """加载所有被试者数据"""
        if not self.dataset_info:
            print("❌ 请先分析数据集结构")
            return None, None
        
        print(f"🔄 开始加载被试者数据...")
        
        # 提取数据集
        extract_path = self.extract_dataset()
        if not extract_path:
            return None, None
        
        all_data = []
        all_labels = []
        
        subjects = self.dataset_info['subjects']
        if max_subjects:
            subjects = subjects[:max_subjects]
        
        for i, subject_id in enumerate(subjects):
            print(f"处理被试 {i+1}/{len(subjects)}: {subject_id}")
            
            data, label = self.load_subject_data(extract_path, subject_id)
            
            if data is not None:
                all_data.append(data)
                all_labels.append(label)
            
        print(f"✅ 成功加载 {len(all_data)} 个被试者的数据")
        
        # 统计标签分布
        if all_labels:
            label_counts = pd.Series(all_labels).value_counts().sort_index()
            class_names = ['健康', 'AD', 'FTD']
            print(f"📊 标签分布:")
            for label, count in label_counts.items():
                if label < len(class_names):
                    print(f"   {class_names[label]}: {count}")
        
        return all_data, all_labels

def main():
    """主函数 - 测试数据加载器"""
    print("🔧 自适应EEG数据加载器测试")
    print("=" * 50)
    
    # 查找数据集文件
    possible_paths = [
        "/root/autodl-tmp/open-nuro-dataset.zip",
        "/root/autodl-tmp/EEG.zip",
        "open-nuro-dataset.zip",
        "EEG.zip"
    ]
    
    zip_path = None
    for path in possible_paths:
        if os.path.exists(path):
            zip_path = path
            break
    
    if not zip_path:
        print("❌ 未找到EEG数据集文件")
        return
    
    # 创建加载器
    loader = AdaptiveEEGLoader(zip_path)
    
    # 分析数据集结构
    structure = loader.analyze_dataset_structure()
    
    if structure:
        # 加载少量数据进行测试
        print(f"\n🧪 测试加载前3个被试者的数据...")
        all_data, all_labels = loader.load_all_subjects(max_subjects=3)
        
        if all_data:
            print(f"\n✅ 测试成功!")
            print(f"   数据形状示例: {all_data[0].shape}")
            print(f"   标签示例: {all_labels}")
        else:
            print(f"\n❌ 测试失败")

if __name__ == "__main__":
    main()
