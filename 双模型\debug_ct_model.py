# -*- coding: utf-8 -*-
"""
CT模型调试脚本
用于分析ct_other_model.h5的实际输出和行为
"""

import os
import warnings
warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

def analyze_ct_model():
    """分析CT模型的详细信息"""
    print("🔍 分析 ct_other_model.h5 模型...")
    
    try:
        import tensorflow as tf
        from tensorflow.keras.preprocessing import image
        import numpy as np
        
        tf.get_logger().setLevel('ERROR')
        
        ct_model_path = r"D:\模型开发\ct_other_model.h5"
        
        if not os.path.exists(ct_model_path):
            print(f"❌ 模型文件不存在: {ct_model_path}")
            return
        
        # 加载模型
        model = tf.keras.models.load_model(ct_model_path)
        print("✅ 模型加载成功")
        
        # 分析模型结构
        print(f"\n📊 模型结构信息:")
        print(f"   输入形状: {model.input_shape}")
        print(f"   输出形状: {model.output_shape}")
        print(f"   层数: {len(model.layers)}")
        
        # 显示模型摘要
        print(f"\n📋 模型摘要:")
        model.summary()
        
        # 分析输出层
        print(f"\n🎯 输出层分析:")
        output_layer = model.layers[-1]
        print(f"   输出层类型: {type(output_layer).__name__}")
        print(f"   激活函数: {getattr(output_layer, 'activation', 'Unknown')}")
        print(f"   输出单元数: {output_layer.units if hasattr(output_layer, 'units') else 'Unknown'}")
        
        # 测试模型预测
        print(f"\n🧪 测试模型预测:")
        
        # 创建随机测试数据
        test_input = np.random.random((1, 150, 150, 3))
        predictions = model.predict(test_input, verbose=0)
        
        print(f"   预测输出形状: {predictions.shape}")
        print(f"   预测值: {predictions}")
        print(f"   预测值范围: [{predictions.min():.6f}, {predictions.max():.6f}]")
        print(f"   预测值总和: {predictions.sum():.6f}")
        
        # 分析预测结果
        if predictions.shape[1] == 2:
            print(f"\n📈 二分类分析:")
            print(f"   类别0概率: {predictions[0][0]:.6f}")
            print(f"   类别1概率: {predictions[0][1]:.6f}")
            predicted_class = np.argmax(predictions, axis=1)[0]
            print(f"   预测类别: {predicted_class}")
            confidence = np.max(predictions, axis=1)[0]
            print(f"   置信度: {confidence:.6f}")
            
            # 检查是否使用softmax
            prob_sum = predictions[0].sum()
            print(f"   概率总和: {prob_sum:.6f}")
            if abs(prob_sum - 1.0) < 0.001:
                print("   ✅ 使用softmax激活，输出为概率分布")
            else:
                print("   ⚠️ 未使用softmax，可能需要额外处理")
        
        return model, tf, image, np
        
    except Exception as e:
        print(f"❌ 模型分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None, None

def test_with_sample_images():
    """如果有样本图像，测试模型行为"""
    print(f"\n🖼️ 样本图像测试:")
    
    # 请用户提供测试图像路径
    test_images = []
    
    print("请提供一些测试图像路径来验证模型行为:")
    print("(输入图像路径，每行一个，输入空行结束)")
    
    while True:
        path = input("图像路径: ").strip()
        if not path:
            break
        if os.path.exists(path):
            test_images.append(path)
            print(f"✅ 添加: {os.path.basename(path)}")
        else:
            print(f"❌ 文件不存在: {path}")
    
    if not test_images:
        print("⚠️ 未提供测试图像，跳过实际测试")
        return
    
    # 加载模型
    model, tf, image_module, np = analyze_ct_model()
    if not model:
        return
    
    print(f"\n🔍 测试 {len(test_images)} 张图像:")
    
    for i, img_path in enumerate(test_images):
        print(f"\n--- 图像 {i+1}: {os.path.basename(img_path)} ---")
        
        try:
            # 加载和预处理图像
            img = image_module.load_img(img_path, target_size=(150, 150))
            img_array = image_module.img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0
            
            # 预测
            predictions = model.predict(img_array, verbose=0)
            
            print(f"原始预测值: {predictions[0]}")
            
            if predictions.shape[1] == 2:
                class_0_prob = predictions[0][0]
                class_1_prob = predictions[0][1]
                predicted_class = np.argmax(predictions, axis=1)[0]
                confidence = np.max(predictions, axis=1)[0]
                
                print(f"类别0概率: {class_0_prob:.6f}")
                print(f"类别1概率: {class_1_prob:.6f}")
                print(f"预测类别: {predicted_class}")
                print(f"置信度: {confidence:.6f}")
                
                # 根据你的描述，让我们看看哪个类别对应CT图像
                print(f"如果类别0=非CT，类别1=CT: {'CT图像' if predicted_class == 1 else '非CT图像'}")
                print(f"如果类别0=CT，类别1=非CT: {'CT图像' if predicted_class == 0 else '非CT图像'}")
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def main():
    """主函数"""
    print("🔍 CT模型调试工具")
    print("=" * 50)
    
    # 分析模型结构
    model, tf, image_module, np = analyze_ct_model()
    
    if model:
        print(f"\n" + "=" * 50)
        print("📝 请告诉我:")
        print("1. 在你的训练中，类别0和类别1分别代表什么？")
        print("2. CT图像对应哪个类别编号？")
        print("3. 模型的预期输出范围是什么？")
        
        # 测试样本图像
        test_with_sample_images()

if __name__ == "__main__":
    main()
