"""
🔗 双模型系统集成配置
整合EEG模型与现有的MRI双模型系统
"""

import os
import json
import pickle
import numpy as np
from pathlib import Path

class DualModelIntegrationConfig:
    """双模型系统集成配置"""
    
    def __init__(self):
        # 现有模型路径
        self.mri_model_path = "D:/模型开发/MRI_class.h5"
        self.picture_model_path = "D:/模型开发/picture_class.h5"
        
        # 新EEG模型路径
        self.eeg_model_path = "D:/模型开发/EEG_complete_classifier.pkl"
        
        # 数据集信息
        self.eeg_dataset_split_path = "D:/模型开发/eeg_dataset_split.json"
        
        # 模型配置
        self.model_configs = {
            'mri_model': {
                'name': 'MRI症状分析模型',
                'classes': ['MildDemented', 'ModerateDemented', 'NonDemented', 'VeryMildDemented'],
                'input_type': 'image',
                'description': '基于MRI图像的痴呆症状分析'
            },
            'picture_model': {
                'name': '图像分类模型',
                'classes': ['CT', 'Non-CT'],
                'input_type': 'image', 
                'description': '检测输入图像是否为CT扫描'
            },
            'eeg_model': {
                'name': 'EEG脑电分析模型',
                'classes': ['健康', '阿尔茨海默病', '额颞叶痴呆'],
                'input_type': 'eeg_signal',
                'description': '基于EEG信号的痴呆症检测'
            }
        }
    
    def get_integration_strategy(self):
        """获取集成策略"""
        return {
            'multi_modal_analysis': {
                'description': '多模态分析 - 同时使用MRI和EEG数据',
                'workflow': [
                    '1. 图像预处理和验证 (picture_model)',
                    '2. MRI症状分析 (mri_model)', 
                    '3. EEG信号分析 (eeg_model)',
                    '4. 多模态结果融合',
                    '5. 生成综合诊断报告'
                ],
                'confidence_weighting': {
                    'mri_weight': 0.4,
                    'eeg_weight': 0.6  # EEG在痴呆检测中通常更敏感
                }
            },
            'sequential_analysis': {
                'description': '序列分析 - 根据数据类型选择模型',
                'workflow': [
                    '1. 检测输入数据类型',
                    '2. 如果是图像: 使用picture_model + mri_model',
                    '3. 如果是EEG信号: 使用eeg_model',
                    '4. 生成对应的分析报告'
                ]
            }
        }
    
    def get_training_recommendations(self):
        """获取训练建议"""
        return {
            'eeg_training': {
                'dataset_split': '训练:验证:测试 = 6:2:2',
                'batch_size': 16,
                'epochs': 150,
                'early_stopping': True,
                'data_augmentation': False,  # EEG信号不适合传统图像增强
                'feature_extraction': [
                    '频域特征 (Delta, Theta, Alpha, Beta, Gamma)',
                    '时域特征 (均值, 标准差, 偏度, 峰度)',
                    '连接性特征 (通道间相关性)'
                ]
            },
            'model_optimization': {
                'architecture': '双分支神经网络 + 特征融合',
                'regularization': 'Dropout + BatchNormalization',
                'optimizer': 'Adam (lr=0.001)',
                'loss_function': 'sparse_categorical_crossentropy'
            },
            'validation_strategy': {
                'cross_validation': False,  # 使用固定验证集
                'stratified_split': True,   # 保持类别平衡
                'performance_metrics': [
                    'accuracy', 'precision', 'recall', 'f1-score',
                    'confusion_matrix', 'classification_report'
                ]
            }
        }
    
    def get_gui_integration_plan(self):
        """获取GUI集成方案"""
        return {
            'interface_modifications': {
                'new_tabs': [
                    'EEG信号分析',
                    '多模态分析',
                    '综合诊断报告'
                ],
                'input_methods': [
                    'EEG文件上传 (.set, .edf, .fif)',
                    'MRI图像上传 (.jpg, .png, .dcm)',
                    '同时上传多种数据'
                ],
                'output_formats': [
                    'HTML诊断报告',
                    'PDF报告',
                    '结果可视化图表'
                ]
            },
            'workflow_integration': {
                'single_modal': '单一模态分析 (保持现有功能)',
                'multi_modal': '多模态联合分析 (新增功能)',
                'comparison': '不同模态结果对比'
            }
        }
    
    def save_config(self, save_path="D:/模型开发/dual_model_config.json"):
        """保存配置信息"""
        config_data = {
            'model_paths': {
                'mri_model': self.mri_model_path,
                'picture_model': self.picture_model_path,
                'eeg_model': self.eeg_model_path
            },
            'model_configs': self.model_configs,
            'integration_strategy': self.get_integration_strategy(),
            'training_recommendations': self.get_training_recommendations(),
            'gui_integration_plan': self.get_gui_integration_plan()
        }
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 配置已保存: {save_path}")
            return True
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
            return False
    
    def load_eeg_dataset_info(self):
        """加载EEG数据集划分信息"""
        if os.path.exists(self.eeg_dataset_split_path):
            try:
                with open(self.eeg_dataset_split_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载EEG数据集信息失败: {e}")
        return None
    
    def check_model_compatibility(self):
        """检查模型兼容性"""
        print("🔍 检查模型兼容性...")
        
        compatibility_report = {
            'mri_model': os.path.exists(self.mri_model_path),
            'picture_model': os.path.exists(self.picture_model_path),
            'eeg_model': os.path.exists(self.eeg_model_path),
            'eeg_dataset_split': os.path.exists(self.eeg_dataset_split_path)
        }
        
        print("📋 模型文件检查:")
        for model_name, exists in compatibility_report.items():
            status = "✅" if exists else "❌"
            print(f"   {model_name}: {status}")
        
        return compatibility_report
    
    def generate_integration_report(self):
        """生成集成报告"""
        print("\n📊 双模型系统集成报告")
        print("=" * 60)
        
        # 检查模型兼容性
        compatibility = self.check_model_compatibility()
        
        # 加载EEG数据集信息
        eeg_info = self.load_eeg_dataset_info()
        
        print(f"\n🎯 集成目标:")
        print(f"   将EEG脑电分析模型集成到现有的MRI双模型系统中")
        print(f"   实现多模态医学影像和信号的联合分析")
        
        print(f"\n📋 现有系统:")
        print(f"   MRI症状分析: 4类痴呆症状检测")
        print(f"   图像分类: CT/非CT图像识别")
        
        print(f"\n🆕 新增功能:")
        print(f"   EEG信号分析: 3类痴呆检测 (健康/AD/FTD)")
        print(f"   多模态融合: MRI + EEG联合诊断")
        
        if eeg_info:
            dataset_info = eeg_info.get('dataset_info', {})
            split_info = eeg_info.get('split_result', {})
            
            print(f"\n📊 EEG数据集信息:")
            print(f"   总被试者数: {dataset_info.get('total_subjects', 'N/A')}")
            print(f"   训练集: {split_info.get('train', {}).get('count', 'N/A')} 人")
            print(f"   验证集: {split_info.get('val', {}).get('count', 'N/A')} 人")
            print(f"   测试集: {split_info.get('test', {}).get('count', 'N/A')} 人")
        
        print(f"\n🔄 建议的集成步骤:")
        print(f"   1. 完成EEG模型训练 (运行 train_eeg_model.py)")
        print(f"   2. 修改现有GUI界面，添加EEG分析功能")
        print(f"   3. 实现多模态数据融合算法")
        print(f"   4. 更新HTML报告生成功能")
        print(f"   5. 进行系统集成测试")
        
        print(f"\n" + "=" * 60)


def main():
    """主函数"""
    print("🔗 双模型系统集成配置")
    print("=" * 50)
    
    # 创建配置
    config = DualModelIntegrationConfig()
    
    # 生成集成报告
    config.generate_integration_report()
    
    # 保存配置
    config.save_config()
    
    print(f"\n✅ 配置完成!")


if __name__ == "__main__":
    main()
