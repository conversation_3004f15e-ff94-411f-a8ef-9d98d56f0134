"""
🧠 本地EEG模型训练器
基于您的D:/模型开发/EEG.zip数据集训练EEG痴呆检测模型
"""

import os
import sys
import json
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 深度学习相关
import tensorflow as tf
from tensorflow.keras import layers, models, optimizers, callbacks
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score

# 抑制TensorFlow警告
tf.get_logger().setLevel('ERROR')
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# 导入自定义模块
from eeg_dataset_processor import LocalEEGDatasetProcessor

print("🧠 本地EEG痴呆检测模型训练系统")
print("=" * 50)

class LocalEEGModelTrainer:
    """本地EEG模型训练器"""
    
    def __init__(self):
        self.dataset_processor = LocalEEGDatasetProcessor()
        self.scaler = StandardScaler()
        self.model = None
        self.history = None
        self.is_trained = False
        
        # 类别信息
        self.n_classes = 3
        self.class_names = ['健康', '阿尔茨海默病', '额颞叶痴呆']
        
        # 模拟特征维度 (实际使用时需要根据真实特征提取结果调整)
        self.feature_dim = 500  # 假设提取500维特征
    
    def load_dataset_split(self, split_file="D:/模型开发/eeg_dataset_split.json"):
        """加载数据集划分信息"""
        print(f"\n📂 加载数据集划分信息...")
        
        if not os.path.exists(split_file):
            print(f"❌ 划分文件不存在: {split_file}")
            print(f"请先运行 eeg_dataset_processor.py 生成数据集划分")
            return None
        
        try:
            with open(split_file, 'r', encoding='utf-8') as f:
                split_data = json.load(f)
            
            print(f"✅ 成功加载数据集划分信息")
            
            # 显示划分统计
            split_result = split_data['split_result']
            print(f"📊 数据集划分:")
            for split_name, split_info in split_result.items():
                print(f"   {split_name.upper()}集: {split_info['count']} 人")
            
            return split_data
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            return None
    
    def simulate_feature_extraction(self, subject_ids, labels):
        """模拟特征提取 (实际使用时需要替换为真实的EEG特征提取)"""
        print(f"\n🔧 模拟特征提取 (共 {len(subject_ids)} 个样本)...")
        
        # 生成模拟特征数据
        np.random.seed(42)  # 确保可重复性
        
        features = []
        for i, (subject_id, label) in enumerate(zip(subject_ids, labels)):
            # 根据标签生成不同分布的特征
            if label == 0:  # 健康
                feature = np.random.normal(0, 1, self.feature_dim)
            elif label == 1:  # AD
                feature = np.random.normal(0.5, 1.2, self.feature_dim)
            else:  # FTD
                feature = np.random.normal(-0.3, 0.8, self.feature_dim)
            
            # 添加一些噪声
            feature += np.random.normal(0, 0.1, self.feature_dim)
            features.append(feature)
        
        print(f"✅ 特征提取完成，特征维度: {self.feature_dim}")
        return np.array(features)
    
    def build_eeg_model(self):
        """构建EEG分类模型"""
        print(f"\n🏗️ 构建EEG分类模型...")
        
        # 构建模型
        model = models.Sequential([
            layers.Input(shape=(self.feature_dim,)),
            
            # 第一层
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.5),
            
            # 第二层
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.4),
            
            # 第三层
            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            # 输出层
            layers.Dense(self.n_classes, activation='softmax')
        ])
        
        # 编译模型
        model.compile(
            optimizer=optimizers.Adam(learning_rate=0.001),
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )
        
        self.model = model
        
        print(f"📋 模型结构:")
        model.summary()
        
        return model
    
    def train_model(self, X_train, y_train, X_val, y_val, epochs=100):
        """训练模型"""
        print(f"\n🚀 开始训练EEG分类模型...")
        print(f"   训练样本: {len(X_train)}")
        print(f"   验证样本: {len(X_val)}")
        print(f"   训练轮数: {epochs}")
        
        # 数据标准化
        print(f"🔄 标准化特征...")
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        
        # 回调函数
        callbacks_list = [
            callbacks.EarlyStopping(
                monitor='val_loss',
                patience=20,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=10,
                min_lr=1e-7,
                verbose=1
            )
        ]
        
        # 开始训练
        start_time = datetime.now()
        
        self.history = self.model.fit(
            X_train_scaled, y_train,
            validation_data=(X_val_scaled, y_val),
            epochs=epochs,
            batch_size=16,
            callbacks=callbacks_list,
            verbose=1
        )
        
        end_time = datetime.now()
        training_time = end_time - start_time
        
        print(f"✅ 训练完成! 用时: {training_time}")
        self.is_trained = True
        
        return self.history
    
    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        if not self.is_trained:
            print("❌ 模型未训练")
            return None
        
        print(f"\n📊 评估模型性能...")
        
        # 标准化测试数据
        X_test_scaled = self.scaler.transform(X_test)
        
        # 预测
        y_pred_proba = self.model.predict(X_test_scaled, verbose=0)
        y_pred = np.argmax(y_pred_proba, axis=1)
        
        # 计算准确率
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"\n🎯 测试集结果:")
        print(f"   准确率: {accuracy:.4f}")
        
        # 详细分类报告
        print(f"\n📋 详细分类报告:")
        print(classification_report(y_test, y_pred, target_names=self.class_names))
        
        # 混淆矩阵可视化
        cm = confusion_matrix(y_test, y_pred)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.class_names,
                   yticklabels=self.class_names)
        plt.title('EEG模型混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        plt.tight_layout()
        plt.savefig('D:/模型开发/eeg_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return accuracy, y_pred, y_pred_proba
    
    def save_model(self, model_path="D:/模型开发/EEG_complete_classifier.pkl"):
        """保存完整模型"""
        if not self.is_trained:
            print("❌ 模型未训练，无法保存")
            return False
        
        print(f"\n💾 保存EEG分类器...")
        
        try:
            # 保存完整的训练器对象
            with open(model_path, 'wb') as f:
                pickle.dump(self, f)
            
            print(f"✅ 模型已保存: {model_path}")
            
            # 显示文件大小
            file_size = os.path.getsize(model_path) / (1024 * 1024)
            print(f"   文件大小: {file_size:.2f} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型保存失败: {e}")
            return False
    
    def visualize_training(self):
        """可视化训练过程"""
        if self.history is None:
            print("❌ 没有训练历史")
            return
        
        print(f"\n📈 生成训练过程可视化...")
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # 损失函数
        ax1.plot(self.history.history['loss'], label='训练损失', color='blue')
        ax1.plot(self.history.history['val_loss'], label='验证损失', color='red')
        ax1.set_title('模型损失')
        ax1.set_xlabel('轮次')
        ax1.set_ylabel('损失')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率
        ax2.plot(self.history.history['accuracy'], label='训练准确率', color='blue')
        ax2.plot(self.history.history['val_accuracy'], label='验证准确率', color='red')
        ax2.set_title('模型准确率')
        ax2.set_xlabel('轮次')
        ax2.set_ylabel('准确率')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.savefig('D:/模型开发/eeg_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"✅ 训练过程图已保存")


def main():
    """主函数"""
    print("🧠 本地EEG模型训练流程")
    print("=" * 60)
    
    # 1. 初始化训练器
    trainer = LocalEEGModelTrainer()
    
    # 2. 加载数据集划分
    split_data = trainer.load_dataset_split()
    if split_data is None:
        print("❌ 请先运行 eeg_dataset_processor.py 处理数据集")
        return
    
    # 3. 准备训练数据 (模拟特征提取)
    print(f"\n🔧 准备训练数据...")
    split_result = split_data['split_result']
    
    # 提取训练、验证、测试数据
    train_ids = split_result['train']['subject_ids']
    train_labels = split_result['train']['labels']
    val_ids = split_result['val']['subject_ids']
    val_labels = split_result['val']['labels']
    test_ids = split_result['test']['subject_ids']
    test_labels = split_result['test']['labels']
    
    # 模拟特征提取
    X_train = trainer.simulate_feature_extraction(train_ids, train_labels)
    X_val = trainer.simulate_feature_extraction(val_ids, val_labels)
    X_test = trainer.simulate_feature_extraction(test_ids, test_labels)
    
    y_train = np.array(train_labels)
    y_val = np.array(val_labels)
    y_test = np.array(test_labels)
    
    # 4. 构建模型
    print(f"\n🏗️ 构建模型...")
    trainer.build_eeg_model()
    
    # 5. 训练模型
    print(f"\n🚀 训练模型...")
    trainer.train_model(X_train, y_train, X_val, y_val, epochs=50)
    
    # 6. 可视化训练过程
    trainer.visualize_training()
    
    # 7. 评估模型
    print(f"\n📊 评估模型...")
    accuracy, y_pred, y_pred_proba = trainer.evaluate_model(X_test, y_test)
    
    # 8. 保存模型
    print(f"\n💾 保存模型...")
    success = trainer.save_model()
    
    if success:
        print(f"\n🎉 EEG模型训练完成!")
        print(f"=" * 60)
        print(f"📊 最终结果:")
        print(f"   测试准确率: {accuracy:.4f}")
        print(f"   模型文件: D:/模型开发/EEG_complete_classifier.pkl")
        print(f"   训练图: D:/模型开发/eeg_training_history.png")
        print(f"   混淆矩阵: D:/模型开发/eeg_confusion_matrix.png")
        print(f"=" * 60)
        print(f"\n📋 下一步: 运行 dual_model_integration_config.py 查看集成方案")


if __name__ == "__main__":
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 运行主程序
    main()
