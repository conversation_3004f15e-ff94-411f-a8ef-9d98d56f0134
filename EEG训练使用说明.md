# 🧠 EEG痴呆症检测模型训练完整指南

## 📋 概述

本系统基于OpenNeuro阿尔茨海默病和额颞叶痴呆EEG数据集，训练一个完整的EEG分类器，用于痴呆症检测。

## 🎯 系统特点

- ✅ **完整流程**: 从数据解压到模型保存的全自动化流程
- ✅ **智能分割**: 自动按8:2比例分割训练/验证/测试集
- ✅ **最优模型**: 双分支融合架构，性能优异
- ✅ **可视化**: 模型结构图、训练过程图、混淆矩阵
- ✅ **单文件输出**: 训练完成后只生成一个`.pkl`模型文件

## 🚀 使用步骤

### 第一步: 准备数据集

1. **下载数据集**
   ```bash
   # 在AutoDL环境中下载
   kaggle datasets download -d yosftag/open-nuro-dataset
   ```

2. **确认文件位置**
   ```
   /root/autodl-tmp/
   ├── open-nuro-dataset.zip  ← 确保这个文件存在
   ├── EEG_数据集处理和模型训练.py
   └── 运行EEG训练.py
   ```

### 第二步: 运行训练

```bash
# 方法1: 直接运行主脚本
python EEG_数据集处理和模型训练.py

# 方法2: 使用启动脚本（推荐）
python 运行EEG训练.py
```

### 第三步: 查看结果

训练完成后会生成以下文件：
```
📁 输出文件:
├── EEG_complete_classifier.pkl    ← 完整模型文件（主要）
├── model_architecture.png         ← 模型结构图
├── training_history.png          ← 训练过程图
├── confusion_matrix.png          ← 混淆矩阵
└── best_eeg_model.h5             ← 最佳权重备份
```

## 📊 训练流程详解

### 1. 数据集处理
- 🔄 自动解压`open-nuro-dataset.zip`
- 📊 扫描所有被试者文件夹（sub-001, sub-002, ...）
- 🏷️ 自动分配标签（健康/AD/FTD）
- 📈 显示数据集统计信息

### 2. 特征提取
- **频域特征**: 5个频段（δ, θ, α, β, γ）的功率和相对功率
- **时域特征**: 均值、标准差、方差、最值、中位数、偏度、峰度
- **连接性特征**: 通道间相关性矩阵
- **总特征数**: 约500+维度

### 3. 数据分割
- 📊 **训练集**: 60% (用于模型学习)
- 📊 **验证集**: 20% (用于超参数调优)
- 📊 **测试集**: 20% (用于最终评估)
- 🎯 保持类别平衡的分层抽样

### 4. 模型架构
```
输入层 (特征维度)
    ├── 分支1: 深度特征提取
    │   ├── Dense(512) + BatchNorm + Dropout(0.5)
    │   └── Dense(256) + BatchNorm + Dropout(0.4)
    └── 分支2: 浅层特征提取
        └── Dense(256) + BatchNorm + Dropout(0.3)
            ↓
        特征融合 (Concatenate)
            ↓
        Dense(128) + BatchNorm + Dropout(0.3)
            ↓
        Dense(64) + BatchNorm + Dropout(0.2)
            ↓
        输出层: Dense(3, softmax)
```

### 5. 训练配置
- **优化器**: Adam (lr=0.001)
- **损失函数**: sparse_categorical_crossentropy
- **批次大小**: 16
- **最大轮数**: 150
- **早停**: 验证损失25轮不改善
- **学习率衰减**: 验证损失15轮不改善时减半

### 6. 评估指标
- ✅ **准确率**: 整体分类准确性
- ✅ **精确率**: 每类别的精确度
- ✅ **召回率**: 每类别的召回率
- ✅ **F1分数**: 精确率和召回率的调和平均
- ✅ **混淆矩阵**: 详细的分类结果

## 🎨 可视化输出

### 1. 模型结构图
- 📊 完整的网络架构图
- 🔍 显示每层的形状和连接
- 💾 保存为高分辨率PNG

### 2. 训练过程图
- 📈 损失函数变化曲线
- 📈 准确率变化曲线
- 📈 精确率和召回率曲线
- 🎯 训练集vs验证集对比

### 3. 混淆矩阵
- 🎯 每个类别的分类结果
- 📊 热力图可视化
- 🔢 具体数值显示

## 💾 模型使用

### 加载模型
```python
from EEG_数据集处理和模型训练 import CompleteEEGClassifier

# 加载训练好的模型
classifier = CompleteEEGClassifier.load_complete_model('EEG_complete_classifier.pkl')

# 预测新的EEG数据
result = classifier.predict_single(new_eeg_data)
print(f"诊断: {result['class_name']}")
print(f"置信度: {result['confidence']:.2%}")
```

### 集成到双模态系统
```python
class TripleModalAISystem:
    def __init__(self):
        # 现有模型
        self.mri_model = load_model('D:/模型开发/MRI_class.h5')
        self.ct_model = load_model('D:/模型开发/picture_class.h5')
        
        # 新增EEG模型
        self.eeg_classifier = CompleteEEGClassifier.load_complete_model('EEG_complete_classifier.pkl')
    
    def analyze_multimodal(self, mri_path=None, eeg_data=None):
        results = {}
        if mri_path:
            results['mri'] = self.analyze_mri(mri_path)
        if eeg_data is not None:
            results['eeg'] = self.eeg_classifier.predict_single(eeg_data)
        return results
```

## ⚠️ 注意事项

1. **内存要求**: 建议至少8GB RAM
2. **存储空间**: 需要约2GB可用空间
3. **训练时间**: 根据硬件配置，约30-60分钟
4. **GPU加速**: 支持CUDA加速（可选）

## 🔧 故障排除

### 常见问题

1. **数据集未找到**
   ```
   ❌ 未找到数据集文件: /root/autodl-tmp/open-nuro-dataset.zip
   ```
   **解决**: 确保已下载数据集到正确位置

2. **内存不足**
   ```
   ❌ ResourceExhaustedError: OOM when allocating tensor
   ```
   **解决**: 减小批次大小或使用更小的模型

3. **依赖缺失**
   ```
   ❌ ModuleNotFoundError: No module named 'mne'
   ```
   **解决**: 运行 `pip install mne`

## 📞 技术支持

如遇到问题，请检查：
1. ✅ Python版本 >= 3.7
2. ✅ TensorFlow版本 >= 2.4
3. ✅ 所有依赖已正确安装
4. ✅ 数据集文件完整且未损坏

---

🎉 **祝训练顺利！完成后你将拥有一个强大的EEG痴呆症检测模型！**
