"""
🧠 科学EEG CPU训练器
基于真实EEG数据的深度学习模型训练
专为CPU环境优化
"""

import os
import numpy as np
import pandas as pd

# 强制使用CPU，避免GPU问题
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import mne
import warnings
warnings.filterwarnings('ignore')

# 确保TensorFlow使用CPU
tf.config.set_visible_devices([], 'GPU')
print("💻 强制使用CPU模式，避免GPU兼容性问题")

class ScientificEEGTrainer:
    """科学EEG训练器"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.label_encoder = LabelEncoder()
        
        # EEG参数
        self.sampling_rate = 128  # Hz
        self.n_channels = 19      # 标准19通道
        self.epoch_length = 1.0   # 1秒epoch
        self.n_samples = int(self.sampling_rate * self.epoch_length)
        
        print("🧠 科学EEG CPU训练器初始化")
        print(f"📁 数据路径: {self.data_path}")
        print(f"📊 EEG参数: {self.n_channels}通道, {self.sampling_rate}Hz, {self.n_samples}样本/epoch")
        print("💻 运行模式: CPU Only")
    
    def load_eeg_data(self, split_name):
        """加载EEG数据"""
        print(f"\n📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        # 读取标签
        labels_df = pd.read_csv(labels_file, sep='\t')
        print(f"📋 标签信息: {len(labels_df)} 个受试者")
        
        # 加载EEG数据
        X_data = []
        y_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找对应的.set文件
            set_files = [f for f in os.listdir(split_dir) if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                print(f"⚠️ 未找到{subject_id}的.set文件")
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                # 使用MNE读取.set文件
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                
                # 获取数据
                data = raw.get_data()  # shape: (n_channels, n_timepoints)
                
                # 分割成epochs
                epochs = self.create_epochs(data)
                
                # 添加到数据集
                for epoch in epochs:
                    X_data.append(epoch)
                    y_labels.append(label)
                
                print(f"✅ {subject_id}: {len(epochs)} epochs")
                
            except Exception as e:
                print(f"❌ 加载{subject_id}失败: {e}")
                continue
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        print(f"📊 {split_name}数据加载完成:")
        print(f"   数据形状: {X.shape}")
        print(f"   标签数量: {len(y)}")
        print(f"   标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    
    def create_epochs(self, data):
        """创建epochs"""
        n_channels, n_timepoints = data.shape
        
        # 计算可以创建多少个epoch
        n_epochs = n_timepoints // self.n_samples
        
        epochs = []
        for i in range(n_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            
            epoch = data[:, start_idx:end_idx]  # shape: (n_channels, n_samples)
            
            # 确保通道数正确
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]  # 取前19个通道
            elif epoch.shape[0] < self.n_channels:
                # 如果通道数不足，用零填充
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            epochs.append(epoch)
        
        return epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("\n🔧 预处理数据...")
        
        # 标准化EEG数据
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                # Z-score标准化
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 0:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
            print(f"📋 标签编码: {dict(zip(self.label_encoder.classes_, range(len(self.label_encoder.classes_))))}")
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 转换为分类标签
        n_classes = len(self.label_encoder.classes_)
        y_categorical = keras.utils.to_categorical(y_encoded, n_classes)
        
        print(f"✅ 预处理完成:")
        print(f"   输入形状: {X_processed.shape}")
        print(f"   标签形状: {y_categorical.shape}")
        print(f"   类别数: {n_classes}")
        
        return X_processed, y_categorical
    
    def build_model(self, n_classes):
        """构建CPU优化的深度学习模型"""
        print(f"\n🏗️ 构建CPU优化模型 (类别数: {n_classes})...")
        
        # 使用CPU设备
        with tf.device('/CPU:0'):
            # 输入层
            input_layer = layers.Input(shape=(self.n_channels, self.n_samples, 1))
            
            # 第一个卷积块 - 时间特征提取
            x = layers.Conv2D(32, (1, 7), padding='same', activation='relu')(input_layer)
            x = layers.BatchNormalization()(x)
            x = layers.Conv2D(32, (self.n_channels, 1), activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.25)(x)
            
            # 第二个卷积块 - 深层特征
            x = layers.Conv2D(64, (1, 7), padding='same', activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.MaxPooling2D((1, 4))(x)
            x = layers.Dropout(0.25)(x)
            
            # 第三个卷积块
            x = layers.Conv2D(128, (1, 7), padding='same', activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.MaxPooling2D((1, 4))(x)
            x = layers.Dropout(0.25)(x)
            
            # 全局平均池化
            x = layers.GlobalAveragePooling2D()(x)
            
            # 全连接层
            x = layers.Dense(256, activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.5)(x)
            
            x = layers.Dense(128, activation='relu')(x)
            x = layers.BatchNormalization()(x)
            x = layers.Dropout(0.5)(x)
            
            # 输出层
            output = layers.Dense(n_classes, activation='softmax')(x)
            
            # 创建模型
            model = keras.Model(inputs=input_layer, outputs=output)
            
            # 编译模型
            model.compile(
                optimizer=keras.optimizers.Adam(learning_rate=0.001),
                loss='categorical_crossentropy',
                metrics=['accuracy', 'precision', 'recall']
            )
        
        print("✅ CPU优化模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model

    def train_model(self, epochs=80, batch_size=16):
        """训练模型 (CPU优化参数)"""
        print(f"\n🚀 开始CPU模型训练...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        print("⏰ CPU训练较慢，请耐心等待...")

        # 加载数据
        X_train, y_train = self.load_eeg_data('train')
        X_val, y_val = self.load_eeg_data('val')

        # 预处理数据
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)

        # 添加通道维度
        X_train = np.expand_dims(X_train, axis=-1)
        X_val = np.expand_dims(X_val, axis=-1)

        # 构建模型
        n_classes = y_train.shape[1]
        self.model = self.build_model(n_classes)

        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_eeg_cpu_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]

        # 训练模型
        print("🎯 开始CPU训练...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )

        print("✅ CPU训练完成!")

        # 保存模型
        self.model.save('final_eeg_cpu_model.h5')
        print("💾 CPU模型已保存: final_eeg_cpu_model.h5")

        # 保存标签编码器
        import joblib
        joblib.dump(self.label_encoder, 'eeg_cpu_label_encoder.pkl')
        print("💾 标签编码器已保存: eeg_cpu_label_encoder.pkl")

        return self.history

    def evaluate_model(self):
        """评估模型"""
        print("\n📊 评估CPU模型...")

        # 加载测试数据
        X_test, y_test = self.load_eeg_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        X_test = np.expand_dims(X_test, axis=-1)

        # 预测
        y_pred_prob = self.model.predict(X_test)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)

        # 计算指标
        test_loss, test_acc, test_prec, test_rec = self.model.evaluate(X_test, y_test_cat, verbose=0)

        print(f"📈 CPU模型测试结果:")
        print(f"   准确率: {test_acc:.4f}")
        print(f"   精确率: {test_prec:.4f}")
        print(f"   召回率: {test_rec:.4f}")
        print(f"   损失: {test_loss:.4f}")

        # 分类报告
        class_names = self.label_encoder.classes_
        report = classification_report(y_true, y_pred, target_names=class_names)
        print(f"\n📋 详细分类报告:")
        print(report)

        # 混淆矩阵
        cm = confusion_matrix(y_true, y_pred)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix_cpu.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 保存结果
        results = {
            'accuracy': test_acc,
            'precision': test_prec,
            'recall': test_rec,
            'loss': test_loss,
            'classification_report': report
        }

        import joblib
        joblib.dump(results, 'cpu_model_results.pkl')
        print("💾 评估结果已保存: cpu_model_results.pkl")

        return results

    def plot_training_history(self):
        """绘制训练历史"""
        if self.history is None:
            print("❌ 没有训练历史")
            return

        print("\n📈 绘制训练历史...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 准确率
        axes[0, 0].plot(self.history.history['accuracy'], label='训练准确率')
        axes[0, 0].plot(self.history.history['val_accuracy'], label='验证准确率')
        axes[0, 0].set_title('模型准确率')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # 损失
        axes[0, 1].plot(self.history.history['loss'], label='训练损失')
        axes[0, 1].plot(self.history.history['val_loss'], label='验证损失')
        axes[0, 1].set_title('模型损失')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('损失')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # 精确率
        axes[1, 0].plot(self.history.history['precision'], label='训练精确率')
        axes[1, 0].plot(self.history.history['val_precision'], label='验证精确率')
        axes[1, 0].set_title('模型精确率')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('精确率')
        axes[1, 0].legend()
        axes[1, 0].grid(True)

        # 召回率
        axes[1, 1].plot(self.history.history['recall'], label='训练召回率')
        axes[1, 1].plot(self.history.history['val_recall'], label='验证召回率')
        axes[1, 1].set_title('模型召回率')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('召回率')
        axes[1, 1].legend()
        axes[1, 1].grid(True)

        plt.tight_layout()
        plt.savefig('training_history_cpu.png', dpi=300, bbox_inches='tight')
        plt.show()


def main():
    """主函数"""
    print("🧠 科学EEG CPU训练系统")
    print("💻 专为CPU环境优化，避免GPU兼容性问题")
    print("=" * 60)

    # 创建训练器
    trainer = ScientificEEGTrainer()

    # 训练模型 (CPU优化参数)
    history = trainer.train_model(epochs=80, batch_size=16)

    # 绘制训练历史
    trainer.plot_training_history()

    # 评估模型
    results = trainer.evaluate_model()

    print("\n🎉 CPU训练完成!")
    print(f"📊 最终准确率: {results['accuracy']:.4f}")
    print(f"\n📁 输出文件:")
    print(f"   🎯 final_eeg_cpu_model.h5 - CPU训练模型")
    print(f"   📊 cpu_model_results.pkl - 评估结果")
    print(f"   📋 eeg_cpu_label_encoder.pkl - 标签编码器")
    print(f"   📈 training_history_cpu.png - 训练历史图")
    print(f"   📊 confusion_matrix_cpu.png - 混淆矩阵图")


if __name__ == "__main__":
    main()
