"""
Fast Complete Dataset Generator
Generate comprehensive speech-dementia datasets instantly
"""

import pandas as pd
import numpy as np
from pathlib import Path
import os

def generate_complete_speech_dataset():
    """Generate complete speech-dementia dataset"""
    
    print("Generating complete speech-dementia dataset...")
    
    np.random.seed(42)
    n_samples = 10000  # Large dataset
    
    # Create comprehensive feature set
    data = {}
    
    # Demographics
    data['subject_id'] = [f'SUB_{i+1:05d}' for i in range(n_samples)]
    data['age'] = np.random.normal(72, 12, n_samples).clip(50, 95)
    data['gender'] = np.random.choice([0, 1], n_samples, p=[0.55, 0.45])
    data['education_years'] = np.random.normal(13, 4, n_samples).clip(6, 20)
    data['native_language'] = np.random.choice([0, 1], n_samples, p=[0.8, 0.2])  # 0=English, 1=Other
    
    # Speech timing features
    data['speech_rate_wpm'] = np.random.normal(150, 40, n_samples).clip(80, 250)
    data['articulation_rate'] = np.random.normal(180, 50, n_samples).clip(100, 300)
    data['pause_frequency'] = np.random.normal(0.25, 0.08, n_samples).clip(0.1, 0.5)
    data['pause_duration_mean'] = np.random.normal(0.8, 0.3, n_samples).clip(0.2, 2.5)
    data['pause_duration_std'] = np.random.normal(0.4, 0.2, n_samples).clip(0.1, 1.2)
    data['silent_pause_ratio'] = np.random.normal(0.15, 0.05, n_samples).clip(0.05, 0.35)
    
    # Acoustic features
    data['f0_mean'] = np.random.normal(150, 50, n_samples).clip(80, 350)
    data['f0_std'] = np.random.normal(25, 12, n_samples).clip(8, 60)
    data['f0_range'] = np.random.normal(80, 30, n_samples).clip(30, 200)
    data['jitter_percent'] = np.random.normal(0.8, 0.4, n_samples).clip(0.2, 3.0)
    data['shimmer_percent'] = np.random.normal(3.5, 1.5, n_samples).clip(1.0, 8.0)
    data['hnr_db'] = np.random.normal(18, 6, n_samples).clip(8, 35)
    
    # Spectral features
    data['spectral_centroid'] = np.random.normal(2200, 600, n_samples).clip(1000, 4500)
    data['spectral_bandwidth'] = np.random.normal(1800, 500, n_samples).clip(800, 3500)
    data['spectral_rolloff'] = np.random.normal(3500, 800, n_samples).clip(2000, 6000)
    data['spectral_flux'] = np.random.normal(0.02, 0.01, n_samples).clip(0.005, 0.08)
    data['zero_crossing_rate'] = np.random.normal(0.08, 0.03, n_samples).clip(0.03, 0.15)
    
    # MFCC features (13 coefficients)
    for i in range(13):
        data[f'mfcc_{i+1}'] = np.random.normal(0, 2, n_samples)
    
    # Linguistic complexity features
    data['total_words'] = np.random.poisson(120, n_samples).clip(30, 300)
    data['unique_words'] = np.random.poisson(80, n_samples).clip(20, 200)
    data['type_token_ratio'] = data['unique_words'] / data['total_words']
    data['avg_word_length'] = np.random.normal(4.8, 0.8, n_samples).clip(3.5, 7.0)
    data['avg_sentence_length'] = np.random.normal(12, 4, n_samples).clip(5, 25)
    data['syntactic_complexity'] = np.random.normal(2.5, 0.8, n_samples).clip(1.0, 5.0)
    
    # Semantic features
    data['semantic_fluency'] = np.random.normal(18, 6, n_samples).clip(5, 35)
    data['phonemic_fluency'] = np.random.normal(15, 5, n_samples).clip(3, 30)
    data['category_switches'] = np.random.poisson(8, n_samples).clip(0, 20)
    data['semantic_errors'] = np.random.poisson(2, n_samples).clip(0, 15)
    
    # Discourse features
    data['information_units'] = np.random.poisson(25, n_samples).clip(8, 50)
    data['efficiency_ratio'] = np.random.normal(0.6, 0.2, n_samples).clip(0.2, 1.0)
    data['coherence_score'] = np.random.normal(7.5, 2.0, n_samples).clip(3, 10)
    data['topic_maintenance'] = np.random.normal(0.8, 0.15, n_samples).clip(0.4, 1.0)
    
    # Disfluency features
    data['filled_pauses'] = np.random.poisson(8, n_samples)
    data['unfilled_pauses'] = np.random.poisson(12, n_samples)
    data['repetitions'] = np.random.poisson(3, n_samples)
    data['revisions'] = np.random.poisson(4, n_samples)
    data['false_starts'] = np.random.poisson(2, n_samples)
    data['incomplete_words'] = np.random.poisson(1, n_samples)
    
    # Memory and cognitive load indicators
    data['word_finding_pauses'] = np.random.poisson(5, n_samples)
    data['circumlocutions'] = np.random.poisson(2, n_samples)
    data['empty_speech_ratio'] = np.random.normal(0.1, 0.05, n_samples).clip(0.02, 0.3)
    data['pronoun_noun_ratio'] = np.random.normal(0.4, 0.15, n_samples).clip(0.1, 0.8)
    
    # Create realistic diagnosis labels
    # Calculate composite risk score
    risk_factors = (
        (data['age'] - 50) / 45 * 0.25 +  # Age
        (200 - data['speech_rate_wpm']) / 120 * 0.15 +  # Speech rate
        data['pause_frequency'] * 0.15 +  # Pauses
        (data['filled_pauses'] / 20) * 0.1 +  # Disfluencies
        (25 - data['semantic_fluency']) / 20 * 0.15 +  # Semantic fluency
        (1.0 - data['efficiency_ratio']) * 0.1 +  # Efficiency
        (data['semantic_errors'] / 10) * 0.1  # Errors
    )
    
    # Add noise and individual variation
    risk_factors += np.random.normal(0, 0.1, n_samples)
    
    # Create three-class labels with realistic distribution
    labels = np.zeros(n_samples, dtype=int)
    labels[risk_factors > 0.65] = 2  # Dementia (~15%)
    labels[(risk_factors > 0.35) & (risk_factors <= 0.65)] = 1  # MCI (~25%)
    # Rest remain 0 (Normal ~60%)
    
    data['diagnosis'] = labels
    data['risk_score'] = risk_factors
    
    # Add diagnosis names
    diagnosis_names = ['Normal', 'MCI', 'Dementia']
    data['diagnosis_name'] = [diagnosis_names[label] for label in labels]
    
    # Convert to DataFrame
    df = pd.DataFrame(data)
    
    return df

def create_task_specific_datasets(df):
    """Create task-specific datasets"""
    
    base_path = Path(r"D:\模型开发")
    
    # 1. Cookie Theft Picture Description Task
    cookie_features = [
        'subject_id', 'age', 'gender', 'education_years',
        'speech_rate_wpm', 'pause_frequency', 'pause_duration_mean',
        'total_words', 'unique_words', 'type_token_ratio',
        'information_units', 'efficiency_ratio', 'coherence_score',
        'filled_pauses', 'repetitions', 'semantic_errors',
        'diagnosis', 'diagnosis_name'
    ]
    
    cookie_df = df[cookie_features].copy()
    cookie_df.to_csv(base_path / "cookie_theft_dataset.csv", index=False)
    
    # 2. Semantic Fluency Task
    fluency_features = [
        'subject_id', 'age', 'gender', 'education_years',
        'semantic_fluency', 'phonemic_fluency', 'category_switches',
        'semantic_errors', 'word_finding_pauses', 'circumlocutions',
        'diagnosis', 'diagnosis_name'
    ]
    
    fluency_df = df[fluency_features].copy()
    fluency_df.to_csv(base_path / "semantic_fluency_dataset.csv", index=False)
    
    # 3. Acoustic Analysis Dataset
    acoustic_features = [
        'subject_id', 'age', 'gender',
        'f0_mean', 'f0_std', 'f0_range', 'jitter_percent', 'shimmer_percent', 'hnr_db',
        'spectral_centroid', 'spectral_bandwidth', 'spectral_rolloff', 'zero_crossing_rate'
    ] + [f'mfcc_{i}' for i in range(1, 14)] + ['diagnosis', 'diagnosis_name']
    
    acoustic_df = df[acoustic_features].copy()
    acoustic_df.to_csv(base_path / "acoustic_features_dataset.csv", index=False)
    
    print("Task-specific datasets created:")
    print(f"  Cookie Theft: {len(cookie_df)} samples, {len(cookie_features)} features")
    print(f"  Semantic Fluency: {len(fluency_df)} samples, {len(fluency_features)} features")
    print(f"  Acoustic Features: {len(acoustic_df)} samples, {len(acoustic_features)} features")
    
    return cookie_df, fluency_df, acoustic_df

def create_train_val_test_splits(df):
    """Create stratified train/validation/test splits"""
    
    from sklearn.model_selection import train_test_split
    
    # First split: 80% train+val, 20% test
    train_val, test = train_test_split(
        df, test_size=0.2, random_state=42, 
        stratify=df['diagnosis']
    )
    
    # Second split: 75% train, 25% val (of the 80%)
    train, val = train_test_split(
        train_val, test_size=0.25, random_state=42,
        stratify=train_val['diagnosis']
    )
    
    base_path = Path(r"D:\模型开发")
    
    # Save splits
    train.to_csv(base_path / "speech_dementia_train.csv", index=False)
    val.to_csv(base_path / "speech_dementia_val.csv", index=False)
    test.to_csv(base_path / "speech_dementia_test.csv", index=False)
    
    # Save complete dataset
    df.to_csv(base_path / "speech_dementia_complete.csv", index=False)
    
    print(f"Dataset splits created:")
    print(f"  Train: {len(train)} samples ({len(train)/len(df)*100:.1f}%)")
    print(f"  Validation: {len(val)} samples ({len(val)/len(df)*100:.1f}%)")
    print(f"  Test: {len(test)} samples ({len(test)/len(df)*100:.1f}%)")
    
    # Print label distribution for each split
    for split_name, split_df in [("Train", train), ("Val", val), ("Test", test)]:
        print(f"\n{split_name} label distribution:")
        for label in [0, 1, 2]:
            count = (split_df['diagnosis'] == label).sum()
            name = ['Normal', 'MCI', 'Dementia'][label]
            print(f"    {name}: {count} ({count/len(split_df)*100:.1f}%)")
    
    return train, val, test

if __name__ == "__main__":
    print("Fast Complete Dataset Generator")
    print("=" * 50)
    
    # Create output directory
    os.makedirs(r"D:\模型开发", exist_ok=True)
    
    # Generate complete dataset
    print("Generating comprehensive speech-dementia dataset...")
    df = generate_complete_speech_dataset()
    
    print(f"\nComplete dataset generated:")
    print(f"  Samples: {len(df):,}")
    print(f"  Features: {len(df.columns)}")
    print(f"  Label distribution:")
    for label in [0, 1, 2]:
        count = (df['diagnosis'] == label).sum()
        name = ['Normal', 'MCI', 'Dementia'][label]
        print(f"    {name}: {count:,} ({count/len(df)*100:.1f}%)")
    
    # Create task-specific datasets
    print(f"\nCreating task-specific datasets...")
    cookie_df, fluency_df, acoustic_df = create_task_specific_datasets(df)
    
    # Create train/val/test splits
    print(f"\nCreating train/validation/test splits...")
    train, val, test = create_train_val_test_splits(df)
    
    print(f"\n✅ All datasets created successfully!")
    print(f"📁 Location: D:\\模型开发\\")
    print(f"\nAvailable datasets:")
    print(f"  📊 speech_dementia_complete.csv - Full dataset ({len(df):,} samples)")
    print(f"  🚂 speech_dementia_train.csv - Training set ({len(train):,} samples)")
    print(f"  ✅ speech_dementia_val.csv - Validation set ({len(val):,} samples)")
    print(f"  🧪 speech_dementia_test.csv - Test set ({len(test):,} samples)")
    print(f"  🍪 cookie_theft_dataset.csv - Cookie theft task")
    print(f"  🗣️ semantic_fluency_dataset.csv - Semantic fluency task")
    print(f"  🎵 acoustic_features_dataset.csv - Acoustic features")
    print(f"\n🚀 Ready for speech-based dementia detection training!")
