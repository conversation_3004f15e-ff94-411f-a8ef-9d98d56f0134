"""
Download DementiaBank dataset to cloud
Simple and fast download script
"""

import tensorflow_datasets as tfds
import os

def download_dementiabank():
    """Download DementiaBank dataset"""

    print("Starting DementiaBank dataset download...")

    try:
        # Set download directory
        download_dir = "./dementiabank_data"

        # Download dataset
        dataset, info = tfds.load(
            'dementiabank',
            with_info=True,
            download=True,
            data_dir=download_dir,
            as_supervised=False
        )

        print("Download completed!")
        print(f"Data saved to: {download_dir}")
        print(f"Dataset info:")
        print(f"   Description: {info.description}")
        print(f"   Features: {info.features}")
        print(f"   Splits: {info.splits}")

        # Preview data samples
        print("\nData sample preview:")
        for split_name, split_dataset in dataset.items():
            print(f"\n{split_name} split:")
            for i, example in enumerate(split_dataset.take(1)):
                print(f"   Sample structure: {example.keys()}")
                break

        return dataset, info

    except Exception as e:
        print(f"Download failed: {e}")
        print("Try manual install: pip install tensorflow-datasets")
        return None, None

def simple_download():
    """Simplest download method"""

    # Direct download
    ds = tfds.load('dementiabank', download=True)

    print("Simple download completed!")
    return ds

if __name__ == "__main__":
    print("DementiaBank Dataset Downloader")
    print("=" * 40)

    # Method 1: Detailed download
    dataset, info = download_dementiabank()

    # If failed, try simple download
    if dataset is None:
        print("\nTrying simple download...")
        dataset = simple_download()

    print("\nDownload completed!")
    print("Data saved locally, ready to use")
