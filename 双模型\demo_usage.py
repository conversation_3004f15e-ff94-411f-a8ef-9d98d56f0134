# -*- coding: utf-8 -*-
"""
双模型系统使用演示
展示如何在代码中使用双模型功能
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 设置环境变量
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

def load_models():
    """加载双模型"""
    print("🔄 正在加载双模型...")
    
    try:
        import tensorflow as tf
        from tensorflow.keras.preprocessing import image
        import numpy as np
        
        tf.get_logger().setLevel('ERROR')
        
        # 加载痴呆症分析模型
        dementia_model_path = r"D:\模型开发\ct_class.h5"
        ct_model_path = r"D:\模型开发\ct_other_model.h5"
        
        models = {}
        
        if os.path.exists(dementia_model_path):
            models['dementia'] = tf.keras.models.load_model(dementia_model_path)
            print("✅ 痴呆症分析模型加载成功")
        else:
            print("❌ 痴呆症分析模型未找到")
            
        if os.path.exists(ct_model_path):
            models['ct_detection'] = tf.keras.models.load_model(ct_model_path)
            print("✅ CT检测模型加载成功")
        else:
            print("❌ CT检测模型未找到")
            
        return models, tf, image, np
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None, None, None

def detect_ct_image(image_path, ct_model, image_module, np):
    """检测图像是否为CT图像"""
    try:
        # 加载和预处理图像
        img = image_module.load_img(image_path, target_size=(150, 150))
        img_array = image_module.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0)
        img_array = img_array / 255.0
        
        # 进行CT检测预测
        predictions = ct_model.predict(img_array, verbose=0)
        predicted_class = np.argmax(predictions, axis=1)
        confidence = max(predictions[0])
        
        ct_labels = ['CT图像', '非CT图像']  # 类别0=CT图像, 类别1=非CT图像

        return {
            'is_ct': predicted_class[0] == 0,  # 类别0表示CT图像
            'confidence': confidence,
            'class_name': ct_labels[predicted_class[0]],
            'probabilities': predictions[0].tolist()
        }
        
    except Exception as e:
        print(f"CT检测失败: {e}")
        return None

def analyze_dementia(image_path, dementia_model, image_module, np):
    """分析痴呆症"""
    try:
        # 类别标签
        class_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)',
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 加载和预处理图像
        img = image_module.load_img(image_path, target_size=(150, 150))
        img_array = image_module.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0)
        img_array = img_array / 255.0
        
        # 进行预测
        predictions = dementia_model.predict(img_array, verbose=0)
        predicted_class = np.argmax(predictions, axis=1)
        confidence = max(predictions[0])
        
        return {
            'predicted_class': predicted_class[0],
            'predicted_class_name': class_labels[predicted_class[0]],
            'confidence': confidence,
            'probabilities': predictions[0].tolist(),
            'class_labels': class_labels
        }
        
    except Exception as e:
        print(f"痴呆症分析失败: {e}")
        return None

def dual_model_analysis(image_path):
    """双模型完整分析流程"""
    print(f"\n🔍 开始分析图像: {os.path.basename(image_path)}")
    print("=" * 50)
    
    # 检查图像文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return None
    
    # 加载模型
    models, tf, image_module, np = load_models()
    if not models:
        print("❌ 模型加载失败，无法进行分析")
        return None
    
    results = {}
    
    # 第一步：CT图像检测
    if 'ct_detection' in models:
        print("\n🔍 步骤1: CT图像检测")
        ct_result = detect_ct_image(image_path, models['ct_detection'], image_module, np)
        if ct_result:
            results['ct_detection'] = ct_result
            print(f"   检测结果: {ct_result['class_name']}")
            print(f"   置信度: {ct_result['confidence']:.2%}")
            
            if not ct_result['is_ct']:
                print("   ⚠️ 警告: 该图像可能不是CT扫描图像")
                print("   ⚠️ 分析结果可能不准确")
        else:
            print("   ❌ CT检测失败")
    
    # 第二步：痴呆症分析
    if 'dementia' in models:
        print("\n🧠 步骤2: 痴呆症分析")
        dementia_result = analyze_dementia(image_path, models['dementia'], image_module, np)
        if dementia_result:
            results['dementia_analysis'] = dementia_result
            print(f"   分析结果: {dementia_result['predicted_class_name']}")
            print(f"   置信度: {dementia_result['confidence']:.2%}")
            
            print("\n   📊 详细概率分布:")
            for i, (label, prob) in enumerate(zip(dementia_result['class_labels'], dementia_result['probabilities'])):
                print(f"      {label}: {prob:.4f} ({prob:.2%})")
        else:
            print("   ❌ 痴呆症分析失败")
    
    print("\n" + "=" * 50)
    print("✅ 分析完成")
    
    return results

def main():
    """主函数"""
    print("🧠 双模型系统使用演示")
    print("=" * 50)
    
    # 示例：如果你有测试图像，可以在这里指定路径
    # 注意：请替换为你实际的图像路径
    test_image_path = input("请输入要分析的图像路径 (或按Enter跳过): ").strip()
    
    if test_image_path and os.path.exists(test_image_path):
        # 执行双模型分析
        results = dual_model_analysis(test_image_path)
        
        if results:
            print("\n📋 分析结果总结:")
            
            if 'ct_detection' in results:
                ct_result = results['ct_detection']
                print(f"CT检测: {ct_result['class_name']} ({ct_result['confidence']:.2%})")
            
            if 'dementia_analysis' in results:
                dementia_result = results['dementia_analysis']
                print(f"痴呆症分析: {dementia_result['predicted_class_name']} ({dementia_result['confidence']:.2%})")
    else:
        print("⚠️ 未提供有效的图像路径，仅测试模型加载")
        models, _, _, _ = load_models()
        if models:
            print(f"✅ 成功加载 {len(models)} 个模型")
        else:
            print("❌ 模型加载失败")

if __name__ == "__main__":
    main()
