# -*- coding: utf-8 -*-
"""
双模型联用系统 - 核心检测器
Core detector for dual model system
"""

import os
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

try:
    import tensorflow as tf
    from tensorflow.keras.preprocessing import image
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

from .utils import ImageProcessor, ResultsManager

class DualModelDetector:
    """双模型检测器 - 核心分析引擎"""
    
    def __init__(self, 
                 ct_model_path: str = None,
                 classification_model_path: str = None,
                 ct_threshold: float = 0.5):
        """
        初始化双模型检测器
        
        Args:
            ct_model_path: CT图像识别模型路径
            classification_model_path: 症状分析模型路径
            ct_threshold: CT识别阈值
        """
        if not TF_AVAILABLE:
            raise ImportError("TensorFlow未安装，请运行: pip install tensorflow")
        
        # 设置TensorFlow日志级别
        tf.get_logger().setLevel('ERROR')
        
        # 模型路径
        self.ct_model_path = ct_model_path or r"D:\模型开发\ct_other_model.h5"
        self.classification_model_path = classification_model_path or r"D:\模型开发\ct_class.h5"
        
        # 模型实例
        self.ct_model = None
        self.classification_model = None
        
        # 配置参数
        self.ct_threshold = ct_threshold
        self.input_size = (150, 150)
        
        # 类别标签
        self.class_labels = [
            'MildDemented(轻度痴呆)',
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)',
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 工具类
        self.image_processor = ImageProcessor(self.input_size)
        self.results_manager = ResultsManager()
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
        # 加载模型
        self.load_models()
    
    def load_models(self):
        """加载双模型"""
        try:
            # 加载CT识别模型
            if os.path.exists(self.ct_model_path):
                self.ct_model = tf.keras.models.load_model(self.ct_model_path)
                self.logger.info(f"CT识别模型加载成功: {self.ct_model_path}")
            else:
                self.logger.error(f"CT模型文件不存在: {self.ct_model_path}")
                raise FileNotFoundError(f"CT模型文件不存在: {self.ct_model_path}")
            
            # 加载症状分析模型
            if os.path.exists(self.classification_model_path):
                self.classification_model = tf.keras.models.load_model(self.classification_model_path)
                self.logger.info(f"症状分析模型加载成功: {self.classification_model_path}")
            else:
                self.logger.error(f"症状分析模型文件不存在: {self.classification_model_path}")
                raise FileNotFoundError(f"症状分析模型文件不存在: {self.classification_model_path}")
                
        except Exception as e:
            self.logger.error(f"模型加载失败: {e}")
            raise
    
    def detect_ct_image(self, image_array: np.ndarray) -> Dict:
        """
        检测是否为CT图像
        
        Args:
            image_array: 预处理后的图像数组
            
        Returns:
            CT检测结果字典
        """
        try:
            if self.ct_model is None:
                raise ValueError("CT识别模型未加载")
            
            # 进行预测
            prediction = self.ct_model.predict(image_array, verbose=0)
            confidence = float(prediction[0][0])
            is_ct = confidence > self.ct_threshold
            
            return {
                'is_ct': is_ct,
                'confidence': confidence,
                'threshold': self.ct_threshold,
                'raw_prediction': prediction[0].tolist()
            }
            
        except Exception as e:
            self.logger.error(f"CT检测失败: {e}")
            raise
    
    def classify_symptoms(self, image_array: np.ndarray) -> Dict:
        """
        分析症状（痴呆症分类）
        
        Args:
            image_array: 预处理后的图像数组
            
        Returns:
            症状分析结果字典
        """
        try:
            if self.classification_model is None:
                raise ValueError("症状分析模型未加载")
            
            # 进行预测
            prediction = self.classification_model.predict(image_array, verbose=0)
            predicted_class_idx = np.argmax(prediction, axis=1)[0]
            confidence = float(np.max(prediction))
            probabilities = prediction[0].tolist()
            
            # 构建详细结果
            result = {
                'predicted_class': int(predicted_class_idx),
                'predicted_class_name': self.class_labels[predicted_class_idx],
                'confidence': confidence,
                'probabilities': probabilities,
                'probability_details': {}
            }
            
            # 添加每个类别的详细概率
            for i, prob in enumerate(probabilities):
                result['probability_details'][self.class_labels[i]] = {
                    'probability': float(prob),
                    'percentage': f"{prob*100:.2f}%"
                }
            
            return result
            
        except Exception as e:
            self.logger.error(f"症状分析失败: {e}")
            raise
    
    def analyze_image(self, image_path: str) -> Dict:
        """
        完整的图像分析流程
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            完整的分析结果字典
        """
        analysis_start = datetime.now()
        
        result = {
            'image_path': image_path,
            'timestamp': analysis_start.isoformat(),
            'ct_detection': None,
            'symptom_analysis': None,
            'analysis_completed': False,
            'processing_time': None,
            'error': None
        }
        
        try:
            self.logger.info(f"开始分析图像: {image_path}")
            
            # 步骤1: 图像预处理
            image_array = self.image_processor.preprocess_image(image_path)
            if image_array is None:
                raise ValueError("图像预处理失败")
            
            # 步骤2: CT图像检测
            ct_result = self.detect_ct_image(image_array)
            result['ct_detection'] = ct_result
            
            if not ct_result['is_ct']:
                # CT检测未通过，终止分析
                result['error'] = '输入图像不是CT图像'
                self.logger.warning(f"CT检测未通过: {image_path}")
                return result
            
            # 步骤3: 症状分析
            symptom_result = self.classify_symptoms(image_array)
            result['symptom_analysis'] = symptom_result
            result['analysis_completed'] = True
            
            self.logger.info(f"分析完成: {image_path}")
            
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"分析失败: {e}")
        
        finally:
            # 计算处理时间
            processing_time = (datetime.now() - analysis_start).total_seconds()
            result['processing_time'] = processing_time
        
        return result
    
    def batch_analyze(self, image_folder: str) -> List[Dict]:
        """
        批量分析图像
        
        Args:
            image_folder: 图像文件夹路径
            
        Returns:
            分析结果列表
        """
        if not os.path.exists(image_folder):
            raise FileNotFoundError(f"文件夹不存在: {image_folder}")
        
        # 获取所有图像文件
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        image_files = [
            f for f in os.listdir(image_folder) 
            if f.lower().endswith(image_extensions)
        ]
        
        if not image_files:
            raise ValueError(f"文件夹中没有找到图像文件: {image_folder}")
        
        self.logger.info(f"开始批量分析 {len(image_files)} 张图像")
        
        results = []
        for i, filename in enumerate(image_files, 1):
            file_path = os.path.join(image_folder, filename)
            self.logger.info(f"处理第 {i}/{len(image_files)} 张图像: {filename}")
            
            result = self.analyze_image(file_path)
            results.append(result)
        
        self.logger.info("批量分析完成")
        return results
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        info = {
            'ct_model': {
                'path': self.ct_model_path,
                'loaded': self.ct_model is not None,
                'input_shape': None,
                'output_shape': None,
                'parameters': None
            },
            'classification_model': {
                'path': self.classification_model_path,
                'loaded': self.classification_model is not None,
                'input_shape': None,
                'output_shape': None,
                'parameters': None
            },
            'configuration': {
                'ct_threshold': self.ct_threshold,
                'input_size': self.input_size,
                'class_labels': self.class_labels
            }
        }
        
        # 获取CT模型详细信息
        if self.ct_model:
            info['ct_model']['input_shape'] = str(self.ct_model.input_shape)
            info['ct_model']['output_shape'] = str(self.ct_model.output_shape)
            info['ct_model']['parameters'] = self.ct_model.count_params()
        
        # 获取症状分析模型详细信息
        if self.classification_model:
            info['classification_model']['input_shape'] = str(self.classification_model.input_shape)
            info['classification_model']['output_shape'] = str(self.classification_model.output_shape)
            info['classification_model']['parameters'] = self.classification_model.count_params()
        
        return info
    
    def save_results(self, results: List[Dict], output_path: str):
        """保存分析结果"""
        self.results_manager.save_results(results, output_path)
    
    def load_results(self, input_path: str) -> List[Dict]:
        """加载分析结果"""
        return self.results_manager.load_results(input_path)
