#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
双模型联用系统安装脚本
AI痴呆症识别器 - 双模型联用版 v2.0
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "双模型AI痴呆症识别系统"

# 读取requirements.txt
def read_requirements():
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        return [
            "tensorflow>=2.10.0",
            "customtkinter>=5.0.0",
            "Pillow>=9.0.0",
            "opencv-python>=4.6.0",
            "numpy>=1.21.0",
            "fpdf2>=2.5.0",
            "reportlab>=3.6.0"
        ]

setup(
    name="dual-model-dementia-detector",
    version="2.0.0",
    author="AI Medical Solutions",
    author_email="<EMAIL>",
    description="双模型AI痴呆症识别系统 - CT图像验证 + 症状智能分析",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/aimedical/dual-model-dementia-detector",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Healthcare Industry",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "gpu": ["tensorflow-gpu>=2.10.0"],
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.991"
        ],
        "full": [
            "scikit-image>=0.19.0",
            "imageio>=2.19.0",
            "seaborn>=0.11.0",
            "plotly>=5.0.0"
        ]
    },
    entry_points={
        "console_scripts": [
            "dual-model-detector=dual_model_detector.main:main",
            "dmd-gui=dual_model_detector.gui:run_gui",
            "dmd-cli=dual_model_detector.cli:run_cli",
        ],
    },
    include_package_data=True,
    package_data={
        "dual_model_detector": [
            "assets/*",
            "models/*.h5",
            "config/*.json",
            "templates/*.html"
        ],
    },
    zip_safe=False,
    keywords=[
        "AI", "医学影像", "痴呆症", "深度学习", "CT图像", 
        "医疗诊断", "机器学习", "神经网络", "图像识别"
    ],
    project_urls={
        "Bug Reports": "https://github.com/aimedical/dual-model-dementia-detector/issues",
        "Source": "https://github.com/aimedical/dual-model-dementia-detector",
        "Documentation": "https://dual-model-detector.readthedocs.io/",
    },
)
