# -*- coding: utf-8 -*-
"""
AI痴呆症识别程序 - 完美IDE版本
直接点击运行按钮即可使用
"""

import os
import warnings
import sys

# 设置环境变量来隐藏TensorFlow的日志信息（必须在导入TensorFlow之前设置）
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 隐藏INFO和WARNING信息
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'  # 关闭oneDNN优化信息

# 隐藏其他警告信息
warnings.filterwarnings('ignore')

def main():
    """主函数"""
    print("🧠 AI痴呆症识别程序")
    print("=" * 50)
    print(f"🐍 Python: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print(f"📍 解释器: {sys.executable}")
    print("=" * 50)
    
    # 检查并导入依赖
    try:
        print("📦 正在加载依赖库...")
        
        # 导入TensorFlow
        import tensorflow as tf
        from tensorflow.keras.preprocessing import image
        import numpy as np
        
        # 设置TensorFlow日志级别
        tf.get_logger().setLevel('ERROR')
        
        # 隐藏absl警告
        try:
            import absl.logging
            absl.logging.set_verbosity(absl.logging.ERROR)
        except:
            pass
        
        print(f"✅ TensorFlow {tf.__version__} 加载成功")
        print(f"✅ NumPy {np.__version__} 加载成功")
        
    except ImportError as e:
        print(f"❌ 依赖库导入失败: {e}")
        print("\n💡 解决方案:")
        print("1. 确保选择了正确的Python解释器")
        print("2. 或运行: pip install tensorflow")
        input("\n按Enter键退出...")
        return
    
    # 检查文件路径
    model_path = r"D:\模型开发\升级model.h5"
    image_path = r"D:\模型开发\1.jpg"
    
    print(f"\n📁 检查文件...")
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        print("💡 请确认模型文件路径是否正确")
        input("\n按Enter键退出...")
        return
    else:
        print(f"✅ 模型文件: {model_path}")
        
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        print("💡 请确认图片文件路径是否正确")
        input("\n按Enter键退出...")
        return
    else:
        print(f"✅ 图片文件: {image_path}")
    
    try:
        # 加载模型
        print(f"\n🤖 正在加载AI模型...")
        model = tf.keras.models.load_model(model_path)
        print("✅ 模型加载成功")
        
        # 类别标签映射
        class_labels = [
            'MildDemented(轻度痴呆)', 
            'ModerateDemented(中度痴呆)', 
            'NonDemented(无痴呆)', 
            'VeryMildDemented(非常轻度痴呆)'
        ]
        
        # 图像预处理函数
        def load_and_preprocess_image(img_path):
            """加载和预处理图像"""
            img = image.load_img(img_path, target_size=(150, 150))
            img_array = image.img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0
            return img_array
        
        # 处理图像
        print(f"🖼️ 正在处理图像...")
        processed_image = load_and_preprocess_image(image_path)
        print("✅ 图像处理完成")
        
        # 进行预测
        print(f"🔮 正在进行AI预测...")
        predictions = model.predict(processed_image, verbose=0)  # verbose=0 隐藏进度条
        predicted_class = np.argmax(predictions, axis=1)
        prediction_probs = predictions[0].tolist()
        predicted_class_name = class_labels[predicted_class[0]]
        confidence = max(prediction_probs)
        
        # 显示结果
        print("\n" + "=" * 50)
        print("🎯 AI预测结果")
        print("=" * 50)
        print(f"📊 预测类别: {predicted_class_name}")
        print(f"🎯 置信度: {confidence:.2%}")
        
        print(f"\n📈 详细概率分布:")
        for i, prob in enumerate(prediction_probs):
            bar_length = int(prob * 20)  # 创建简单的进度条
            bar = "█" * bar_length + "░" * (20 - bar_length)
            print(f"   {class_labels[i]:<25} {bar} {prob:.4f} ({prob:.2%})")
        
        print("\n" + "=" * 50)
        print("✅ 预测完成！")
        
        # 添加结果解释
        print(f"\n📝 结果解释:")
        if "NonDemented" in predicted_class_name:
            print("   🟢 检测结果显示无痴呆症状")
        elif "VeryMild" in predicted_class_name:
            print("   🟡 检测到非常轻度的痴呆症状，建议进一步检查")
        elif "Mild" in predicted_class_name:
            print("   🟠 检测到轻度痴呆症状，建议咨询医生")
        elif "Moderate" in predicted_class_name:
            print("   🔴 检测到中度痴呆症状，建议及时就医")
        
        print(f"   ⚠️  注意：此结果仅供参考，不能替代专业医学诊断")
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("💡 请检查模型文件和图片文件是否正确")
        import traceback
        print(f"\n详细错误信息:")
        traceback.print_exc()
    
    # 等待用户输入（防止窗口立即关闭）
    print(f"\n" + "=" * 50)
    input("🎉 程序运行完成，按Enter键退出...")

if __name__ == "__main__":
    main()
