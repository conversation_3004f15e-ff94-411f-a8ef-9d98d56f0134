"""
查找并读取EEG信息文件
"""

import os
import glob

def find_and_read_eeg_info():
    """查找并读取EEG信息文件"""
    print("🔍 查找EEG信息文件...")
    
    # 可能的文件名模式
    patterns = [
        "*EEG_extracted*",
        "*目录详细信息*",
        "*Untitled*",
        "*.txt",
        "*.md"
    ]
    
    found_files = []
    
    # 在当前目录及子目录中搜索
    for pattern in patterns:
        files = glob.glob(pattern, recursive=False)
        found_files.extend(files)
    
    # 去重
    found_files = list(set(found_files))
    
    print(f"📁 找到的相关文件:")
    for i, file in enumerate(found_files, 1):
        file_size = os.path.getsize(file) if os.path.exists(file) else 0
        print(f"   {i}. {file} ({file_size} bytes)")
    
    # 查找最可能的文件
    target_file = None
    for file in found_files:
        if "EEG" in file and ("目录" in file or "extracted" in file):
            target_file = file
            break
    
    if not target_file and found_files:
        # 选择最大的文件（可能包含最多信息）
        target_file = max(found_files, key=lambda f: os.path.getsize(f) if os.path.exists(f) else 0)
    
    if target_file:
        print(f"\n✅ 选择文件: {target_file}")
        print("=" * 80)
        
        try:
            with open(target_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(content)
            
        except UnicodeDecodeError:
            try:
                with open(target_file, 'r', encoding='gbk') as f:
                    content = f.read()
                print(content)
            except:
                print("❌ 文件编码读取失败")
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    else:
        print("❌ 未找到EEG信息文件")
        print("\n📁 当前目录所有文件:")
        for file in os.listdir('.'):
            if os.path.isfile(file):
                size = os.path.getsize(file)
                print(f"   📄 {file} ({size} bytes)")

if __name__ == "__main__":
    find_and_read_eeg_info()
