"""
🧠 修复版AutoDL科学EEG数据集创建器
专门为autodl云端环境设计，修复标签处理问题
"""

import os
import json
import pandas as pd
import numpy as np
from collections import Counter
import shutil
from sklearn.model_selection import train_test_split

class FixedAutoEEGCreator:
    """修复版AutoDL EEG数据集创建器"""
    
    def __init__(self):
        self.base_path = "/root/EEG_extracted"
        self.output_path = "/root/Scientific_EEG_Datasets"
        
        if os.path.exists(self.output_path):
            shutil.rmtree(self.output_path)
        os.makedirs(self.output_path, exist_ok=True)
        
        print("🧠 修复版AutoDL科学EEG数据集创建器")
        print("=" * 60)
        print(f"📁 源数据路径: {self.base_path}")
        print(f"📁 输出路径: {self.output_path}")
    
    def discover_eeg_data(self):
        """发现EEG数据"""
        print("\n🔍 发现EEG数据...")
        
        if not os.path.exists(self.base_path):
            print(f"❌ 源数据目录不存在: {self.base_path}")
            return None
        
        # 查找所有.set文件
        set_files = []
        for root, dirs, files in os.walk(self.base_path):
            for file in files:
                if file.endswith('.set'):
                    full_path = os.path.join(root, file)
                    set_files.append(full_path)
        
        print(f"📁 找到 {len(set_files)} 个.set文件")
        
        # 分析文件结构
        subjects_info = {}
        for set_file in set_files:
            filename = os.path.basename(set_file)
            
            if filename.startswith('sub-'):
                subject_id = filename.split('_')[0]  # sub-XXX
                
                if subject_id not in subjects_info:
                    subjects_info[subject_id] = {
                        'id': subject_id,
                        'files': [],
                        'paths': []
                    }
                
                subjects_info[subject_id]['files'].append(filename)
                subjects_info[subject_id]['paths'].append(set_file)
        
        print(f"📊 识别出 {len(subjects_info)} 个受试者")
        return subjects_info
    
    def load_participants_data(self):
        """加载participants.tsv数据"""
        print("\n📋 加载participants.tsv...")
        
        # 查找participants.tsv文件
        participants_file = None
        for root, dirs, files in os.walk(self.base_path):
            for file in files:
                if file == 'participants.tsv':
                    participants_file = os.path.join(root, file)
                    break
            if participants_file:
                break
        
        if not participants_file:
            print("❌ 未找到participants.tsv文件")
            return None
        
        try:
            df = pd.read_csv(participants_file, sep='\t')
            print(f"✅ 成功读取participants.tsv")
            print(f"   形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
            print(f"   前3行:")
            print(df.head(3))
            
            # 检查Group列的分布
            if 'Group' in df.columns:
                group_counts = df['Group'].value_counts()
                print(f"\n📊 Group分布:")
                for group, count in group_counts.items():
                    print(f"   {group}: {count} 个受试者")
            
            return df
            
        except Exception as e:
            print(f"❌ 读取participants.tsv失败: {e}")
            return None
    
    def create_training_dataset(self, participants_df, subjects_info):
        """创建训练数据集"""
        print("\n🔧 创建训练数据集...")
        
        if participants_df is None:
            print("❌ 没有participants数据")
            return None
        
        # 合并数据
        training_data = []
        
        for _, row in participants_df.iterrows():
            subject_id = row['participant_id']
            group = row['Group']
            
            # 检查是否有对应的EEG文件
            if subject_id in subjects_info:
                subject_info = subjects_info[subject_id]
                
                training_data.append({
                    'participant_id': subject_id,
                    'label': group,
                    'gender': row.get('Gender', 'Unknown'),
                    'age': row.get('Age', 0),
                    'mmse': row.get('MMSE', 0),
                    'file_count': len(subject_info['files']),
                    'files': subject_info['files'],
                    'paths': subject_info['paths']
                })
        
        df = pd.DataFrame(training_data)
        
        print(f"📊 训练数据集信息:")
        print(f"   总受试者数: {len(df)}")
        print(f"   标签分布:")
        
        label_counts = df['label'].value_counts()
        for label, count in label_counts.items():
            print(f"     {label}: {count} 个受试者")
        
        return df
    
    def create_splits(self, df):
        """创建数据集划分"""
        print("\n📊 创建数据集划分...")
        
        # 检查标签分布
        label_counts = df['label'].value_counts()
        min_samples = label_counts.min()
        
        print(f"📈 数据集统计:")
        print(f"   总样本数: {len(df)}")
        print(f"   类别数: {len(label_counts)}")
        print(f"   最少类别样本数: {min_samples}")
        
        # 分层划分
        if min_samples >= 3:
            print("✅ 使用分层划分")
            train_df, temp_df = train_test_split(
                df, test_size=0.4, random_state=42, stratify=df['label']
            )
            val_df, test_df = train_test_split(
                temp_df, test_size=0.5, random_state=42, stratify=temp_df['label']
            )
        else:
            print("⚠️ 使用随机划分")
            train_df, temp_df = train_test_split(df, test_size=0.4, random_state=42)
            val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)
        
        splits = {
            'train': train_df,
            'val': val_df,
            'test': test_df
        }
        
        # 显示结果
        print(f"\n📊 划分结果:")
        total_samples = len(df)
        
        for split_name, split_df in splits.items():
            percentage = len(split_df) / total_samples * 100
            print(f"   {split_name.upper()}集: {len(split_df)} 个样本 ({percentage:.1f}%)")
            
            split_counts = split_df['label'].value_counts()
            for label, count in split_counts.items():
                print(f"     {label}: {count} 个")
        
        return splits
    
    def save_datasets(self, splits):
        """保存数据集"""
        print("\n💾 保存数据集...")
        
        for split_name, split_df in splits.items():
            split_dir = os.path.join(self.output_path, split_name)
            os.makedirs(split_dir, exist_ok=True)
            
            print(f"\n📂 保存 {split_name.upper()} 集...")
            
            # 保存标签文件
            labels_file = os.path.join(split_dir, 'labels.txt')
            with open(labels_file, 'w', encoding='utf-8') as f:
                f.write("subject_id\tlabel\n")
                for _, row in split_df.iterrows():
                    f.write(f"{row['participant_id']}\t{row['label']}\n")
            
            print(f"   ✅ 标签文件: {labels_file}")
            
            # 复制EEG文件
            copied_count = 0
            for _, row in split_df.iterrows():
                if row['paths']:
                    # 选择预处理过的文件（通常在derivatives目录下）
                    best_file = None
                    for path in row['paths']:
                        if 'derivatives' in path:
                            best_file = path
                            break
                    
                    if best_file is None:
                        best_file = row['paths'][0]  # 使用第一个文件
                    
                    dst_file = os.path.join(split_dir, os.path.basename(best_file))
                    
                    try:
                        shutil.copy2(best_file, dst_file)
                        copied_count += 1
                    except Exception as e:
                        print(f"     ⚠️ 复制失败 {os.path.basename(best_file)}: {e}")
            
            print(f"   ✅ 复制EEG文件: {copied_count}/{len(split_df)} 个")
            
            # 保存详细信息
            dataset_info = {
                'split_name': split_name,
                'total_subjects': len(split_df),
                'label_distribution': split_df['label'].value_counts().to_dict(),
                'subjects': split_df['participant_id'].tolist(),
                'creation_method': 'stratified_split',
                'random_seed': 42
            }
            
            info_file = os.path.join(split_dir, 'dataset_info.json')
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ 信息文件: {info_file}")
        
        # 保存总体描述
        overall_info = {
            'dataset_name': 'Fixed AutoDL EEG Dataset',
            'creation_date': pd.Timestamp.now().isoformat(),
            'total_subjects': len(pd.concat(splits.values())),
            'splits': {
                split_name: {
                    'count': len(split_df),
                    'label_distribution': split_df['label'].value_counts().to_dict()
                }
                for split_name, split_df in splits.items()
            },
            'data_source': self.base_path
        }
        
        overall_file = os.path.join(self.output_path, 'dataset_description.json')
        with open(overall_file, 'w', encoding='utf-8') as f:
            json.dump(overall_info, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 总体描述: {overall_file}")
        return overall_info
    
    def run_creation(self):
        """运行完整创建流程"""
        print("🚀 开始修复版EEG数据集创建")
        print("=" * 60)
        
        try:
            # 1. 发现EEG数据
            subjects_info = self.discover_eeg_data()
            if not subjects_info:
                return False
            
            # 2. 加载participants数据
            participants_df = self.load_participants_data()
            if participants_df is None:
                return False
            
            # 3. 创建训练数据集
            df = self.create_training_dataset(participants_df, subjects_info)
            if df is None or len(df) == 0:
                return False
            
            # 4. 创建划分
            splits = self.create_splits(df)
            
            # 5. 保存数据集
            overall_info = self.save_datasets(splits)
            
            print(f"\n🎉 修复版EEG数据集创建完成!")
            print("=" * 60)
            print(f"📁 输出目录: {self.output_path}")
            print(f"📊 总样本数: {overall_info['total_subjects']}")
            print(f"🔬 方法: 分层划分")
            print(f"☁️ 平台: AutoDL云端")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🧠 修复版AutoDL科学EEG数据集创建系统")
    print()
    
    creator = FixedAutoEEGCreator()
    success = creator.run_creation()
    
    if success:
        print("\n🏆 数据集创建成功!")
        print("📋 可以开始训练EEG模型了")
    else:
        print("\n❌ 创建失败")


if __name__ == "__main__":
    main()
