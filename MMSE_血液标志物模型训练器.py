"""
🧠 MMSE认知评估 & 🩸 血液标志物模型训练器
===============================================

功能说明:
1. 基于ADNI数据集训练MMSE认知评估模型
2. 基于血液生物标志物训练痴呆症风险预测模型
3. 支持多种机器学习算法
4. 提供完整的模型评估和保存功能

作者: AI医学团队
版本: v1.0
更新日期: 2024-06-23
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score
import joblib
import warnings
warnings.filterwarnings('ignore')

class MMSEModelTrainer:
    """MMSE认知评估模型训练器"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # MMSE评估项目
        self.mmse_features = [
            'orientation_time',      # 时间定向力 (0-5)
            'orientation_place',     # 地点定向力 (0-5)
            'registration',          # 即时记忆 (0-3)
            'attention',            # 注意力和计算 (0-5)
            'recall',               # 延迟回忆 (0-3)
            'language',             # 语言能力 (0-8)
            'visuospatial',         # 视空间能力 (0-1)
            'total_score',          # 总分 (0-30)
            'age',                  # 年龄
            'education_years',      # 教育年限
            'gender'                # 性别 (0=女, 1=男)
        ]
        
        # 认知状态标签
        self.cognitive_labels = ['正常', '轻度认知障碍', '痴呆']
    
    def create_synthetic_mmse_data(self, n_samples=2000):
        """
        创建合成MMSE数据集
        (实际使用时应该用ADNI真实数据)
        """
        print("📊 创建合成MMSE数据集...")
        
        np.random.seed(42)
        data = []
        
        for i in range(n_samples):
            # 随机生成基础信息
            age = np.random.normal(75, 10)
            age = max(50, min(95, age))  # 限制年龄范围
            
            education = np.random.normal(14, 3)
            education = max(8, min(20, education))
            
            gender = np.random.choice([0, 1])
            
            # 根据年龄和教育水平影响认知状态
            cognitive_decline_risk = (age - 65) / 30 + (16 - education) / 8
            cognitive_decline_risk = max(0, min(1, cognitive_decline_risk))
            
            # 确定认知状态
            if cognitive_decline_risk < 0.3:
                cognitive_status = 0  # 正常
                base_score = np.random.normal(28, 1.5)
            elif cognitive_decline_risk < 0.6:
                cognitive_status = 1  # 轻度认知障碍
                base_score = np.random.normal(24, 2)
            else:
                cognitive_status = 2  # 痴呆
                base_score = np.random.normal(18, 3)
            
            base_score = max(0, min(30, base_score))
            
            # 生成各项MMSE评分
            # 时间定向力
            orientation_time = min(5, max(0, int(base_score * 5/30 + np.random.normal(0, 0.5))))
            
            # 地点定向力
            orientation_place = min(5, max(0, int(base_score * 5/30 + np.random.normal(0, 0.5))))
            
            # 即时记忆
            registration = min(3, max(0, int(base_score * 3/30 + np.random.normal(0, 0.3))))
            
            # 注意力和计算
            attention = min(5, max(0, int(base_score * 5/30 + np.random.normal(0, 0.8))))
            
            # 延迟回忆
            recall = min(3, max(0, int(base_score * 3/30 + np.random.normal(0, 0.5))))
            
            # 语言能力
            language = min(8, max(0, int(base_score * 8/30 + np.random.normal(0, 1))))
            
            # 视空间能力
            visuospatial = 1 if base_score > 20 and np.random.random() > 0.3 else 0
            
            # 计算总分
            total_score = orientation_time + orientation_place + registration + attention + recall + language + visuospatial
            
            data.append({
                'orientation_time': orientation_time,
                'orientation_place': orientation_place,
                'registration': registration,
                'attention': attention,
                'recall': recall,
                'language': language,
                'visuospatial': visuospatial,
                'total_score': total_score,
                'age': age,
                'education_years': education,
                'gender': gender,
                'cognitive_status': cognitive_status
            })
        
        df = pd.DataFrame(data)
        
        print(f"✅ 数据集创建完成:")
        print(f"   样本数: {len(df)}")
        print(f"   特征数: {len(self.mmse_features)}")
        print(f"   认知状态分布:")
        for i, label in enumerate(self.cognitive_labels):
            count = len(df[df['cognitive_status'] == i])
            print(f"     {label}: {count} ({count/len(df)*100:.1f}%)")
        
        return df
    
    def preprocess_data(self, df):
        """预处理数据"""
        print("🔧 预处理MMSE数据...")
        
        # 准备特征和标签
        X = df[self.mmse_features].copy()
        y = df['cognitive_status'].copy()
        
        # 标准化数值特征
        numerical_features = [col for col in self.mmse_features if col != 'gender']
        X[numerical_features] = self.scaler.fit_transform(X[numerical_features])
        
        print(f"✅ 数据预处理完成:")
        print(f"   特征形状: {X.shape}")
        print(f"   标签形状: {y.shape}")
        
        return X, y
    
    def train_model(self, X, y):
        """训练MMSE模型"""
        print("🚀 开始训练MMSE认知评估模型...")
        
        # 划分数据集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 定义候选模型
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'GradientBoosting': GradientBoostingClassifier(random_state=42),
            'SVM': SVC(probability=True, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        best_model = None
        best_score = 0
        best_name = ""
        
        # 训练和评估每个模型
        for name, model in models.items():
            print(f"\n📊 训练 {name}...")
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
            mean_cv_score = cv_scores.mean()
            
            print(f"   交叉验证准确率: {mean_cv_score:.4f} (±{cv_scores.std()*2:.4f})")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 测试集评估
            test_score = model.score(X_test, y_test)
            print(f"   测试集准确率: {test_score:.4f}")
            
            # 选择最佳模型
            if test_score > best_score:
                best_score = test_score
                best_model = model
                best_name = name
        
        print(f"\n🏆 最佳模型: {best_name} (准确率: {best_score:.4f})")
        
        self.model = best_model
        
        # 详细评估
        self.evaluate_model(X_test, y_test)
        
        return best_model
    
    def evaluate_model(self, X_test, y_test):
        """评估模型性能"""
        print("\n📊 模型性能评估:")
        
        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)
        
        # 准确率
        accuracy = accuracy_score(y_test, y_pred)
        print(f"   准确率: {accuracy:.4f}")
        
        # 分类报告
        print("\n📋 详细分类报告:")
        report = classification_report(y_test, y_pred, target_names=self.cognitive_labels)
        print(report)
        
        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=self.cognitive_labels,
                   yticklabels=self.cognitive_labels)
        plt.title('MMSE模型混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('mmse_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.plot_feature_importance()
    
    def plot_feature_importance(self):
        """绘制特征重要性"""
        importance = self.model.feature_importances_
        feature_names = self.mmse_features
        
        # 排序
        indices = np.argsort(importance)[::-1]
        
        plt.figure(figsize=(10, 6))
        plt.title('MMSE特征重要性')
        plt.bar(range(len(importance)), importance[indices])
        plt.xticks(range(len(importance)), [feature_names[i] for i in indices], rotation=45)
        plt.tight_layout()
        plt.savefig('mmse_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_model(self, filename='MMSE_classifier.pkl'):
        """保存模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.mmse_features,
            'class_labels': self.cognitive_labels
        }
        
        joblib.dump(model_data, filename)
        print(f"💾 MMSE模型已保存: {filename}")
    
    def predict(self, mmse_data):
        """预测认知状态"""
        if self.model is None:
            raise ValueError("模型未训练")
        
        # 预处理输入数据
        X = pd.DataFrame([mmse_data], columns=self.mmse_features)
        numerical_features = [col for col in self.mmse_features if col != 'gender']
        X[numerical_features] = self.scaler.transform(X[numerical_features])
        
        # 预测
        prediction = self.model.predict(X)[0]
        probabilities = self.model.predict_proba(X)[0]
        
        return {
            'predicted_class': prediction,
            'class_name': self.cognitive_labels[prediction],
            'probabilities': probabilities.tolist(),
            'confidence': max(probabilities)
        }


class BloodBiomarkerTrainer:
    """血液标志物模型训练器"""
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        
        # 血液标志物特征
        self.biomarker_features = [
            'amyloid_beta_42',       # Aβ42蛋白
            'amyloid_beta_40',       # Aβ40蛋白
            'amyloid_ratio',         # Aβ42/Aβ40比值
            'p_tau_181',             # 磷酸化Tau181
            'total_tau',             # 总Tau蛋白
            'neurofilament_light',   # 神经丝轻链
            'gfap',                  # 胶质纤维酸性蛋白
            'age',                   # 年龄
            'apoe_e4_count'          # APOE E4等位基因数量
        ]
        
        # 风险等级标签
        self.risk_labels = ['低风险', '中风险', '高风险']
    
    def create_synthetic_blood_data(self, n_samples=1500):
        """创建合成血液标志物数据集"""
        print("🩸 创建合成血液标志物数据集...")
        
        np.random.seed(42)
        data = []
        
        for i in range(n_samples):
            # 基础信息
            age = np.random.normal(72, 8)
            age = max(50, min(90, age))
            
            apoe_e4_count = np.random.choice([0, 1, 2], p=[0.6, 0.3, 0.1])
            
            # 根据年龄和APOE状态确定风险等级
            base_risk = (age - 50) / 40 + apoe_e4_count * 0.3
            
            if base_risk < 0.4:
                risk_level = 0  # 低风险
            elif base_risk < 0.7:
                risk_level = 1  # 中风险
            else:
                risk_level = 2  # 高风险
            
            # 根据风险等级生成生物标志物
            if risk_level == 0:  # 低风险
                amyloid_beta_42 = np.random.normal(1200, 200)
                amyloid_beta_40 = np.random.normal(15000, 2000)
                p_tau_181 = np.random.normal(15, 5)
                total_tau = np.random.normal(250, 50)
                neurofilament_light = np.random.normal(25, 8)
                gfap = np.random.normal(100, 30)
            elif risk_level == 1:  # 中风险
                amyloid_beta_42 = np.random.normal(800, 150)
                amyloid_beta_40 = np.random.normal(16000, 2500)
                p_tau_181 = np.random.normal(25, 8)
                total_tau = np.random.normal(350, 80)
                neurofilament_light = np.random.normal(40, 12)
                gfap = np.random.normal(150, 40)
            else:  # 高风险
                amyloid_beta_42 = np.random.normal(500, 100)
                amyloid_beta_40 = np.random.normal(18000, 3000)
                p_tau_181 = np.random.normal(40, 12)
                total_tau = np.random.normal(500, 120)
                neurofilament_light = np.random.normal(60, 20)
                gfap = np.random.normal(200, 50)
            
            # 确保数值为正
            amyloid_beta_42 = max(100, amyloid_beta_42)
            amyloid_beta_40 = max(5000, amyloid_beta_40)
            p_tau_181 = max(5, p_tau_181)
            total_tau = max(100, total_tau)
            neurofilament_light = max(10, neurofilament_light)
            gfap = max(50, gfap)
            
            # 计算比值
            amyloid_ratio = amyloid_beta_42 / amyloid_beta_40
            
            data.append({
                'amyloid_beta_42': amyloid_beta_42,
                'amyloid_beta_40': amyloid_beta_40,
                'amyloid_ratio': amyloid_ratio,
                'p_tau_181': p_tau_181,
                'total_tau': total_tau,
                'neurofilament_light': neurofilament_light,
                'gfap': gfap,
                'age': age,
                'apoe_e4_count': apoe_e4_count,
                'risk_level': risk_level
            })
        
        df = pd.DataFrame(data)
        
        print(f"✅ 血液标志物数据集创建完成:")
        print(f"   样本数: {len(df)}")
        print(f"   特征数: {len(self.biomarker_features)}")
        print(f"   风险等级分布:")
        for i, label in enumerate(self.risk_labels):
            count = len(df[df['risk_level'] == i])
            print(f"     {label}: {count} ({count/len(df)*100:.1f}%)")
        
        return df

    def train_blood_model(self, df):
        """训练血液标志物模型"""
        print("🚀 开始训练血液标志物模型...")

        # 准备数据
        X = df[self.biomarker_features].copy()
        y = df['risk_level'].copy()

        # 标准化特征
        X_scaled = self.scaler.fit_transform(X)

        # 划分数据集
        X_train, X_test, y_train, y_test = train_test_split(
            X_scaled, y, test_size=0.2, random_state=42, stratify=y
        )

        # 定义候选模型
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'GradientBoosting': GradientBoostingClassifier(random_state=42),
            'SVM': SVC(probability=True, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }

        best_model = None
        best_score = 0
        best_name = ""

        # 训练和评估每个模型
        for name, model in models.items():
            print(f"\n📊 训练 {name}...")

            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
            mean_cv_score = cv_scores.mean()

            print(f"   交叉验证准确率: {mean_cv_score:.4f} (±{cv_scores.std()*2:.4f})")

            # 训练模型
            model.fit(X_train, y_train)

            # 测试集评估
            test_score = model.score(X_test, y_test)
            print(f"   测试集准确率: {test_score:.4f}")

            # 选择最佳模型
            if test_score > best_score:
                best_score = test_score
                best_model = model
                best_name = name

        print(f"\n🏆 最佳血液标志物模型: {best_name} (准确率: {best_score:.4f})")

        self.model = best_model

        # 详细评估
        self.evaluate_blood_model(X_test, y_test)

        return best_model

    def evaluate_blood_model(self, X_test, y_test):
        """评估血液标志物模型"""
        print("\n📊 血液标志物模型性能评估:")

        # 预测
        y_pred = self.model.predict(X_test)
        y_pred_proba = self.model.predict_proba(X_test)

        # 准确率
        accuracy = accuracy_score(y_test, y_pred)
        print(f"   准确率: {accuracy:.4f}")

        # 分类报告
        print("\n📋 详细分类报告:")
        report = classification_report(y_test, y_pred, target_names=self.risk_labels)
        print(report)

        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)

        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Reds',
                   xticklabels=self.risk_labels,
                   yticklabels=self.risk_labels)
        plt.title('血液标志物模型混淆矩阵')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('blood_biomarker_confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 特征重要性
        if hasattr(self.model, 'feature_importances_'):
            self.plot_blood_feature_importance()

    def plot_blood_feature_importance(self):
        """绘制血液标志物特征重要性"""
        importance = self.model.feature_importances_
        feature_names = self.biomarker_features

        # 排序
        indices = np.argsort(importance)[::-1]

        plt.figure(figsize=(12, 6))
        plt.title('血液标志物特征重要性')
        plt.bar(range(len(importance)), importance[indices])
        plt.xticks(range(len(importance)), [feature_names[i] for i in indices], rotation=45)
        plt.tight_layout()
        plt.savefig('blood_biomarker_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

    def save_blood_model(self, filename='Blood_biomarker_classifier.pkl'):
        """保存血液标志物模型"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'feature_names': self.biomarker_features,
            'risk_labels': self.risk_labels
        }

        joblib.dump(model_data, filename)
        print(f"💾 血液标志物模型已保存: {filename}")

    def predict_blood_risk(self, biomarker_data):
        """预测血液标志物风险"""
        if self.model is None:
            raise ValueError("模型未训练")

        # 预处理输入数据
        X = pd.DataFrame([biomarker_data], columns=self.biomarker_features)
        X_scaled = self.scaler.transform(X)

        # 预测
        prediction = self.model.predict(X_scaled)[0]
        probabilities = self.model.predict_proba(X_scaled)[0]

        return {
            'predicted_risk': prediction,
            'risk_level': self.risk_labels[prediction],
            'probabilities': probabilities.tolist(),
            'confidence': max(probabilities)
        }


def main():
    """主训练流程"""
    print("🧠 MMSE认知评估 & 🩸 血液标志物模型训练系统")
    print("=" * 60)

    # 1. 训练MMSE模型
    print("\n📋 第一阶段: MMSE认知评估模型训练")
    print("-" * 40)

    mmse_trainer = MMSEModelTrainer()

    # 创建MMSE数据集
    mmse_df = mmse_trainer.create_synthetic_mmse_data(n_samples=2000)

    # 预处理数据
    X_mmse, y_mmse = mmse_trainer.preprocess_data(mmse_df)

    # 训练模型
    mmse_model = mmse_trainer.train_model(X_mmse, y_mmse)

    # 保存模型
    mmse_trainer.save_model('MMSE_classifier.pkl')

    # 2. 训练血液标志物模型
    print("\n🩸 第二阶段: 血液标志物模型训练")
    print("-" * 40)

    blood_trainer = BloodBiomarkerTrainer()

    # 创建血液标志物数据集
    blood_df = blood_trainer.create_synthetic_blood_data(n_samples=1500)

    # 训练模型
    blood_model = blood_trainer.train_blood_model(blood_df)

    # 保存模型
    blood_trainer.save_blood_model('Blood_biomarker_classifier.pkl')

    # 3. 模型测试
    print("\n🧪 第三阶段: 模型功能测试")
    print("-" * 40)

    # 测试MMSE模型
    test_mmse_data = {
        'orientation_time': 4,
        'orientation_place': 4,
        'registration': 3,
        'attention': 3,
        'recall': 2,
        'language': 6,
        'visuospatial': 1,
        'total_score': 23,
        'age': 75,
        'education_years': 12,
        'gender': 0
    }

    mmse_result = mmse_trainer.predict(test_mmse_data)
    print(f"🧠 MMSE测试结果:")
    print(f"   认知状态: {mmse_result['class_name']}")
    print(f"   置信度: {mmse_result['confidence']:.2%}")

    # 测试血液标志物模型
    test_blood_data = {
        'amyloid_beta_42': 800,
        'amyloid_beta_40': 16000,
        'amyloid_ratio': 0.05,
        'p_tau_181': 25,
        'total_tau': 350,
        'neurofilament_light': 45,
        'gfap': 160,
        'age': 72,
        'apoe_e4_count': 1
    }

    blood_result = blood_trainer.predict_blood_risk(test_blood_data)
    print(f"🩸 血液标志物测试结果:")
    print(f"   风险等级: {blood_result['risk_level']}")
    print(f"   置信度: {blood_result['confidence']:.2%}")

    print("\n🎉 模型训练完成!")
    print("=" * 60)
    print("📁 输出文件:")
    print("   🧠 MMSE_classifier.pkl - MMSE认知评估模型")
    print("   🩸 Blood_biomarker_classifier.pkl - 血液标志物模型")
    print("   📊 mmse_confusion_matrix.png - MMSE混淆矩阵")
    print("   📊 blood_biomarker_confusion_matrix.png - 血液标志物混淆矩阵")
    print("   📈 mmse_feature_importance.png - MMSE特征重要性")
    print("   📈 blood_biomarker_feature_importance.png - 血液标志物特征重要性")

    print("\n💡 使用建议:")
    print("1. 将生成的.pkl文件放入模型目录")
    print("2. 在五模态系统中加载这些模型")
    print("3. 使用真实的ADNI数据重新训练以获得更好性能")
    print("4. 定期更新模型以适应新的临床数据")


if __name__ == "__main__":
    main()
