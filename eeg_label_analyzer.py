"""
🏷️ EEG数据集标签分析器
专门用于分析和提取EEG数据集中的真实标签信息
"""

import os
import zipfile
import json
import pandas as pd
import numpy as np
from pathlib import Path

class EEGLabelAnalyzer:
    """EEG标签分析器"""
    
    def __init__(self, zip_path="D:/模型开发/EEG.zip"):
        self.zip_path = zip_path
        self.label_info = {}
        self.subjects_labels = {}
        
    def analyze_all_labels(self):
        """分析所有可能的标签来源"""
        print("🔍 EEG数据集标签分析报告")
        print("=" * 60)
        
        if not os.path.exists(self.zip_path):
            print(f"❌ 数据集文件不存在: {self.zip_path}")
            return
        
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zip_ref:
                file_list = zip_ref.namelist()
                
                print(f"📦 数据集文件总数: {len(file_list)}")
                
                # 1. 查找标签相关文件
                self.find_label_files(zip_ref, file_list)
                
                # 2. 分析JSON文件
                self.analyze_json_files(zip_ref, file_list)
                
                # 3. 分析TSV文件
                self.analyze_tsv_files(zip_ref, file_list)
                
                # 4. 分析文件名模式
                self.analyze_filename_patterns(file_list)
                
                # 5. 生成标签映射
                self.generate_label_mapping()
                
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    def find_label_files(self, zip_ref, file_list):
        """查找可能包含标签的文件"""
        print(f"\n📋 查找标签相关文件...")
        
        label_files = {
            'participants.tsv': [],
            'participants.json': [],
            'dataset_description.json': [],
            'README': [],
            'phenotype': [],
            'other_tsv': [],
            'other_json': []
        }
        
        for file_path in file_list:
            filename = os.path.basename(file_path).lower()
            
            if filename == 'participants.tsv':
                label_files['participants.tsv'].append(file_path)
            elif filename == 'participants.json':
                label_files['participants.json'].append(file_path)
            elif filename == 'dataset_description.json':
                label_files['dataset_description.json'].append(file_path)
            elif filename.startswith('readme'):
                label_files['README'].append(file_path)
            elif 'phenotype' in filename:
                label_files['phenotype'].append(file_path)
            elif filename.endswith('.tsv'):
                label_files['other_tsv'].append(file_path)
            elif filename.endswith('.json'):
                label_files['other_json'].append(file_path)
        
        print(f"🔍 发现的标签相关文件:")
        for file_type, files in label_files.items():
            if files:
                print(f"   {file_type}: {len(files)} 个")
                for file_path in files[:3]:  # 显示前3个
                    print(f"      📄 {file_path}")
                if len(files) > 3:
                    print(f"      ... 还有 {len(files)-3} 个")
        
        return label_files
    
    def analyze_json_files(self, zip_ref, file_list):
        """分析JSON文件中的标签信息"""
        print(f"\n📄 分析JSON文件...")
        
        json_files = [f for f in file_list if f.endswith('.json')]
        
        for json_file in json_files[:5]:  # 分析前5个JSON文件
            try:
                with zip_ref.open(json_file) as f:
                    content = json.load(f)
                
                print(f"\n📋 文件: {json_file}")
                print(f"   键值对数量: {len(content)}")
                
                # 查找可能的标签相关键
                label_keys = []
                for key in content.keys():
                    key_lower = key.lower()
                    if any(keyword in key_lower for keyword in 
                          ['group', 'diagnosis', 'condition', 'label', 'class', 
                           'patient', 'subject', 'participant', 'demographic']):
                        label_keys.append(key)
                
                if label_keys:
                    print(f"   🏷️ 可能的标签键: {label_keys}")
                    for key in label_keys:
                        print(f"      {key}: {content[key]}")
                else:
                    # 显示所有键
                    print(f"   📝 所有键: {list(content.keys())[:10]}")
                    if len(content) > 10:
                        print(f"      ... 还有 {len(content)-10} 个键")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
    
    def analyze_tsv_files(self, zip_ref, file_list):
        """分析TSV文件中的标签信息"""
        print(f"\n📊 分析TSV文件...")
        
        tsv_files = [f for f in file_list if f.endswith('.tsv')]
        
        for tsv_file in tsv_files:
            try:
                with zip_ref.open(tsv_file) as f:
                    # 读取前几行来分析结构
                    lines = f.read().decode('utf-8').split('\n')
                
                print(f"\n📋 文件: {tsv_file}")
                print(f"   行数: {len(lines)}")
                
                if lines:
                    # 分析表头
                    header = lines[0].split('\t')
                    print(f"   📝 列名: {header}")
                    
                    # 查找标签相关列
                    label_columns = []
                    for col in header:
                        col_lower = col.lower()
                        if any(keyword in col_lower for keyword in 
                              ['group', 'diagnosis', 'condition', 'label', 'class',
                               'patient_type', 'subject_type', 'age', 'sex', 'gender']):
                            label_columns.append(col)
                    
                    if label_columns:
                        print(f"   🏷️ 标签相关列: {label_columns}")
                    
                    # 显示前几行数据
                    if len(lines) > 1:
                        print(f"   📄 前3行数据:")
                        for i, line in enumerate(lines[1:4]):
                            if line.strip():
                                print(f"      行{i+1}: {line}")
                    
                    # 如果是participants.tsv，尝试解析完整数据
                    if 'participants.tsv' in tsv_file:
                        self.parse_participants_tsv(lines)
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
    
    def parse_participants_tsv(self, lines):
        """解析participants.tsv文件"""
        print(f"\n🎯 详细解析 participants.tsv...")
        
        try:
            # 创建DataFrame
            data = []
            header = lines[0].split('\t')
            
            for line in lines[1:]:
                if line.strip():
                    row = line.split('\t')
                    if len(row) == len(header):
                        data.append(row)
            
            if data:
                df = pd.DataFrame(data, columns=header)
                print(f"   📊 数据形状: {df.shape}")
                print(f"   📋 列信息:")
                
                for col in df.columns:
                    unique_values = df[col].unique()
                    print(f"      {col}: {len(unique_values)} 个唯一值")
                    if len(unique_values) <= 10:
                        print(f"         值: {list(unique_values)}")
                    else:
                        print(f"         示例: {list(unique_values[:5])} ...")
                
                # 尝试识别标签列
                self.identify_label_column(df)
                
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")
    
    def identify_label_column(self, df):
        """识别标签列"""
        print(f"\n🔍 识别标签列...")
        
        potential_label_cols = []
        
        for col in df.columns:
            col_lower = col.lower()
            unique_values = df[col].unique()
            
            # 检查是否是标签列的条件
            if (any(keyword in col_lower for keyword in 
                   ['group', 'diagnosis', 'condition', 'label', 'class']) or
                len(unique_values) <= 5 and len(unique_values) >= 2):
                
                potential_label_cols.append({
                    'column': col,
                    'unique_count': len(unique_values),
                    'values': list(unique_values),
                    'sample_data': df[col].head().tolist()
                })
        
        if potential_label_cols:
            print(f"   🏷️ 可能的标签列:")
            for col_info in potential_label_cols:
                print(f"      📋 {col_info['column']}:")
                print(f"         唯一值数量: {col_info['unique_count']}")
                print(f"         所有值: {col_info['values']}")
                print(f"         示例数据: {col_info['sample_data']}")
                
                # 尝试映射到疾病类型
                self.map_to_disease_types(col_info['column'], col_info['values'], df)
        else:
            print(f"   ⚠️ 未找到明显的标签列")
    
    def map_to_disease_types(self, column_name, unique_values, df):
        """映射到疾病类型"""
        print(f"\n🎯 尝试映射 '{column_name}' 到疾病类型...")
        
        disease_mapping = {}
        
        for value in unique_values:
            value_str = str(value).upper()
            
            if any(keyword in value_str for keyword in ['CN', 'CONTROL', 'HEALTHY', 'NORMAL', 'HC']):
                disease_mapping[value] = '健康对照 (CN)'
            elif any(keyword in value_str for keyword in ['AD', 'ALZHEIMER', 'DEMENTIA']):
                disease_mapping[value] = '阿尔茨海默病 (AD)'
            elif any(keyword in value_str for keyword in ['FTD', 'FRONTOTEMPORAL', 'FRONTAL']):
                disease_mapping[value] = '额颞叶痴呆 (FTD)'
            else:
                disease_mapping[value] = f'未知类型 ({value})'
        
        print(f"   🗺️ 疾病类型映射:")
        for original, mapped in disease_mapping.items():
            count = len(df[df[column_name] == original])
            print(f"      {original} → {mapped} ({count} 人)")
        
        # 保存映射结果
        self.subjects_labels[column_name] = {
            'mapping': disease_mapping,
            'data': df[[df.columns[0], column_name]].to_dict('records') if len(df.columns) > 0 else []
        }
    
    def analyze_filename_patterns(self, file_list):
        """分析文件名模式"""
        print(f"\n📁 分析文件名模式...")
        
        # 提取所有被试者ID
        subjects = set()
        for file_path in file_list:
            filename = os.path.basename(file_path)
            if 'sub-' in filename:
                # 提取sub-xxx格式
                parts = filename.split('sub-')
                if len(parts) > 1:
                    subject_part = parts[1].split('_')[0].split('.')[0]
                    if subject_part.isdigit() or (subject_part[:3].isdigit()):
                        subjects.add(f"sub-{subject_part}")
        
        subjects_list = sorted(list(subjects))
        print(f"   👥 发现被试者: {len(subjects_list)} 人")
        print(f"   📋 被试者范围: {subjects_list[0]} 到 {subjects_list[-1]}")
        
        # 分析可能的分组模式
        if len(subjects_list) == 88:
            print(f"\n🔍 基于88个被试者的可能分组:")
            print(f"   方案1 - 均匀分布:")
            print(f"      sub-001 到 sub-029: 健康对照 (29人)")
            print(f"      sub-030 到 sub-059: 阿尔茨海默病 (30人)")
            print(f"      sub-060 到 sub-088: 额颞叶痴呆 (29人)")
            
            print(f"\n   方案2 - 基于常见研究比例:")
            print(f"      sub-001 到 sub-030: 健康对照 (30人)")
            print(f"      sub-031 到 sub-065: 阿尔茨海默病 (35人)")
            print(f"      sub-066 到 sub-088: 额颞叶痴呆 (23人)")
    
    def generate_label_mapping(self):
        """生成最终的标签映射"""
        print(f"\n📋 生成标签映射建议...")
        
        if self.subjects_labels:
            print(f"✅ 基于TSV文件的标签映射:")
            for column, info in self.subjects_labels.items():
                print(f"   使用列: {column}")
                for original, mapped in info['mapping'].items():
                    print(f"      {original} → {mapped}")
        else:
            print(f"⚠️ 未找到明确的标签文件，建议:")
            print(f"   1. 检查是否有participants.tsv文件")
            print(f"   2. 查看README文件中的说明")
            print(f"   3. 联系数据集提供者获取标签信息")
            print(f"   4. 使用默认的编号分组方案")
    
    def save_analysis_result(self, save_path="D:/模型开发/eeg_label_analysis.json"):
        """保存分析结果"""
        result = {
            'analysis_summary': {
                'zip_path': self.zip_path,
                'has_participants_tsv': bool(self.subjects_labels),
                'label_columns_found': list(self.subjects_labels.keys()) if self.subjects_labels else []
            },
            'subjects_labels': self.subjects_labels
        }
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n💾 分析结果已保存: {save_path}")
        except Exception as e:
            print(f"\n❌ 保存失败: {e}")


def main():
    """主函数"""
    print("🏷️ EEG数据集标签分析器")
    print("=" * 50)
    
    analyzer = EEGLabelAnalyzer()
    analyzer.analyze_all_labels()
    analyzer.save_analysis_result()
    
    print(f"\n" + "=" * 60)
    print(f"✅ 标签分析完成!")
    print(f"📋 请查看上述分析结果，确定真实的标签信息")
    print(f"=" * 60)


if __name__ == "__main__":
    main()
