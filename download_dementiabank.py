import tensorflow_datasets as tfds
import os
import warnings
warnings.filterwarnings('ignore')

def download_dementiabank():
    print("Downloading DementiaBank dataset...")

    try:
        # Set download config
        download_config = tfds.download.DownloadConfig(
            extract_dir="./dementiabank_extract",
            manual_dir="./dementiabank_manual"
        )

        dataset, info = tfds.load(
            'dementiabank',
            with_info=True,
            download=True,
            data_dir="./dementiabank_data",
            download_and_prepare_kwargs={'download_config': download_config},
            as_supervised=False
        )

        print("Download completed!")
        print(f"Dataset splits: {list(dataset.keys())}")
        print(f"Total samples: {info.splits.total_num_examples}")

        return dataset, info

    except Exception as e:
        print(f"Primary download failed: {e}")
        return None, None

def alternative_download():
    print("Trying alternative download method...")

    try:
        # Try with different settings
        ds = tfds.load(
            'dementiabank',
            download=True,
            try_gcs=False,  # Disable Google Cloud Storage
            data_dir="./dementiabank_alt"
        )
        print("Alternative download completed!")
        return ds

    except Exception as e:
        print(f"Alternative download failed: {e}")
        return None

def manual_info():
    print("\nManual download instructions:")
    print("1. Visit: https://dementia.talkbank.org/")
    print("2. Register and download Pitt corpus")
    print("3. Extract to ./dementiabank_manual/")
    print("4. Run tfds.load with manual_dir parameter")

if __name__ == "__main__":
    # Suppress TensorFlow warnings
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

    dataset, info = download_dementiabank()

    if dataset is None:
        print("Trying alternative method...")
        dataset = alternative_download()

    if dataset is None:
        print("Automatic download failed.")
        manual_info()
    else:
        print("Dataset ready to use!")

        # Show sample
        for split_name, split_data in dataset.items():
            print(f"\n{split_name} split:")
            for example in split_data.take(1):
                print(f"Keys: {list(example.keys())}")
                break
