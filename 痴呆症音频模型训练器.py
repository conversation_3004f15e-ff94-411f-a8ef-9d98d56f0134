"""
Dementia Audio Model Trainer
痴呆症音频模型训练器
基于38维特征的多模态深度学习模型
"""

import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization, Input, Concatenate
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import matplotlib.pyplot as plt
import seaborn as sns
import os
import pickle
import json
import warnings
warnings.filterwarnings('ignore')

# 抑制TensorFlow警告
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

class DementiaAudioModelTrainer:
    def __init__(self, data_path=None):
        # 尝试多个可能的路径
        possible_paths = [
            r"D:\模型开发\audio\processed_datasets",
            "processed_datasets",
            "."
        ]

        self.data_path = None
        if data_path:
            self.data_path = data_path
        else:
            # 自动查找数据路径
            for path in possible_paths:
                test_file = os.path.join(path, "train_set_scaled.csv")
                if os.path.exists(test_file):
                    self.data_path = path
                    break

        if not self.data_path:
            raise ValueError("无法找到数据文件，请检查路径")
        self.model = None
        self.history = None
        self.feature_info = None
        self.label_encoder = None
        self.scaler = None
        
        # 检查GPU
        self.setup_gpu()
        
    def setup_gpu(self):
        """设置GPU训练环境"""
        print("🔧 设置训练环境...")
        
        # 检查GPU可用性
        gpus = tf.config.experimental.list_physical_devices('GPU')
        if gpus:
            try:
                # 设置GPU内存增长
                for gpu in gpus:
                    tf.config.experimental.set_memory_growth(gpu, True)
                print(f"✅ 找到 {len(gpus)} 个GPU，已启用GPU训练")
            except RuntimeError as e:
                print(f"⚠️ GPU设置失败: {e}")
        else:
            print("⚠️ 未找到GPU，使用CPU训练")
    
    def load_data(self):
        """加载预处理后的数据"""
        print("📊 加载训练数据...")
        
        try:
            # 加载训练、验证、测试集
            self.train_data = pd.read_csv(os.path.join(self.data_path, "train_set_scaled.csv"))
            self.val_data = pd.read_csv(os.path.join(self.data_path, "validation_set_scaled.csv"))
            self.test_data = pd.read_csv(os.path.join(self.data_path, "test_set_scaled.csv"))
            
            print(f"   训练集: {len(self.train_data)} 样本")
            print(f"   验证集: {len(self.val_data)} 样本")
            print(f"   测试集: {len(self.test_data)} 样本")
            
            # 加载特征信息
            with open(os.path.join(self.data_path, "feature_info.json"), 'r', encoding='utf-8') as f:
                self.feature_info = json.load(f)
            
            # 加载标签编码器和标准化器
            with open(os.path.join(self.data_path, "label_encoder.pkl"), 'rb') as f:
                self.label_encoder = pickle.load(f)
            
            with open(os.path.join(self.data_path, "scaler.pkl"), 'rb') as f:
                self.scaler = pickle.load(f)
            
            print(f"   特征维度: {len(self.get_feature_columns())}")
            print(f"   类别: {list(self.label_encoder.classes_)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def get_feature_columns(self):
        """获取特征列名"""
        feature_cols = []
        for category in ['demographic', 'acoustic', 'mfcc', 'linguistic']:
            if category in self.feature_info:
                feature_cols.extend(self.feature_info[category])
        return feature_cols
    
    def prepare_data(self):
        """准备训练数据"""
        print("🔧 准备训练数据...")
        
        feature_cols = self.get_feature_columns()
        
        # 分离特征和标签
        self.X_train = self.train_data[feature_cols].values
        self.y_train = self.train_data['diagnosis_encoded'].values
        
        self.X_val = self.val_data[feature_cols].values
        self.y_val = self.val_data['diagnosis_encoded'].values
        
        self.X_test = self.test_data[feature_cols].values
        self.y_test = self.test_data['diagnosis_encoded'].values
        
        # 转换为分类标签
        self.y_train_cat = tf.keras.utils.to_categorical(self.y_train, num_classes=3)
        self.y_val_cat = tf.keras.utils.to_categorical(self.y_val, num_classes=3)
        self.y_test_cat = tf.keras.utils.to_categorical(self.y_test, num_classes=3)
        
        print(f"   特征矩阵形状: {self.X_train.shape}")
        print(f"   标签矩阵形状: {self.y_train_cat.shape}")
        
        # 显示类别分布
        unique, counts = np.unique(self.y_train, return_counts=True)
        class_names = [self.label_encoder.classes_[i] for i in unique]
        for name, count in zip(class_names, counts):
            print(f"   {name}: {count} 样本 ({count/len(self.y_train)*100:.1f}%)")
    
    def build_model(self):
        """构建多模态深度学习模型"""
        print("🏗️ 构建模型架构...")
        
        # 输入层
        input_layer = Input(shape=(self.X_train.shape[1],), name='features_input')
        
        # 特征分组处理
        demographic_size = len(self.feature_info['demographic'])
        acoustic_size = len(self.feature_info['acoustic'])
        mfcc_size = len(self.feature_info['mfcc'])
        linguistic_size = len(self.feature_info['linguistic'])
        
        # 分离不同类型的特征
        demographic_features = input_layer[:, :demographic_size]
        acoustic_features = input_layer[:, demographic_size:demographic_size+acoustic_size]
        mfcc_features = input_layer[:, demographic_size+acoustic_size:demographic_size+acoustic_size+mfcc_size]
        linguistic_features = input_layer[:, demographic_size+acoustic_size+mfcc_size:]
        
        # 人口学特征分支
        demo_branch = Dense(16, activation='relu', name='demo_dense1')(demographic_features)
        demo_branch = BatchNormalization()(demo_branch)
        demo_branch = Dropout(0.3)(demo_branch)
        
        # 声学特征分支
        acoustic_branch = Dense(64, activation='relu', name='acoustic_dense1')(acoustic_features)
        acoustic_branch = BatchNormalization()(acoustic_branch)
        acoustic_branch = Dropout(0.4)(acoustic_branch)
        acoustic_branch = Dense(32, activation='relu', name='acoustic_dense2')(acoustic_branch)
        acoustic_branch = BatchNormalization()(acoustic_branch)
        acoustic_branch = Dropout(0.3)(acoustic_branch)
        
        # MFCC特征分支
        mfcc_branch = Dense(64, activation='relu', name='mfcc_dense1')(mfcc_features)
        mfcc_branch = BatchNormalization()(mfcc_branch)
        mfcc_branch = Dropout(0.4)(mfcc_branch)
        mfcc_branch = Dense(32, activation='relu', name='mfcc_dense2')(mfcc_branch)
        mfcc_branch = BatchNormalization()(mfcc_branch)
        mfcc_branch = Dropout(0.3)(mfcc_branch)
        
        # 语言特征分支
        linguistic_branch = Dense(64, activation='relu', name='linguistic_dense1')(linguistic_features)
        linguistic_branch = BatchNormalization()(linguistic_branch)
        linguistic_branch = Dropout(0.4)(linguistic_branch)
        linguistic_branch = Dense(32, activation='relu', name='linguistic_dense2')(linguistic_branch)
        linguistic_branch = BatchNormalization()(linguistic_branch)
        linguistic_branch = Dropout(0.3)(linguistic_branch)
        
        # 特征融合
        merged = Concatenate(name='feature_fusion')([demo_branch, acoustic_branch, mfcc_branch, linguistic_branch])
        
        # 融合后的深度网络
        x = Dense(128, activation='relu', name='fusion_dense1')(merged)
        x = BatchNormalization()(x)
        x = Dropout(0.5)(x)
        
        x = Dense(64, activation='relu', name='fusion_dense2')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.4)(x)
        
        x = Dense(32, activation='relu', name='fusion_dense3')(x)
        x = BatchNormalization()(x)
        x = Dropout(0.3)(x)
        
        # 输出层
        output = Dense(3, activation='softmax', name='classification_output')(x)
        
        # 创建模型
        self.model = Model(inputs=input_layer, outputs=output, name='DementiaAudioClassifier')
        
        # 编译模型
        self.model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', 'precision', 'recall']
        )
        
        # 显示模型结构
        print("✅ 模型构建完成")
        print(f"   总参数: {self.model.count_params():,}")
        
        return self.model

    def train_model(self, epochs=100, batch_size=32):
        """训练模型"""
        print("🚀 开始模型训练...")

        # 创建输出目录
        output_dir = "trained_audio_models"
        os.makedirs(output_dir, exist_ok=True)

        # 设置回调函数
        callbacks = [
            EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-7,
                verbose=1
            ),
            ModelCheckpoint(
                filepath=os.path.join(output_dir, 'best_dementia_audio_model.h5'),
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]

        # 训练模型
        print(f"   训练参数: epochs={epochs}, batch_size={batch_size}")

        self.history = self.model.fit(
            self.X_train, self.y_train_cat,
            validation_data=(self.X_val, self.y_val_cat),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )

        print("✅ 模型训练完成")
        return self.history

    def evaluate_model(self):
        """评估模型性能"""
        print("📊 评估模型性能...")

        # 在测试集上预测
        y_pred_proba = self.model.predict(self.X_test)
        y_pred = np.argmax(y_pred_proba, axis=1)

        # 计算准确率
        accuracy = accuracy_score(self.y_test, y_pred)
        print(f"   测试集准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")

        # 分类报告
        class_names = self.label_encoder.classes_
        report = classification_report(
            self.y_test, y_pred,
            target_names=class_names,
            output_dict=True
        )

        print("\n📋 详细分类报告:")
        for class_name in class_names:
            metrics = report[class_name]
            print(f"   {class_name}:")
            print(f"     精确率: {metrics['precision']:.4f}")
            print(f"     召回率: {metrics['recall']:.4f}")
            print(f"     F1分数: {metrics['f1-score']:.4f}")

        # 混淆矩阵
        cm = confusion_matrix(self.y_test, y_pred)

        return {
            'accuracy': accuracy,
            'classification_report': report,
            'confusion_matrix': cm,
            'predictions': y_pred,
            'probabilities': y_pred_proba
        }

    def plot_training_history(self):
        """绘制训练历史"""
        print("📈 生成训练历史图表...")

        if self.history is None:
            print("❌ 没有训练历史数据")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('痴呆症音频模型训练历史', fontsize=16, fontweight='bold')

        # 准确率
        axes[0, 0].plot(self.history.history['accuracy'], label='训练准确率', linewidth=2)
        axes[0, 0].plot(self.history.history['val_accuracy'], label='验证准确率', linewidth=2)
        axes[0, 0].set_title('模型准确率')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Accuracy')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 损失
        axes[0, 1].plot(self.history.history['loss'], label='训练损失', linewidth=2)
        axes[0, 1].plot(self.history.history['val_loss'], label='验证损失', linewidth=2)
        axes[0, 1].set_title('模型损失')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Loss')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # 精确率
        if 'precision' in self.history.history:
            axes[1, 0].plot(self.history.history['precision'], label='训练精确率', linewidth=2)
            axes[1, 0].plot(self.history.history['val_precision'], label='验证精确率', linewidth=2)
            axes[1, 0].set_title('模型精确率')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Precision')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

        # 召回率
        if 'recall' in self.history.history:
            axes[1, 1].plot(self.history.history['recall'], label='训练召回率', linewidth=2)
            axes[1, 1].plot(self.history.history['val_recall'], label='验证召回率', linewidth=2)
            axes[1, 1].set_title('模型召回率')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Recall')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        output_path = "trained_audio_models/training_history.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"   训练历史图表已保存: {output_path}")

        plt.show()

    def plot_confusion_matrix(self, cm):
        """绘制混淆矩阵"""
        print("📊 生成混淆矩阵图表...")

        plt.figure(figsize=(10, 8))

        # 创建热力图
        sns.heatmap(
            cm,
            annot=True,
            fmt='d',
            cmap='Blues',
            xticklabels=self.label_encoder.classes_,
            yticklabels=self.label_encoder.classes_,
            cbar_kws={'label': '样本数量'}
        )

        plt.title('痴呆症音频模型 - 混淆矩阵', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('预测标签', fontsize=12)
        plt.ylabel('真实标签', fontsize=12)

        # 添加准确率信息
        accuracy = np.trace(cm) / np.sum(cm)
        plt.figtext(0.5, 0.02, f'总体准确率: {accuracy:.4f} ({accuracy*100:.2f}%)',
                   ha='center', fontsize=12, fontweight='bold')

        plt.tight_layout()

        # 保存图表
        output_path = "trained_audio_models/confusion_matrix.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"   混淆矩阵图表已保存: {output_path}")

        plt.show()

    def save_model_info(self, evaluation_results):
        """保存模型信息和评估结果"""
        print("💾 保存模型信息...")

        output_dir = "trained_audio_models"

        # 保存模型架构
        model_json = self.model.to_json()
        with open(os.path.join(output_dir, "model_architecture.json"), "w") as f:
            f.write(model_json)

        # 保存训练历史
        if self.history:
            history_df = pd.DataFrame(self.history.history)
            history_df.to_csv(os.path.join(output_dir, "training_history.csv"), index=False)

        # 保存评估结果
        eval_info = {
            'model_info': {
                'total_parameters': int(self.model.count_params()),
                'feature_dimensions': len(self.get_feature_columns()),
                'classes': list(self.label_encoder.classes_),
                'training_samples': len(self.X_train),
                'validation_samples': len(self.X_val),
                'test_samples': len(self.X_test)
            },
            'performance': {
                'test_accuracy': float(evaluation_results['accuracy']),
                'classification_report': evaluation_results['classification_report']
            },
            'feature_groups': self.feature_info
        }

        with open(os.path.join(output_dir, "model_evaluation.json"), "w", encoding='utf-8') as f:
            json.dump(eval_info, f, indent=2, ensure_ascii=False)

        print(f"   模型信息已保存到: {output_dir}")

    def run_complete_training(self, epochs=100, batch_size=32):
        """运行完整的训练流程"""
        print("🎵 痴呆症音频模型训练器")
        print("=" * 60)

        try:
            # 1. 加载数据
            if not self.load_data():
                return None

            # 2. 准备数据
            self.prepare_data()

            # 3. 构建模型
            self.build_model()

            # 4. 训练模型
            self.train_model(epochs=epochs, batch_size=batch_size)

            # 5. 评估模型
            evaluation_results = self.evaluate_model()

            # 6. 生成图表
            self.plot_training_history()
            self.plot_confusion_matrix(evaluation_results['confusion_matrix'])

            # 7. 保存模型信息
            self.save_model_info(evaluation_results)

            print("\n" + "=" * 60)
            print("✅ 模型训练完成!")
            print(f"🎯 最终测试准确率: {evaluation_results['accuracy']:.4f} ({evaluation_results['accuracy']*100:.2f}%)")
            print("📁 模型文件保存在: trained_audio_models/")
            print("🚀 模型已准备好用于部署!")

            return {
                'model': self.model,
                'evaluation': evaluation_results,
                'history': self.history
            }

        except Exception as e:
            print(f"❌ 训练失败: {str(e)}")
            return None

if __name__ == "__main__":
    print("🎵 痴呆症音频模型训练器")
    print("=" * 60)

    # 创建训练器
    trainer = DementiaAudioModelTrainer()

    # 运行完整训练
    result = trainer.run_complete_training(
        epochs=100,  # 可以调整训练轮数
        batch_size=32  # 可以调整批次大小
    )

    if result:
        print(f"\n🎯 训练结果摘要:")
        print(f"   模型参数: {result['model'].count_params():,}")
        print(f"   测试准确率: {result['evaluation']['accuracy']:.4f}")

        # 显示各类别性能
        report = result['evaluation']['classification_report']
        for class_name in ['Normal', 'MCI', 'Dementia']:
            if class_name in report:
                f1 = report[class_name]['f1-score']
                print(f"   {class_name} F1分数: {f1:.4f}")

        print(f"\n📋 查看详细结果:")
        print(f"   模型文件: trained_audio_models/best_dementia_audio_model.h5")
        print(f"   训练历史: trained_audio_models/training_history.png")
        print(f"   混淆矩阵: trained_audio_models/confusion_matrix.png")
    else:
        print("\n❌ 训练失败，请检查数据和环境")
