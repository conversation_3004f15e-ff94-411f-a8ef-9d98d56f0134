"""
Dementia召回率专用训练器
专门解决Dementia识别失败和假阴性风险
目标: Dementia召回率 >= 80%, 控制假阴性
"""

import pandas as pd
import numpy as np
import os
import time
import warnings
warnings.filterwarnings('ignore')

print("🚨 Dementia召回率专用训练器")
print("🎯 专门解决Dementia识别失败问题")
print("⚠️ 目标: Dementia召回率 >= 80%")
print("🏥 医疗原则: 宁可误诊，不可漏诊")
print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("=" * 70)

start_time = time.time()
data_path = r"D:\模型开发\audio\processed_datasets"
output_path = r"D:\模型开发\audio"

# 加载数据
print("📊 加载数据...")
train_data = pd.read_csv(os.path.join(data_path, "train_set_scaled.csv"))
val_data = pd.read_csv(os.path.join(data_path, "validation_set_scaled.csv"))
test_data = pd.read_csv(os.path.join(data_path, "test_set_scaled.csv"))

combined_train = pd.concat([train_data, val_data], ignore_index=True)

feature_cols = [col for col in combined_train.columns if col not in ['diagnosis_encoded', 'diagnosis_name']]
X_train = combined_train[feature_cols].values
y_train = combined_train['diagnosis_encoded'].values
X_test = test_data[feature_cols].values
y_test = test_data['diagnosis_encoded'].values

print(f"✅ 数据加载完成")
class_counts = np.bincount(y_train)
test_class_counts = np.bincount(y_test)
print(f"   训练集: Dementia({class_counts[0]}) MCI({class_counts[1]}) Normal({class_counts[2]})")
print(f"   测试集: Dementia({test_class_counts[0]}) MCI({test_class_counts[1]}) Normal({test_class_counts[2]})")

# 策略1: 极端SMOTE - 让Dementia成为主要类别
print(f"\n🚨 策略1: 极端SMOTE - 让Dementia成为主要类别...")

try:
    from imblearn.over_sampling import SMOTE, ADASYN
    
    # 极端采样策略: 让Dementia样本数量超过其他类别
    target_dementia = max(class_counts) + 1000  # 比最大类别还多1000个
    target_others = max(class_counts)
    
    sampling_strategy = {
        0: target_dementia,  # Dementia - 最多
        1: target_others,    # MCI
        2: target_others     # Normal
    }
    
    print(f"   目标分布: Dementia({target_dementia}) MCI({target_others}) Normal({target_others})")
    
    smote = SMOTE(sampling_strategy=sampling_strategy, random_state=42, k_neighbors=3)
    X_train_smote, y_train_smote = smote.fit_resample(X_train, y_train)
    
    final_counts = np.bincount(y_train_smote)
    print(f"   SMOTE结果: {final_counts}")
    use_smote = True

except ImportError:
    print("   ❌ SMOTE不可用")
    X_train_smote = X_train
    y_train_smote = y_train
    use_smote = False

# 策略2: 极端权重 - Dementia权重×10
print(f"\n🚨 策略2: 极端权重 - Dementia权重×10...")

from sklearn.utils.class_weight import compute_class_weight

# 计算极端权重
extreme_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
extreme_weights[0] *= 10.0  # Dementia权重×10

class_weight_dict = {i: extreme_weights[i] for i in range(len(extreme_weights))}
print(f"   极端权重: {class_weight_dict}")

# 策略3: 专门的Dementia检测器
print(f"\n🚨 策略3: 专门的Dementia检测器...")

# 创建二分类问题: Dementia vs 非Dementia
y_binary_train = (y_train_smote == 0).astype(int)
y_binary_test = (y_test == 0).astype(int)

print(f"   二分类分布: 训练集 Dementia({np.sum(y_binary_train)}) 非Dementia({len(y_binary_train)-np.sum(y_binary_train)})")
print(f"   二分类分布: 测试集 Dementia({np.sum(y_binary_test)}) 非Dementia({len(y_binary_test)-np.sum(y_binary_test)})")

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.metrics import accuracy_score, classification_report, recall_score, precision_score

# 训练专门的Dementia检测器
dementia_detectors = {}

# 检测器1: 极端随机森林
print("   训练Dementia专用随机森林...")
dementia_rf = RandomForestClassifier(
    n_estimators=2000,
    max_depth=None,
    class_weight={0: 1, 1: 20},  # 极端偏向Dementia
    random_state=42,
    n_jobs=-1
)

dementia_rf.fit(X_train_smote, y_binary_train)
rf_binary_pred = dementia_rf.predict(X_test)
rf_dementia_recall = recall_score(y_binary_test, rf_binary_pred, pos_label=1)
rf_dementia_precision = precision_score(y_binary_test, rf_binary_pred, pos_label=1)

dementia_detectors['DementiaRF'] = dementia_rf
print(f"     Dementia召回率: {rf_dementia_recall:.4f} ({rf_dementia_recall*100:.2f}%)")
print(f"     Dementia精确率: {rf_dementia_precision:.4f} ({rf_dementia_precision*100:.2f}%)")

# 检测器2: 极端XGBoost
print("   训练Dementia专用XGBoost...")
try:
    import xgboost as xgb
    
    # 计算样本权重 - 极端偏向Dementia
    sample_weights = np.where(y_binary_train == 1, 20.0, 1.0)
    
    dementia_xgb = xgb.XGBClassifier(
        n_estimators=2000,
        max_depth=10,
        learning_rate=0.01,  # 更慢学习，更仔细
        subsample=0.8,
        colsample_bytree=0.8,
        scale_pos_weight=20,  # 极端偏向正类
        random_state=42,
        eval_metric='logloss'
    )
    
    dementia_xgb.fit(
        X_train_smote, y_binary_train,
        sample_weight=sample_weights,
        eval_set=[(X_test, y_binary_test)],
        verbose=False
    )
    
    xgb_binary_pred = dementia_xgb.predict(X_test)
    xgb_dementia_recall = recall_score(y_binary_test, xgb_binary_pred, pos_label=1)
    xgb_dementia_precision = precision_score(y_binary_test, xgb_binary_pred, pos_label=1)
    
    dementia_detectors['DementiaXGB'] = dementia_xgb
    print(f"     Dementia召回率: {xgb_dementia_recall:.4f} ({xgb_dementia_recall*100:.2f}%)")
    print(f"     Dementia精确率: {xgb_dementia_precision:.4f} ({xgb_dementia_precision*100:.2f}%)")

except ImportError:
    print("     XGBoost不可用")

# 策略4: 调整决策阈值优化召回率
print(f"\n🚨 策略4: 调整决策阈值优化召回率...")

# 使用概率预测并调整阈值
best_threshold = 0.5
best_recall = 0

for threshold in np.arange(0.1, 0.9, 0.05):
    rf_proba = dementia_rf.predict_proba(X_test)[:, 1]
    rf_pred_threshold = (rf_proba >= threshold).astype(int)
    recall = recall_score(y_binary_test, rf_pred_threshold, pos_label=1)
    
    if recall > best_recall:
        best_recall = recall
        best_threshold = threshold

print(f"   最佳阈值: {best_threshold:.2f}")
print(f"   最佳Dementia召回率: {best_recall:.4f} ({best_recall*100:.2f}%)")

# 使用最佳阈值进行最终预测
rf_proba = dementia_rf.predict_proba(X_test)[:, 1]
final_dementia_pred = (rf_proba >= best_threshold).astype(int)
final_dementia_recall = recall_score(y_binary_test, final_dementia_pred, pos_label=1)
final_dementia_precision = precision_score(y_binary_test, final_dementia_pred, pos_label=1)

print(f"   最终Dementia召回率: {final_dementia_recall:.4f} ({final_dementia_recall*100:.2f}%)")
print(f"   最终Dementia精确率: {final_dementia_precision:.4f} ({final_dementia_precision*100:.2f}%)")

# 策略5: 集成多个检测器
print(f"\n🚨 策略5: 集成多个Dementia检测器...")

if len(dementia_detectors) >= 2:
    # 收集所有检测器的预测概率
    all_probas = []
    
    for name, detector in dementia_detectors.items():
        proba = detector.predict_proba(X_test)[:, 1]
        all_probas.append(proba)
    
    # 集成预测 - 取平均
    ensemble_proba = np.mean(all_probas, axis=0)
    
    # 寻找最佳集成阈值
    best_ensemble_threshold = 0.5
    best_ensemble_recall = 0
    
    for threshold in np.arange(0.1, 0.9, 0.05):
        ensemble_pred = (ensemble_proba >= threshold).astype(int)
        recall = recall_score(y_binary_test, ensemble_pred, pos_label=1)
        
        if recall > best_ensemble_recall:
            best_ensemble_recall = recall
            best_ensemble_threshold = threshold
    
    # 最终集成预测
    final_ensemble_pred = (ensemble_proba >= best_ensemble_threshold).astype(int)
    ensemble_recall = recall_score(y_binary_test, final_ensemble_pred, pos_label=1)
    ensemble_precision = precision_score(y_binary_test, final_ensemble_pred, pos_label=1)
    
    print(f"   集成最佳阈值: {best_ensemble_threshold:.2f}")
    print(f"   集成Dementia召回率: {ensemble_recall:.4f} ({ensemble_recall*100:.2f}%)")
    print(f"   集成Dementia精确率: {ensemble_precision:.4f} ({ensemble_precision*100:.2f}%)")

# 策略6: 构建三分类器（基于Dementia检测结果）
print(f"\n🚨 策略6: 构建医疗安全的三分类器...")

# 使用最佳Dementia检测器的结果
dementia_mask = final_ensemble_pred if 'ensemble_recall' in locals() else final_dementia_pred

# 对于检测为非Dementia的样本，进行MCI vs Normal分类
non_dementia_indices = np.where(dementia_mask == 0)[0]

if len(non_dementia_indices) > 0:
    # 训练MCI vs Normal分类器
    non_dementia_train_mask = y_train_smote != 0
    X_non_dementia_train = X_train_smote[non_dementia_train_mask]
    y_non_dementia_train = y_train_smote[non_dementia_train_mask]
    
    # 重新编码: MCI(1)->0, Normal(2)->1
    y_non_dementia_binary = (y_non_dementia_train == 2).astype(int)
    
    mci_normal_classifier = RandomForestClassifier(
        n_estimators=500,
        max_depth=20,
        class_weight='balanced',
        random_state=42,
        n_jobs=-1
    )
    
    mci_normal_classifier.fit(X_non_dementia_train, y_non_dementia_binary)
    
    # 构建最终三分类预测
    final_three_class_pred = np.zeros(len(y_test))
    
    # 设置Dementia预测
    final_three_class_pred[dementia_mask == 1] = 0  # Dementia
    
    # 对非Dementia样本进行MCI vs Normal分类
    if len(non_dementia_indices) > 0:
        X_non_dementia_test = X_test[non_dementia_indices]
        mci_normal_pred = mci_normal_classifier.predict(X_non_dementia_test)
        
        # 转换回原始标签
        for i, idx in enumerate(non_dementia_indices):
            if mci_normal_pred[i] == 1:
                final_three_class_pred[idx] = 2  # Normal
            else:
                final_three_class_pred[idx] = 1  # MCI
    
    # 评估最终三分类结果
    final_accuracy = accuracy_score(y_test, final_three_class_pred)
    final_report = classification_report(y_test, final_three_class_pred, output_dict=True)
    
    print(f"   最终三分类准确率: {final_accuracy:.4f} ({final_accuracy*100:.2f}%)")
    
    if '0' in final_report:
        final_dem_recall = final_report['0']['recall']
        final_dem_precision = final_report['0']['precision']
        final_dem_f1 = final_report['0']['f1-score']
        
        print(f"   最终Dementia召回率: {final_dem_recall:.4f} ({final_dem_recall*100:.2f}%)")
        print(f"   最终Dementia精确率: {final_dem_precision:.4f} ({final_dem_precision*100:.2f}%)")
        print(f"   最终Dementia F1分数: {final_dem_f1:.4f}")

# 显示结果
print(f"\n" + "="*80)
print("🚨 Dementia召回率专用训练结果")
print("="*80)

results_summary = {
    'RF二分类': (rf_dementia_recall, rf_dementia_precision),
    '阈值优化': (final_dementia_recall, final_dementia_precision),
}

if 'ensemble_recall' in locals():
    results_summary['集成检测'] = (ensemble_recall, ensemble_precision)

if 'final_dem_recall' in locals():
    results_summary['最终三分类'] = (final_dem_recall, final_dem_precision)

print(f"{'方法':<15} {'Dementia召回率':<15} {'Dementia精确率':<15} {'状态':<10}")
print("-"*80)

best_recall = 0
best_method = ""

for method, (recall, precision) in results_summary.items():
    status = "🎯" if recall >= 0.8 else "✅" if recall >= 0.6 else "📈" if recall >= 0.3 else "❌"
    print(f"{method:<15} {recall:<15.4f} {precision:<15.4f} {status}")
    
    if recall > best_recall:
        best_recall = recall
        best_method = method

print("-"*80)
print(f"🏆 最佳方法: {best_method}")
print(f"🎯 最佳Dementia召回率: {best_recall:.4f} ({best_recall*100:.2f}%)")

# 医疗安全评估
print(f"\n🏥 医疗安全评估:")

if best_recall >= 0.8:
    print("✅ 优秀: Dementia召回率 >= 80%")
    print("🏥 医疗建议: 可用于临床筛查")
    safety_level = "优秀"
elif best_recall >= 0.6:
    print("📈 良好: Dementia召回率 >= 60%")
    print("🔍 医疗建议: 可用于辅助诊断，需结合其他检查")
    safety_level = "良好"
elif best_recall >= 0.3:
    print("⚠️ 一般: Dementia召回率 >= 30%")
    print("🔬 医疗建议: 仅适用于研究，不建议临床使用")
    safety_level = "一般"
else:
    print("❌ 不合格: Dementia召回率 < 30%")
    print("🚫 医疗建议: 不适用于任何医疗场景")
    safety_level = "不合格"

# 假阴性风险评估
false_negative_rate = 1 - best_recall
print(f"\n⚠️ 假阴性风险评估:")
print(f"   假阴性率: {false_negative_rate:.4f} ({false_negative_rate*100:.2f}%)")

if false_negative_rate <= 0.2:
    print("✅ 低风险: 假阴性率 <= 20%")
elif false_negative_rate <= 0.4:
    print("⚠️ 中等风险: 假阴性率 <= 40%")
else:
    print("🚨 高风险: 假阴性率 > 40%")

# 保存结果
if best_recall >= 0.3:  # 至少有30%召回率才保存
    print(f"\n💾 保存Dementia专用模型...")
    
    os.makedirs(output_path, exist_ok=True)
    
    # 保存最佳检测器
    import joblib
    if best_method == 'RF二分类':
        joblib.dump(dementia_rf, os.path.join(output_path, "dementia_detector.pkl"))
    elif 'dementia_xgb' in locals() and best_method == 'XGBoost二分类':
        joblib.dump(dementia_xgb, os.path.join(output_path, "dementia_detector.pkl"))
    
    # 保存详细报告
    import json
    dementia_report = {
        'dementia_recall_training': {
            'best_method': best_method,
            'best_dementia_recall': float(best_recall),
            'false_negative_rate': float(false_negative_rate),
            'safety_level': safety_level,
            'medical_recommendation': 'Clinical use approved' if best_recall >= 0.8 else 'Research only',
            'training_time_hours': (time.time() - start_time) / 3600
        },
        'all_results': {k: {'recall': float(v[0]), 'precision': float(v[1])} for k, v in results_summary.items()},
        'medical_safety': {
            'dementia_detection_quality': safety_level,
            'false_negative_risk': 'Low' if false_negative_rate <= 0.2 else 'Medium' if false_negative_rate <= 0.4 else 'High',
            'clinical_readiness': best_recall >= 0.8
        }
    }
    
    with open(os.path.join(output_path, "dementia_recall_report.json"), "w", encoding='utf-8') as f:
        json.dump(dementia_report, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Dementia专用模型已保存到: {output_path}")

total_time = (time.time() - start_time) / 3600
print(f"\n⏰ 训练耗时: {total_time:.2f}小时")
print(f"⏰ 结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
print("🚨 Dementia召回率专用训练器完成!")
