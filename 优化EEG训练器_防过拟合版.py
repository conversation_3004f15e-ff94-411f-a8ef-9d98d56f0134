"""
🧠 优化EEG训练器 - 防过拟合版
专门解决过拟合问题的EEG模型训练器
"""

import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow.keras import layers, models, regularizers, callbacks
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import mne
import joblib
import warnings
warnings.filterwarnings('ignore')

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    for gpu in gpus:
        tf.config.experimental.set_memory_growth(gpu, True)

class OptimizedEEGTrainer:
    """优化的EEG训练器 - 防过拟合版"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.history = None
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        # 优化的EEG参数
        self.n_channels = 19
        self.n_samples = 128
        self.sampling_rate = 128
        
        print("🧠 优化EEG训练器初始化 - 防过拟合版")
        print("⚡ 专门解决过拟合问题")
    
    def load_eeg_data(self, split_name):
        """加载EEG数据"""
        print(f"📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        labels_df = pd.read_csv(labels_file, sep='\t')
        
        X_data = []
        y_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            set_files = [f for f in os.listdir(split_dir) 
                        if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                data = raw.get_data()
                
                # 创建更多epochs以增加数据量
                epochs = self.create_augmented_epochs(data)
                
                for epoch in epochs:
                    X_data.append(epoch)
                    y_labels.append(label)
                
            except Exception as e:
                print(f"❌ 加载{subject_id}失败: {e}")
                continue
        
        X = np.array(X_data)
        y = np.array(y_labels)
        
        print(f"✅ {split_name}数据加载完成: {X.shape}")
        return X, y
    
    def create_augmented_epochs(self, data):
        """创建增强的epochs"""
        n_channels, n_timepoints = data.shape
        epochs = []
        
        # 基础epochs
        n_basic_epochs = n_timepoints // self.n_samples
        for i in range(n_basic_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            epoch = data[:, start_idx:end_idx]
            
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]
            elif epoch.shape[0] < self.n_channels:
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            epochs.append(epoch)
        
        # 数据增强 - 滑动窗口
        step_size = self.n_samples // 4  # 25%重叠
        for i in range(0, n_timepoints - self.n_samples, step_size):
            start_idx = i
            end_idx = start_idx + self.n_samples
            epoch = data[:, start_idx:end_idx]
            
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]
            elif epoch.shape[0] < self.n_channels:
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            # 添加轻微噪声
            noise = np.random.normal(0, 0.01, epoch.shape)
            epoch_noisy = epoch + noise
            
            epochs.append(epoch_noisy)
        
        return epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("🔧 预处理数据...")
        
        # 标准化EEG数据 - 更强的标准化
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                # 使用robust标准化
                median = np.median(channel_data)
                mad = np.median(np.abs(channel_data - median))
                if mad > 0:
                    X_processed[i, ch, :] = (channel_data - median) / (1.4826 * mad)
                else:
                    X_processed[i, ch, :] = channel_data - median
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
        else:
            y_encoded = self.label_encoder.transform(y)
        
        n_classes = len(self.label_encoder.classes_)
        y_categorical = tf.keras.utils.to_categorical(y_encoded, n_classes)
        
        # 添加通道维度
        X_processed = np.expand_dims(X_processed, axis=-1)
        
        print(f"✅ 预处理完成: {X_processed.shape}")
        return X_processed, y_categorical
    
    def build_optimized_model(self, n_classes):
        """构建优化的防过拟合模型"""
        print("🏗️ 构建优化模型 - 防过拟合版...")
        
        # 输入层
        inputs = layers.Input(shape=(self.n_channels, self.n_samples, 1))
        
        # 第一个卷积块 - 减少参数
        x = layers.Conv2D(16, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.01))(inputs)
        x = layers.BatchNormalization()(x)
        x = layers.Conv2D(16, (self.n_channels, 1), activation='relu',
                         kernel_regularizer=regularizers.l2(0.01))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.5)(x)  # 增加dropout
        
        # 第二个卷积块
        x = layers.Conv2D(32, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.01))(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.5)(x)
        
        # 第三个卷积块
        x = layers.Conv2D(64, (1, 7), padding='same', activation='relu',
                         kernel_regularizer=regularizers.l2(0.01))(x)
        x = layers.BatchNormalization()(x)
        x = layers.MaxPooling2D((1, 4))(x)
        x = layers.Dropout(0.5)(x)
        
        # 全局平均池化 - 减少参数
        x = layers.GlobalAveragePooling2D()(x)
        
        # 简化的全连接层
        x = layers.Dense(64, activation='relu',
                        kernel_regularizer=regularizers.l2(0.01))(x)
        x = layers.BatchNormalization()(x)
        x = layers.Dropout(0.6)(x)  # 更高的dropout
        
        # 输出层
        outputs = layers.Dense(n_classes, activation='softmax')(x)
        
        model = models.Model(inputs=inputs, outputs=outputs)
        
        # 优化的编译参数
        model.compile(
            optimizer=tf.keras.optimizers.Adam(
                learning_rate=0.0005,  # 降低学习率
                beta_1=0.9,
                beta_2=0.999,
                epsilon=1e-7
            ),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print(f"✅ 优化模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model
    
    def create_optimized_callbacks(self):
        """创建优化的回调函数"""
        callbacks_list = [
            # 更激进的早停
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=8,  # 减少patience
                restore_best_weights=True,
                verbose=1,
                min_delta=0.001
            ),
            
            # 学习率调度
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.3,  # 更大的衰减
                patience=4,  # 更快响应
                min_lr=1e-7,
                verbose=1
            ),
            
            # 模型检查点
            callbacks.ModelCheckpoint(
                filepath='best_optimized_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
        ]
        
        return callbacks_list
    
    def train_optimized_model(self, epochs=30, batch_size=64):
        """训练优化模型"""
        print("🚀 开始优化训练 - 30轮...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        
        # 加载数据
        X_train, y_train = self.load_eeg_data('train')
        X_val, y_val = self.load_eeg_data('val')
        
        # 预处理
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 构建模型
        n_classes = y_train.shape[1]
        self.model = self.build_optimized_model(n_classes)
        
        # 创建回调
        callbacks_list = self.create_optimized_callbacks()
        
        # 训练模型
        print("🎯 开始训练...")
        self.history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks_list,
            verbose=1,
            shuffle=True
        )
        
        print("✅ 优化训练完成!")
        
        # 评估模型
        self.evaluate_optimized_model()
        
        # 保存模型
        self.model.save('final_optimized_eeg_model.h5')
        joblib.dump(self.label_encoder, 'optimized_eeg_label_encoder.pkl')
        
        print("💾 优化模型已保存")
        
        return self.history
    
    def evaluate_optimized_model(self):
        """评估优化模型"""
        print("\n📊 评估优化模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_eeg_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        
        # 预测
        y_pred_prob = self.model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)
        
        # 计算指标
        test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
        
        print(f"📈 优化模型测试结果:")
        print(f"   测试准确率: {test_acc:.4f}")
        print(f"   测试损失: {test_loss:.4f}")
        
        # 训练历史摘要
        if self.history:
            final_train_acc = self.history.history['accuracy'][-1]
            final_val_acc = self.history.history['val_accuracy'][-1]
            overfitting_gap = final_train_acc - final_val_acc
            
            print(f"\n📊 过拟合分析:")
            print(f"   最终训练准确率: {final_train_acc:.4f}")
            print(f"   最终验证准确率: {final_val_acc:.4f}")
            print(f"   过拟合差距: {overfitting_gap:.4f}")
            
            if overfitting_gap < 0.15:
                print("✅ 过拟合问题已显著改善!")
            elif overfitting_gap < 0.25:
                print("⚠️ 过拟合有所改善，但仍需优化")
            else:
                print("❌ 过拟合问题仍然存在")
        
        return test_acc, test_loss


def main():
    """主函数"""
    print("🧠 优化EEG训练器 - 防过拟合版")
    print("=" * 50)
    
    trainer = OptimizedEEGTrainer()
    
    # 开始优化训练
    history = trainer.train_optimized_model(epochs=30, batch_size=64)
    
    print("\n🎉 优化训练完成!")
    print("📁 输出文件:")
    print("   🎯 final_optimized_eeg_model.h5")
    print("   📊 optimized_eeg_label_encoder.pkl")
    print("   ✅ best_optimized_eeg_model.h5")


if __name__ == "__main__":
    main()
