"""
Comprehensive Audio Dataset Analyzer for D:/模型开发/audio
Deep analysis of all files and structure
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Try to import audio libraries
try:
    import librosa
    AUDIO_ANALYSIS = True
except ImportError:
    AUDIO_ANALYSIS = False
    print("Note: librosa not available, audio analysis will be limited")

try:
    import matplotlib.pyplot as plt
    PLOTTING = True
except ImportError:
    PLOTTING = False

def analyze_audio_dataset():
    """Comprehensive analysis of D:/模型开发/audio dataset"""

    audio_path = Path(r"D:\模型开发\audio")

    if not audio_path.exists():
        print(f"Error: Path {audio_path} does not exist!")
        return None

    print(f"🔍 Analyzing audio dataset at: {audio_path}")
    print("=" * 60)

    analysis_results = {
        'directory_structure': {},
        'file_types': {},
        'total_files': 0,
        'audio_files': [],
        'text_files': [],
        'metadata_files': [],
        'other_files': [],
        'size_analysis': {},
        'content_samples': {}
    }

    # Walk through all directories and files
    for root, dirs, files in os.walk(audio_path):
        rel_path = os.path.relpath(root, audio_path)

        if rel_path == '.':
            rel_path = 'root'

        analysis_results['directory_structure'][rel_path] = {
            'subdirs': dirs,
            'files': files,
            'file_count': len(files)
        }

        for file in files:
            file_path = Path(root) / file
            file_ext = file_path.suffix.lower()
            file_size = file_path.stat().st_size if file_path.exists() else 0

            # Count file types
            if file_ext in analysis_results['file_types']:
                analysis_results['file_types'][file_ext] += 1
            else:
                analysis_results['file_types'][file_ext] = 1

            analysis_results['total_files'] += 1

            # Categorize files
            if file_ext in ['.mp3', '.wav', '.flac', '.m4a', '.ogg']:
                analysis_results['audio_files'].append({
                    'path': str(file_path),
                    'name': file,
                    'size': file_size,
                    'directory': rel_path
                })
            elif file_ext in ['.txt', '.csv', '.tsv', '.json']:
                analysis_results['text_files'].append({
                    'path': str(file_path),
                    'name': file,
                    'size': file_size,
                    'directory': rel_path
                })
            elif file_ext in ['.xml', '.json', '.yaml', '.yml']:
                analysis_results['metadata_files'].append({
                    'path': str(file_path),
                    'name': file,
                    'size': file_size,
                    'directory': rel_path
                })
            else:
                analysis_results['other_files'].append({
                    'path': str(file_path),
                    'name': file,
                    'size': file_size,
                    'directory': rel_path,
                    'extension': file_ext
                })

    return analysis_results

def analyze_text_files(analysis_results):
    """Analyze text files for content patterns"""

    print("\n📄 Analyzing text files...")

    for text_file in analysis_results['text_files'][:10]:  # Analyze first 10 text files
        try:
            file_path = Path(text_file['path'])
            print(f"\n📝 File: {text_file['name']} (Directory: {text_file['directory']})")

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Show first few lines
            lines = content.split('\n')[:5]
            print(f"   First 5 lines:")
            for i, line in enumerate(lines, 1):
                print(f"   {i}: {line[:100]}...")

            # Basic statistics
            print(f"   Total lines: {len(content.split(chr(10)))}")
            print(f"   Total characters: {len(content)}")
            print(f"   File size: {text_file['size']} bytes")

        except Exception as e:
            print(f"   Error reading {text_file['name']}: {e}")

    return analysis_results

def analyze_audio_files(analysis_results):
    """Analyze audio files if librosa is available"""

    print("\n🎵 Analyzing audio files...")

    if not AUDIO_ANALYSIS:
        print("   Librosa not available - showing basic file info only")
        for audio_file in analysis_results['audio_files'][:5]:
            print(f"   📁 {audio_file['name']} - Size: {audio_file['size']} bytes - Dir: {audio_file['directory']}")
        return analysis_results

    # Detailed audio analysis with librosa
    for audio_file in analysis_results['audio_files'][:3]:  # Analyze first 3 audio files
        try:
            file_path = audio_file['path']
            print(f"\n🎧 Analyzing: {audio_file['name']}")

            # Load audio
            y, sr = librosa.load(file_path, duration=10)  # Load first 10 seconds

            # Basic audio properties
            duration = len(y) / sr
            print(f"   Duration: {duration:.2f} seconds")
            print(f"   Sample rate: {sr} Hz")
            print(f"   Samples: {len(y)}")

            # Audio features
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)

            print(f"   MFCC shape: {mfccs.shape}")
            print(f"   Spectral centroid mean: {np.mean(spectral_centroids):.2f}")

        except Exception as e:
            print(f"   Error analyzing {audio_file['name']}: {e}")

    return analysis_results

def generate_framework_diagram(analysis_results):
    """Generate a comprehensive framework diagram of the dataset"""

    print("\n" + "="*80)
    print("🏗️  AUDIO DATASET FRAMEWORK DIAGRAM")
    print("="*80)

    # Directory structure
    print("\n📁 DIRECTORY STRUCTURE:")
    for dir_name, dir_info in analysis_results['directory_structure'].items():
        print(f"   📂 {dir_name}/")
        print(f"      ├── Subdirectories: {len(dir_info['subdirs'])}")
        print(f"      ├── Files: {dir_info['file_count']}")
        if dir_info['subdirs']:
            for subdir in dir_info['subdirs'][:3]:  # Show first 3 subdirs
                print(f"      │   └── {subdir}/")
            if len(dir_info['subdirs']) > 3:
                print(f"      │   └── ... and {len(dir_info['subdirs'])-3} more")

    # File type distribution
    print(f"\n📊 FILE TYPE DISTRIBUTION (Total: {analysis_results['total_files']} files):")
    for ext, count in sorted(analysis_results['file_types'].items(), key=lambda x: x[1], reverse=True):
        percentage = (count / analysis_results['total_files']) * 100
        print(f"   {ext or 'no extension'}: {count} files ({percentage:.1f}%)")

    # Audio files summary
    print(f"\n🎵 AUDIO FILES SUMMARY ({len(analysis_results['audio_files'])} files):")
    if analysis_results['audio_files']:
        total_audio_size = sum(f['size'] for f in analysis_results['audio_files'])
        print(f"   Total size: {total_audio_size / (1024*1024):.2f} MB")

        # Group by directory
        audio_by_dir = {}
        for audio_file in analysis_results['audio_files']:
            dir_name = audio_file['directory']
            if dir_name not in audio_by_dir:
                audio_by_dir[dir_name] = []
            audio_by_dir[dir_name].append(audio_file)

        for dir_name, files in audio_by_dir.items():
            print(f"   📂 {dir_name}: {len(files)} audio files")

    # Text files summary
    print(f"\n📄 TEXT FILES SUMMARY ({len(analysis_results['text_files'])} files):")
    if analysis_results['text_files']:
        text_by_dir = {}
        for text_file in analysis_results['text_files']:
            dir_name = text_file['directory']
            if dir_name not in text_by_dir:
                text_by_dir[dir_name] = []
            text_by_dir[dir_name].append(text_file)

        for dir_name, files in text_by_dir.items():
            print(f"   📂 {dir_name}: {len(files)} text files")

    # Other files
    if analysis_results['other_files']:
        print(f"\n📋 OTHER FILES ({len(analysis_results['other_files'])} files):")
        other_by_ext = {}
        for other_file in analysis_results['other_files']:
            ext = other_file['extension']
            if ext not in other_by_ext:
                other_by_ext[ext] = []
            other_by_ext[ext].append(other_file)

        for ext, files in other_by_ext.items():
            print(f"   {ext}: {len(files)} files")

def save_analysis_report(analysis_results):
    """Save detailed analysis to JSON file"""

    report_path = Path(r"D:\模型开发\audio_dataset_analysis_report.json")

    # Convert Path objects to strings for JSON serialization
    json_results = json.loads(json.dumps(analysis_results, default=str))

    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(json_results, f, indent=2, ensure_ascii=False)

    print(f"\n💾 Detailed analysis saved to: {report_path}")

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE AUDIO DATASET ANALYZER")
    print("=" * 60)

    # Suppress warnings
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

    # Run comprehensive analysis
    print("🚀 Starting comprehensive analysis...")
    analysis_results = analyze_audio_dataset()

    if analysis_results:
        # Analyze text files
        analysis_results = analyze_text_files(analysis_results)

        # Analyze audio files
        analysis_results = analyze_audio_files(analysis_results)

        # Generate framework diagram
        generate_framework_diagram(analysis_results)

        # Save detailed report
        save_analysis_report(analysis_results)

        print("\n✅ Analysis completed successfully!")
        print("📊 Framework diagram displayed above")
        print("💾 Detailed report saved to JSON file")
    else:
        print("❌ Analysis failed - please check the audio path exists")
