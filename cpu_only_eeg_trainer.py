"""
🧠 CPU专用EEG训练器
解决DNN库问题，强制使用CPU训练
"""

import os
import numpy as np
import pandas as pd

# 强制使用CPU，避免GPU/cuDNN问题
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'

import tensorflow as tf
from tensorflow.keras import layers, Model, optimizers, callbacks
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
import mne
import warnings
warnings.filterwarnings('ignore')

# 确保TensorFlow使用CPU
tf.config.set_visible_devices([], 'GPU')
print("🔧 强制使用CPU模式，避免cuDNN问题")

class CPUEEGTrainer:
    """CPU专用EEG训练器"""
    
    def __init__(self, data_path="/root/Scientific_EEG_Datasets"):
        self.data_path = data_path
        self.model = None
        self.label_encoder = LabelEncoder()
        
        # EEG参数
        self.n_channels = 19
        self.n_samples = 128
        self.n_classes = None
        
        print("🧠 CPU专用EEG训练器")
        print(f"📁 数据路径: {self.data_path}")
        print(f"💻 运行模式: CPU Only")
    
    def load_and_process_data(self, split_name):
        """加载并处理数据"""
        print(f"\n📂 加载{split_name}数据...")
        
        split_dir = os.path.join(self.data_path, split_name)
        labels_file = os.path.join(split_dir, 'labels.txt')
        
        if not os.path.exists(labels_file):
            raise FileNotFoundError(f"标签文件不存在: {labels_file}")
        
        # 读取标签
        labels_df = pd.read_csv(labels_file, sep='\t')
        print(f"📋 找到 {len(labels_df)} 个受试者")
        
        # 收集数据
        all_data = []
        all_labels = []
        
        for _, row in labels_df.iterrows():
            subject_id = row['subject_id']
            label = row['label']
            
            # 查找.set文件
            set_files = [f for f in os.listdir(split_dir) 
                        if f.startswith(subject_id) and f.endswith('.set')]
            
            if not set_files:
                continue
            
            set_file = os.path.join(split_dir, set_files[0])
            
            try:
                # 读取EEG数据
                raw = mne.io.read_raw_eeglab(set_file, preload=True, verbose=False)
                data = raw.get_data()
                
                # 创建epochs
                epochs = self.create_epochs(data)
                
                for epoch in epochs:
                    all_data.append(epoch)
                    all_labels.append(label)
                
                if len(epochs) > 0:
                    print(f"✅ {subject_id}: {len(epochs)} epochs")
                
            except Exception as e:
                print(f"❌ 处理{subject_id}失败: {e}")
                continue
        
        if len(all_data) == 0:
            raise ValueError("没有成功加载任何数据")
        
        X = np.array(all_data, dtype=np.float32)
        y = np.array(all_labels)
        
        print(f"📊 {split_name}数据: {X.shape}")
        print(f"📋 标签分布: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    
    def create_epochs(self, data):
        """创建标准化epochs"""
        n_channels, n_timepoints = data.shape
        n_epochs = n_timepoints // self.n_samples
        
        epochs = []
        for i in range(n_epochs):
            start_idx = i * self.n_samples
            end_idx = start_idx + self.n_samples
            
            epoch = data[:, start_idx:end_idx]
            
            # 调整通道数
            if epoch.shape[0] > self.n_channels:
                epoch = epoch[:self.n_channels, :]
            elif epoch.shape[0] < self.n_channels:
                padded_epoch = np.zeros((self.n_channels, self.n_samples))
                padded_epoch[:epoch.shape[0], :] = epoch
                epoch = padded_epoch
            
            epochs.append(epoch)
        
        return epochs
    
    def preprocess_data(self, X, y, fit_encoder=False):
        """预处理数据"""
        print("🔧 预处理数据...")
        
        # 标准化EEG数据
        X_processed = np.zeros_like(X)
        for i in range(X.shape[0]):
            for ch in range(X.shape[1]):
                channel_data = X[i, ch, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 1e-8:
                    X_processed[i, ch, :] = (channel_data - mean) / std
                else:
                    X_processed[i, ch, :] = channel_data
        
        # 编码标签
        if fit_encoder:
            y_encoded = self.label_encoder.fit_transform(y)
            self.n_classes = len(self.label_encoder.classes_)
            print(f"📋 标签编码: {dict(zip(self.label_encoder.classes_, range(self.n_classes)))}")
        else:
            y_encoded = self.label_encoder.transform(y)
        
        # 转换为one-hot编码
        y_categorical = tf.keras.utils.to_categorical(y_encoded, self.n_classes)
        
        # 添加通道维度 (batch, channels, time, 1)
        X_processed = np.expand_dims(X_processed, axis=-1)
        
        print(f"✅ 预处理完成: {X_processed.shape} -> {y_categorical.shape}")
        return X_processed, y_categorical
    
    def build_cpu_optimized_model(self):
        """构建CPU优化的模型"""
        print(f"🏗️ 构建CPU优化模型 (类别数: {self.n_classes})...")
        
        # 使用CPU设备
        with tf.device('/CPU:0'):
            # 输入层
            inputs = layers.Input(shape=(self.n_channels, self.n_samples, 1), name='eeg_input')
            
            # 简化的卷积层 - 减少参数量
            x = layers.Conv2D(16, (1, 5), padding='same', activation='relu', name='conv1')(inputs)
            x = layers.BatchNormalization(name='bn1')(x)
            x = layers.MaxPooling2D((1, 2), name='pool1')(x)
            x = layers.Dropout(0.2, name='dropout1')(x)
            
            # 空间卷积
            x = layers.Conv2D(16, (self.n_channels, 1), activation='relu', name='spatial_conv')(x)
            x = layers.BatchNormalization(name='bn2')(x)
            x = layers.Dropout(0.2, name='dropout2')(x)
            
            # 深层特征
            x = layers.Conv2D(32, (1, 5), padding='same', activation='relu', name='conv2')(x)
            x = layers.BatchNormalization(name='bn3')(x)
            x = layers.MaxPooling2D((1, 4), name='pool2')(x)
            x = layers.Dropout(0.3, name='dropout3')(x)
            
            # 全局池化
            x = layers.GlobalAveragePooling2D(name='global_pool')(x)
            
            # 分类头 - 简化
            x = layers.Dense(128, activation='relu', name='fc1')(x)
            x = layers.BatchNormalization(name='bn4')(x)
            x = layers.Dropout(0.4, name='dropout4')(x)
            
            x = layers.Dense(64, activation='relu', name='fc2')(x)
            x = layers.Dropout(0.3, name='dropout5')(x)
            
            # 输出层
            outputs = layers.Dense(self.n_classes, activation='softmax', name='predictions')(x)
            
            # 创建模型
            model = Model(inputs=inputs, outputs=outputs, name='CPUOptimizedEEGModel')
            
            # 编译模型
            model.compile(
                optimizer=optimizers.Adam(learning_rate=0.001),
                loss='categorical_crossentropy',
                metrics=['accuracy']
            )
        
        print(f"✅ CPU优化模型构建完成")
        print(f"📊 模型参数: {model.count_params():,}")
        
        return model
    
    def train_cpu_model(self, epochs=80, batch_size=16):
        """训练CPU模型"""
        print(f"🚀 开始CPU模型训练...")
        print(f"📊 训练参数: epochs={epochs}, batch_size={batch_size}")
        print("⏰ CPU训练较慢，请耐心等待...")
        
        # 加载所有数据
        X_train, y_train = self.load_and_process_data('train')
        X_val, y_val = self.load_and_process_data('val')
        
        # 预处理数据
        X_train, y_train = self.preprocess_data(X_train, y_train, fit_encoder=True)
        X_val, y_val = self.preprocess_data(X_val, y_val, fit_encoder=False)
        
        # 构建模型
        self.model = self.build_cpu_optimized_model()
        
        # 设置回调函数
        callback_list = [
            callbacks.EarlyStopping(
                monitor='val_accuracy',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=8,
                min_lr=1e-6,
                verbose=1
            ),
            callbacks.ModelCheckpoint(
                'cpu_eeg_model.h5',
                monitor='val_accuracy',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
        ]
        
        # 开始训练
        print("🎯 开始CPU训练...")
        history = self.model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callback_list,
            verbose=1
        )
        
        print("✅ CPU训练完成!")
        
        # 保存完整模型
        self.model.save('final_cpu_eeg_model.h5', save_format='h5')
        print("💾 CPU模型已保存: final_cpu_eeg_model.h5")
        
        # 保存标签编码器
        import joblib
        joblib.dump(self.label_encoder, 'cpu_eeg_label_encoder.pkl')
        print("💾 标签编码器已保存: cpu_eeg_label_encoder.pkl")
        
        return history
    
    def evaluate_cpu_model(self):
        """评估CPU模型"""
        print("\n📊 评估CPU模型...")
        
        # 加载测试数据
        X_test, y_test = self.load_and_process_data('test')
        X_test, y_test_cat = self.preprocess_data(X_test, y_test, fit_encoder=False)
        
        # 预测
        print("🔮 进行预测...")
        y_pred_prob = self.model.predict(X_test, verbose=0)
        y_pred = np.argmax(y_pred_prob, axis=1)
        y_true = np.argmax(y_test_cat, axis=1)
        
        # 计算指标
        test_loss, test_acc = self.model.evaluate(X_test, y_test_cat, verbose=0)
        
        print(f"📈 CPU模型测试结果:")
        print(f"   准确率: {test_acc:.4f}")
        print(f"   损失: {test_loss:.4f}")
        
        # 详细分类报告
        class_names = self.label_encoder.classes_
        report = classification_report(y_true, y_pred, target_names=class_names)
        print(f"\n📋 详细分类报告:")
        print(report)
        
        # 保存预测结果
        results = {
            'accuracy': test_acc,
            'loss': test_loss,
            'predictions': y_pred,
            'probabilities': y_pred_prob,
            'true_labels': y_true,
            'class_names': class_names
        }
        
        import joblib
        joblib.dump(results, 'cpu_model_results.pkl')
        print("💾 评估结果已保存: cpu_model_results.pkl")
        
        return results


def main():
    """主函数"""
    print("🧠 CPU专用EEG训练系统")
    print("🔧 解决cuDNN问题，使用CPU训练")
    print("=" * 60)
    
    # 创建训练器
    trainer = CPUEEGTrainer()
    
    # 训练CPU模型
    history = trainer.train_cpu_model(epochs=80, batch_size=16)
    
    # 评估模型
    results = trainer.evaluate_cpu_model()
    
    print(f"\n🎉 CPU训练完成!")
    print(f"🏆 最终准确率: {results['accuracy']:.4f}")
    print(f"\n📁 输出文件:")
    print(f"   🎯 final_cpu_eeg_model.h5 - CPU训练模型")
    print(f"   📊 cpu_model_results.pkl - 评估结果")
    print(f"   📋 cpu_eeg_label_encoder.pkl - 标签编码器")


if __name__ == "__main__":
    main()
