# 📍 AutoDL环境 EEG数据集划分器 - 配置说明

## 🔧 AutoDL环境路径配置

既然您已经在AutoDL环境中解压了EEG数据，请按以下方式配置：

### 1. 已解压数据路径配置

```python
# 在 autodl_eeg_data_splitter.py 第 18-19 行
self.extracted_data_path = "/root/autodl-tmp/EEG_extracted"  # 📍 修改为您的解压目录路径
self.dataset_root = os.path.join(self.extracted_data_path, "dataset")  # dataset子目录
```

**根据您的情况修改:**
- 如果您的解压目录是 `EEG_extracted`，请确认完整路径
- 常见的AutoDL路径格式：
  ```python
  self.extracted_data_path = "/root/autodl-tmp/EEG_extracted"
  # 或者
  self.extracted_data_path = "/root/autodl-fs/EEG_extracted"
  ```

### 2. 输出路径配置

```python
# 在 autodl_eeg_data_splitter.py 第 22 行
self.output_base_path = "/root/autodl-tmp/EEG_splits"  # 📍 修改为您希望保存划分结果的目录
```

**建议的AutoDL输出路径:**
```python
# 保存到临时目录 (重启后会丢失)
self.output_base_path = "/root/autodl-tmp/EEG_splits"

# 或保存到持久化目录 (推荐)
self.output_base_path = "/root/autodl-fs/EEG_splits"
```

## 📁 预期的AutoDL数据结构

您的解压数据应该具有以下结构：

```
/root/autodl-tmp/EEG_extracted/          # 您的解压目录
└── dataset/                             # 数据集根目录
    ├── participants.tsv                 # 患者标签文件
    ├── participants.json                # 标签定义文件
    ├── dataset_description.json         # 数据集描述
    ├── sub-001/                         # 患者目录
    │   └── eeg/
    │       └── sub-001_task-eyesclosed_eeg.set
    ├── sub-002/
    │   └── eeg/
    │       └── sub-002_task-eyesclosed_eeg.set
    ├── ...
    └── sub-088/
        └── eeg/
            └── sub-088_task-eyesclosed_eeg.set
```

## 🚀 AutoDL环境使用步骤

### 步骤1: 检查数据结构
```bash
# 在AutoDL终端中检查您的数据结构
ls -la /root/autodl-tmp/EEG_extracted/
ls -la /root/autodl-tmp/EEG_extracted/dataset/
```

### 步骤2: 修改脚本配置
根据您的实际路径修改 `autodl_eeg_data_splitter.py` 中的配置：

```python
# ==========================================
# 🔧 AutoDL环境配置 - 请根据您的实际路径修改
# ==========================================

# 1. 已解压的数据集路径配置
self.extracted_data_path = "您的实际解压路径"  # 📍 修改这里
self.dataset_root = os.path.join(self.extracted_data_path, "dataset")

# 2. 输出路径配置
self.output_base_path = "您的输出目录路径"  # 📍 修改这里
```

### 步骤3: 运行脚本
```bash
cd /root/autodl-tmp  # 或您的工作目录
python autodl_eeg_data_splitter.py
```

## 📊 AutoDL环境优势

1. **无需重新解压**: 直接使用已解压的数据
2. **高速处理**: AutoDL的SSD存储提供快速I/O
3. **内存充足**: 可以处理大量EEG文件
4. **GPU就绪**: 划分完成后可直接用于模型训练

## 📁 输出目录结构

运行完成后，在您指定的输出目录下会创建：

```
/root/autodl-tmp/EEG_splits/              # 您配置的输出目录
├── train/                                # 训练集目录
│   ├── sub-001_task-eyesclosed_eeg.set
│   ├── sub-002_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt                        # 训练集标签文件
├── val/                                  # 验证集目录
│   ├── sub-037_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt                        # 验证集标签文件
├── test/                                 # 测试集目录
│   ├── sub-066_task-eyesclosed_eeg.set
│   ├── ...
│   └── labels.txt                        # 测试集标签文件
├── split_metadata.json                   # 完整的划分元数据
└── split_summary.txt                     # 划分总结报告
```

## 🔍 AutoDL环境故障排除

### 问题1: 路径不存在
```
❌ 错误: 解压目录不存在
```
**解决方案**: 
```bash
# 检查实际路径
find /root -name "EEG_extracted" -type d 2>/dev/null
# 或
ls -la /root/autodl-tmp/
ls -la /root/autodl-fs/
```

### 问题2: dataset目录不存在
```
❌ 错误: dataset子目录不存在
```
**解决方案**:
```bash
# 检查解压结构
ls -la /root/autodl-tmp/EEG_extracted/
# 可能需要调整dataset_root路径
```

### 问题3: 磁盘空间不足
```bash
# 检查磁盘空间
df -h
# 清理临时文件
rm -rf /tmp/*
```

## 📋 快速配置模板

复制以下代码到 `autodl_eeg_data_splitter.py` 的配置部分：

```python
# ==========================================
# 🔧 AutoDL环境配置 - 请根据您的实际路径修改
# ==========================================

# 1. 已解压的数据集路径配置
self.extracted_data_path = "/root/autodl-tmp/EEG_extracted"  # 📍 修改为您的解压目录
self.dataset_root = os.path.join(self.extracted_data_path, "dataset")

# 2. 输出路径配置
self.output_base_path = "/root/autodl-tmp/EEG_splits"  # 📍 修改为您的输出目录

# 3. 划分比例配置 (针对双模型联用优化)
self.train_ratio = 0.70  # 训练集比例
self.val_ratio = 0.15    # 验证集比例
self.test_ratio = 0.15   # 测试集比例
```

## 🎯 下一步计划

数据划分完成后，您可以：

1. **验证划分结果**: 检查 `split_summary.txt`
2. **开始EEG模型训练**: 使用划分好的数据
3. **集成双模型系统**: 将EEG模型与现有MRI双模型联用
4. **部署到生产环境**: 将训练好的模型部署

---

💡 **AutoDL提示**: 
- 使用 `autodl-tmp` 目录进行临时处理
- 重要结果保存到 `autodl-fs` 目录以持久化
- 利用AutoDL的GPU资源进行后续模型训练
