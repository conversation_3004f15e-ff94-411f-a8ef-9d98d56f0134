"""
🧠 真实EEG数据训练器
直接处理.set文件，提取真实EEG特征，训练有意义的模型
"""

import os
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

import json
import pickle
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
from collections import Counter
warnings.filterwarnings('ignore')

try:
    import mne
    print("✅ MNE库可用，可以处理真实EEG数据")
except ImportError:
    print("❌ 需要安装MNE库: pip install mne")
    exit(1)

import tensorflow as tf
from tensorflow.keras import layers, models
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint

from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.utils.class_weight import compute_class_weight
from scipy import signal
from scipy.stats import entropy

print("🧠 真实EEG数据训练器")
print("💻 处理真实.set文件，提取真实EEG特征")
print("=" * 50)

class RealEEGTrainer:
    """真实EEG数据训练器"""
    
    def __init__(self):
        self.data_splits_path = "EEG_splits"
        self.model_save_path = "trained_eeg_models"
        
        # 根据您的实际需求调整
        self.n_classes = 2  # 如果是M/F分类
        self.class_names = ['女性(F)', '男性(M)']
        self.label_mapping = {'F': 0, 'M': 1}
        
        # 如果您想要痴呆分类，改为：
        # self.n_classes = 3
        # self.class_names = ['健康', '轻度痴呆', '重度痴呆']
        # self.label_mapping = {'HC': 0, 'MCI': 1, 'AD': 2}
        
        # 训练参数
        self.batch_size = 8
        self.epochs = 50
        self.learning_rate = 0.001
        self.feature_dim = 200  # 真实特征维度
        
        # EEG参数
        self.sfreq = 128  # 采样率
        self.n_channels = 19  # 通道数
        self.epoch_length = 2.0  # 每个epoch长度（秒）
        
        self.scaler = StandardScaler()
        self.model = None
        self.history = None
        
        os.makedirs(self.model_save_path, exist_ok=True)
        
        print(f"📊 真实EEG训练配置:")
        print(f"   批大小: {self.batch_size}")
        print(f"   训练轮次: {self.epochs}")
        print(f"   特征维度: {self.feature_dim}")
        print(f"   采样率: {self.sfreq} Hz")
        print(f"   通道数: {self.n_channels}")
    
    def load_eeg_file(self, file_path):
        """加载真实EEG .set文件"""
        try:
            # 使用MNE加载EEGLAB .set文件
            raw = mne.io.read_raw_eeglab(file_path, preload=True, verbose=False)
            
            # 基本预处理
            raw.filter(0.5, 50, verbose=False)  # 带通滤波
            raw.resample(self.sfreq, verbose=False)  # 重采样
            
            # 获取数据
            data = raw.get_data()  # shape: (n_channels, n_samples)
            
            print(f"   加载文件: {os.path.basename(file_path)}")
            print(f"   数据形状: {data.shape}")
            print(f"   采样率: {raw.info['sfreq']} Hz")
            
            return data
            
        except Exception as e:
            print(f"❌ 加载文件失败 {file_path}: {e}")
            return None
    
    def extract_real_features(self, eeg_data):
        """从真实EEG数据提取特征"""
        if eeg_data is None:
            return None
        
        features = []
        
        # 1. 频域特征 - 各频段功率
        freq_bands = {
            'delta': (0.5, 4),
            'theta': (4, 8), 
            'alpha': (8, 13),
            'beta': (13, 30),
            'gamma': (30, 50)
        }
        
        for band_name, (low, high) in freq_bands.items():
            band_power = []
            for ch in range(min(self.n_channels, eeg_data.shape[0])):
                # 计算功率谱密度
                freqs, psd = signal.welch(eeg_data[ch], fs=self.sfreq, nperseg=256)
                
                # 提取频段功率
                band_mask = (freqs >= low) & (freqs <= high)
                power = np.mean(psd[band_mask])
                band_power.append(power)
            
            # 统计特征
            features.extend([
                np.mean(band_power),
                np.std(band_power),
                np.max(band_power),
                np.min(band_power)
            ])
        
        # 2. 时域特征
        for ch in range(min(5, eeg_data.shape[0])):  # 前5个通道
            ch_data = eeg_data[ch]
            features.extend([
                np.mean(ch_data),
                np.std(ch_data),
                np.var(ch_data),
                np.max(ch_data) - np.min(ch_data)  # 峰峰值
            ])
        
        # 3. 复杂性特征
        for ch in range(min(3, eeg_data.shape[0])):  # 前3个通道
            ch_data = eeg_data[ch]
            
            # 样本熵（简化版）
            try:
                # 计算信号的复杂性
                diff_data = np.diff(ch_data)
                complexity = np.std(diff_data) / (np.std(ch_data) + 1e-8)
                features.append(complexity)
            except:
                features.append(0.0)
        
        # 4. 通道间相关性
        if eeg_data.shape[0] >= 2:
            correlations = []
            for i in range(min(3, eeg_data.shape[0])):
                for j in range(i+1, min(3, eeg_data.shape[0])):
                    corr = np.corrcoef(eeg_data[i], eeg_data[j])[0, 1]
                    if not np.isnan(corr):
                        correlations.append(abs(corr))
            
            if correlations:
                features.extend([
                    np.mean(correlations),
                    np.std(correlations),
                    np.max(correlations)
                ])
            else:
                features.extend([0.0, 0.0, 0.0])
        
        # 确保特征维度一致
        features = np.array(features[:self.feature_dim])
        if len(features) < self.feature_dim:
            # 用零填充
            features = np.pad(features, (0, self.feature_dim - len(features)))
        
        return features.astype(np.float32)
    
    def load_data(self):
        """加载所有真实EEG数据"""
        print("\n📋 加载真实EEG数据...")
        
        all_patients = []
        all_labels = []
        split_info = {}
        
        for split_name in ['train', 'val', 'test']:
            labels_file = os.path.join(self.data_splits_path, split_name, 'labels.txt')
            
            if not os.path.exists(labels_file):
                continue
            
            patients = []
            labels = []
            
            with open(labels_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()[1:]  # 跳过表头
            
            for line in lines:
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    subject_id = parts[0].strip()
                    label = parts[1].strip()
                    
                    if label in self.label_mapping:
                        patients.append(subject_id)
                        labels.append(label)
                        all_patients.append(subject_id)
                        all_labels.append(label)
            
            split_info[split_name] = {'patients': patients, 'labels': labels}
            label_counts = Counter(labels)
            print(f"   {split_name.upper()}集: {len(patients)} 个患者, 分布: {dict(label_counts)}")
        
        overall_counts = Counter(all_labels)
        print(f"\n📊 总体统计: {len(all_patients)} 个患者")
        print(f"   标签分布: {dict(overall_counts)}")
        
        return split_info
    
    def prepare_datasets(self, split_info):
        """准备真实EEG数据集"""
        print("\n🔧 准备真实EEG数据集...")
        
        datasets = {}
        
        for split_name, data in split_info.items():
            print(f"\n   处理{split_name}集: {len(data['patients'])}个样本")
            
            features = []
            labels = []
            
            for i, (subject_id, label) in enumerate(zip(data['patients'], data['labels'])):
                # 构建.set文件路径
                set_file = os.path.join(self.data_splits_path, split_name, 
                                       f"{subject_id}_task-eyesclosed_eeg.set")
                
                if os.path.exists(set_file):
                    print(f"     {i+1}/{len(data['patients'])}: {subject_id}")
                    
                    # 加载真实EEG数据
                    eeg_data = self.load_eeg_file(set_file)
                    
                    if eeg_data is not None:
                        # 提取真实特征
                        feature = self.extract_real_features(eeg_data)
                        
                        if feature is not None:
                            features.append(feature)
                            labels.append(self.label_mapping[label])
                        else:
                            print(f"     ⚠️ 特征提取失败: {subject_id}")
                    else:
                        print(f"     ⚠️ 数据加载失败: {subject_id}")
                else:
                    print(f"     ❌ 文件不存在: {set_file}")
            
            if len(features) > 0:
                datasets[split_name] = {
                    'features': np.array(features, dtype=np.float32),
                    'labels': np.array(labels, dtype=np.int32)
                }
                
                print(f"   ✅ {split_name.upper()}集完成: {len(features)} 个有效样本")
                print(f"   📏 特征形状: {datasets[split_name]['features'].shape}")
            else:
                print(f"   ❌ {split_name}集没有有效数据")
        
        return datasets

    def build_model(self):
        """构建真实EEG分类模型"""
        print("\n🏗️ 构建真实EEG分类模型...")

        model = models.Sequential([
            layers.Input(shape=(self.feature_dim,)),

            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),

            layers.Dense(64, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),

            layers.Dense(32, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.2),

            layers.Dense(self.n_classes, activation='softmax')
        ])

        optimizer = Adam(learning_rate=self.learning_rate)
        model.compile(
            optimizer=optimizer,
            loss='sparse_categorical_crossentropy',
            metrics=['accuracy']
        )

        self.model = model
        model.summary()

        return model

    def train_model(self, datasets):
        """训练真实EEG模型"""
        print("\n🚀 开始真实EEG模型训练...")

        if 'train' not in datasets:
            raise ValueError("没有训练数据！")

        X_train = datasets['train']['features']
        y_train = datasets['train']['labels']

        if 'val' in datasets:
            X_val = datasets['val']['features']
            y_val = datasets['val']['labels']
        else:
            # 如果没有验证集，从训练集分割
            from sklearn.model_selection import train_test_split
            X_train, X_val, y_train, y_val = train_test_split(
                X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
            )

        print(f"   训练集: {X_train.shape}")
        print(f"   验证集: {X_val.shape}")

        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)

        # 类别权重
        class_weights = compute_class_weight('balanced', classes=np.unique(y_train), y=y_train)
        class_weight_dict = dict(enumerate(class_weights))
        print(f"   类别权重: {class_weight_dict}")

        # 回调函数
        callbacks_list = [
            EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True, verbose=1),
            ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=5, min_lr=1e-6, verbose=1),
            ModelCheckpoint(
                filepath=os.path.join(self.model_save_path, 'best_real_eeg_model.h5'),
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]

        # 训练
        start_time = datetime.now()
        print(f"⏰ 开始时间: {start_time.strftime('%H:%M:%S')}")
        print("💻 训练真实EEG模型...")

        self.history = self.model.fit(
            X_train_scaled, y_train,
            validation_data=(X_val_scaled, y_val),
            epochs=self.epochs,
            batch_size=self.batch_size,
            class_weight=class_weight_dict,
            callbacks=callbacks_list,
            verbose=1
        )

        end_time = datetime.now()
        print(f"✅ 真实EEG模型训练完成! 用时: {end_time - start_time}")

        return self.history

    def evaluate_model(self, datasets):
        """评估模型"""
        print("\n📊 真实EEG模型评估...")

        results = {}

        for split_name in datasets.keys():
            X = datasets[split_name]['features']
            y_true = datasets[split_name]['labels']

            X_scaled = self.scaler.transform(X)
            y_pred_proba = self.model.predict(X_scaled, verbose=0)
            y_pred = np.argmax(y_pred_proba, axis=1)

            accuracy = accuracy_score(y_true, y_pred)
            print(f"\n{split_name.upper()}集结果:")
            print(f"   准确率: {accuracy:.4f}")

            report = classification_report(y_true, y_pred, target_names=self.class_names, output_dict=True)
            print(classification_report(y_true, y_pred, target_names=self.class_names))

            cm = confusion_matrix(y_true, y_pred)
            print(f"   混淆矩阵:\n{cm}")

            results[split_name] = {
                'accuracy': accuracy,
                'y_true': y_true,
                'y_pred': y_pred,
                'classification_report': report,
                'confusion_matrix': cm
            }

        return results

    def save_model(self, results):
        """保存真实EEG模型"""
        print("\n💾 保存真实EEG模型...")

        # 保存模型
        model_file = os.path.join(self.model_save_path, 'real_eeg_classifier.h5')
        self.model.save(model_file)

        # 保存预处理器
        scaler_file = os.path.join(self.model_save_path, 'real_eeg_scaler.pkl')
        with open(scaler_file, 'wb') as f:
            pickle.dump(self.scaler, f)

        # 保存元数据
        metadata = {
            'model_info': {
                'name': 'Real EEG Classifier',
                'n_classes': self.n_classes,
                'class_names': self.class_names,
                'label_mapping': self.label_mapping,
                'feature_dim': self.feature_dim,
                'data_source': 'Real EEG .set files'
            },
            'training_params': {
                'batch_size': self.batch_size,
                'epochs': self.epochs,
                'learning_rate': self.learning_rate,
                'sfreq': self.sfreq,
                'n_channels': self.n_channels
            },
            'performance': {
                split: {
                    'accuracy': float(results[split]['accuracy']),
                    'classification_report': results[split]['classification_report']
                }
                for split in results.keys()
            },
            'timestamp': datetime.now().isoformat()
        }

        metadata_file = os.path.join(self.model_save_path, 'real_eeg_metadata.json')
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)

        print(f"✅ 真实EEG模型已保存:")
        print(f"   - 模型: {model_file}")
        print(f"   - 预处理器: {scaler_file}")
        print(f"   - 元数据: {metadata_file}")

        return model_file

    def run_training(self):
        """运行真实EEG训练流程"""
        print("🚀 开始真实EEG数据训练")
        print("=" * 50)

        try:
            # 1. 加载真实数据
            split_info = self.load_data()

            # 2. 准备真实数据集
            datasets = self.prepare_datasets(split_info)

            if not datasets:
                raise ValueError("没有可用的真实EEG数据！")

            # 3. 构建模型
            self.build_model()

            # 4. 训练模型
            self.train_model(datasets)

            # 5. 评估模型
            results = self.evaluate_model(datasets)

            # 6. 保存模型
            model_file = self.save_model(results)

            print(f"\n🎉 真实EEG模型训练完成!")
            print("=" * 50)
            print(f"📊 最终性能:")
            for split in results.keys():
                acc = results[split]['accuracy']
                print(f"   {split.upper()}集准确率: {acc:.4f}")

            print(f"\n🔗 使用示例:")
            print(f"   from tensorflow.keras.models import load_model")
            print(f"   import pickle")
            print(f"   ")
            print(f"   # 加载真实EEG模型")
            print(f"   model = load_model('trained_eeg_models/real_eeg_classifier.h5')")
            print(f"   with open('trained_eeg_models/real_eeg_scaler.pkl', 'rb') as f:")
            print(f"       scaler = pickle.load(f)")

            return True

        except Exception as e:
            print(f"❌ 真实EEG训练失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🧠 真实EEG数据训练系统")
    print("💻 处理真实.set文件，提取真实EEG特征")
    print()

    # 创建训练器
    trainer = RealEEGTrainer()

    # 运行训练
    success = trainer.run_training()

    if success:
        print("\n🏆 真实EEG模型训练成功!")
        print("📋 基于真实EEG数据的有意义模型已完成")
    else:
        print("\n❌ 训练失败")


if __name__ == "__main__":
    main()
